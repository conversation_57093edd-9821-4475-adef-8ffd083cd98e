{"name": "core-ui-kendo", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --port 4001 --proxy-config proxy-config.json", "build": "ng build", "watch": "ng build --watch --configuration development", "lint": "ng lint", "lint-staged": "lint-staged", "format": "prettier --write \"src/**/*.{ts,js,html,scss}\"", "prepare": "cd .. && husky 'core-ui-kendo/.husky'", "build:light-theme": "sass --load-path=node_modules ./src/local_assets/themes/light-theme/index.scss ./public/themes/light-theme.css --style compressed", "build:dark-theme": "sass --load-path=node_modules ./src/local_assets/themes/dark-theme/index.scss ./public/themes/dark-theme.css --style compressed", "postinstall": "npm run build:light-theme && npm run build:dark-theme"}, "lint-staged": {"**/*.{js,ts,html,scss}": ["prettier --write"]}, "private": true, "dependencies": {"@angular/animations": "^20.0.2", "@angular/common": "^20.0.2", "@angular/compiler": "^20.0.2", "@angular/core": "^20.0.2", "@angular/forms": "^20.0.2", "@angular/localize": "^20.0.2", "@angular/platform-browser": "^20.0.2", "@angular/platform-browser-dynamic": "^20.0.2", "@angular/router": "^20.0.2", "@progress/kendo-angular-buttons": "19.3.0", "@progress/kendo-angular-common": "19.3.0", "@progress/kendo-angular-dateinputs": "19.3.0", "@progress/kendo-angular-dialog": "19.3.0", "@progress/kendo-angular-dropdowns": "19.3.0", "@progress/kendo-angular-editor": "19.3.0", "@progress/kendo-angular-excel-export": "19.3.0", "@progress/kendo-angular-filter": "^19.3.0", "@progress/kendo-angular-grid": "19.3.0", "@progress/kendo-angular-icons": "19.3.0", "@progress/kendo-angular-menu": "19.3.0", "@progress/kendo-angular-indicators": "19.3.0", "@progress/kendo-angular-inputs": "19.3.0", "@progress/kendo-angular-intl": "19.3.0", "@progress/kendo-angular-l10n": "19.3.0", "@progress/kendo-angular-label": "19.3.0", "@progress/kendo-angular-layout": "19.3.0", "@progress/kendo-angular-navigation": "19.3.0", "@progress/kendo-angular-notification": "19.3.0", "@progress/kendo-angular-pdf-export": "19.3.0", "@progress/kendo-angular-popup": "19.3.0", "@progress/kendo-angular-progressbar": "19.3.0", "@progress/kendo-angular-toolbar": "19.3.0", "@progress/kendo-angular-tooltip": "19.3.0", "@progress/kendo-angular-treeview": "19.3.0", "@progress/kendo-angular-upload": "19.3.0", "@progress/kendo-angular-utils": "19.3.0", "@progress/kendo-data-query": "^1.7.1", "@progress/kendo-drawing": "^1.21.2", "@progress/kendo-file-saver": "1.1.2", "@progress/kendo-licensing": "^1.7.1", "@progress/kendo-svg-icons": "^4.5.0", "@progress/kendo-theme-default": "^11.3.2", "@zxing/library": "^0.21.3", "chart.js": "4.4.9", "cronstrue": "^2.56.0", "gridstack": "^11.2.0", "leaflet": "1.9.4", "luxon": "3.6.1", "ngx-webcam": "^0.4.1", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-eslint/schematics": "^19.3.0", "@angular/build": "^20.0.1", "@angular/cli": "^20.0.1", "@angular/compiler-cli": "^20.0.2", "@types/leaflet": "1.9.14", "@types/luxon": "^3.4.2", "angular-eslint": "20.0.0", "eslint": "9.28.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-prettier": "5.4.1", "husky": "^9.0.11", "lint-staged": "^15.2.7", "postcss": "^8.4.38", "prettier": "3.5.3", "prettier-eslint": "16.4.2", "typescript": "~5.8.3", "typescript-eslint": "^8.0.0"}}