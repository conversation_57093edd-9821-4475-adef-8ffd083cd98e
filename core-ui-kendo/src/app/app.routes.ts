// Angular
import { Routes } from '@angular/router';
// Components
import { DashboardComponent } from './main/views/dashboard/dashboard.component';
import { MapComponent } from './main/views/map/map.component';
import { ButtonComponent } from './main/views/button/button.component';
import { CardComponent } from './main/views/card/card.component';
import { ControlsComponent } from './main/views/controls/controls.component';
import { ControlOverviewComponent } from './main/views/controls/views/control-overview/control-overview.component';
import { ControlTextViewComponent } from './main/views/controls/views/control-text-view/control-text-view.component';
import { ControlTextExamplesComponent } from './main/views/controls/views/control-text-view/views/control-text-examples/control-text-examples.component';
import { ControlTextApiComponent } from './main/views/controls/views/control-text-view/views/control-text-api/control-text-api.component';
import { ControlTextareaViewComponent } from './main/views/controls/views/control-textarea-view/control-textarea-view.component';
import { ControlTextareaExamplesComponent } from './main/views/controls/views/control-textarea-view/views/control-textarea-examples/control-textarea-examples.component';
import { ControlTextareaApiComponent } from './main/views/controls/views/control-textarea-view/views/control-textarea-api/control-textarea-api.component';
import { ControlNumberViewComponent } from './main/views/controls/views/control-number-view/control-number-view.component';
import { ControlNumberExamplesComponent } from './main/views/controls/views/control-number-view/views/control-number-examples/control-number-examples.component';
import { ControlNumberApiComponent } from './main/views/controls/views/control-number-view/views/control-number-api/control-number-api.component';
import { ControlPasswordViewComponent } from './main/views/controls/views/control-password-view/control-password-view.component';
import { ControlPasswordExamplesComponent } from './main/views/controls/views/control-password-view/views/control-password-examples/control-password-examples.component';
import { ControlPasswordApiComponent } from './main/views/controls/views/control-password-view/views/control-password-api/control-password-api.component';
import { ControlDateViewComponent } from './main/views/controls/views/control-date-view/control-date-view.component';
import { ControlDateExamplesComponent } from './main/views/controls/views/control-date-view/views/control-date-examples/control-date-examples.component';
import { ControlDateApiComponent } from './main/views/controls/views/control-date-view/views/control-date-api/control-date-api.component';
import { ControlSelectViewComponent } from './main/views/controls/views/control-select-view/control-select-view.component';
import { ControlSelectExamplesComponent } from './main/views/controls/views/control-select-view/views/control-select-examples/control-select-examples.component';
import { ControlSelectApiComponent } from './main/views/controls/views/control-select-view/views/control-select-api/control-select-api.component';
import { ControlMultiselectViewComponent } from './main/views/controls/views/control-multiselect-view/control-multiselect-view.component';
import { ControlMultiselectExamplesComponent } from './main/views/controls/views/control-multiselect-view/views/control-multiselect-examples/control-multiselect-examples.component';
import { ControlMultiselectApiComponent } from './main/views/controls/views/control-multiselect-view/views/control-multiselect-api/control-multiselect-api.component';
import { ControlBooleanViewComponent } from './main/views/controls/views/control-boolean-view/control-boolean-view.component';
import { ControlBooleanExamplesComponent } from './main/views/controls/views/control-boolean-view/views/control-boolean-examples/control-boolean-examples.component';
import { ControlBooleanApiComponent } from './main/views/controls/views/control-boolean-view/views/control-boolean-api/control-boolean-api.component';
import { ControlEmailViewComponent } from './main/views/controls/views/control-email-view/control-email-view.component';
import { ControlEmailExamplesComponent } from './main/views/controls/views/control-email-view/views/control-email-examples/control-email-examples.component';
import { ControlEmailApiComponent } from './main/views/controls/views/control-email-view/views/control-email-api/control-email-api.component';
import { ControlMaskedTextViewComponent } from './main/views/controls/views/control-masked-text-view/control-masked-text-view.component';
import { ControlMaskedTextApiComponent } from './main/views/controls/views/control-masked-text-view/views/control-masked-text-api/control-masked-text-api.component';
import { ControlMaskedTextExamplesComponent } from './main/views/controls/views/control-masked-text-view/views/control-masked-text-examples/control-masked-text-examples.component';
import { ControlDateTimeViewComponent } from './main/views/controls/views/control-date-time-view/control-date-time-view.component';
import { ControlDateTimeExamplesComponent } from './main/views/controls/views/control-date-time-view/views/control-date-time-examples/control-date-time-examples.component';
import { ControlDateTimeApiComponent } from './main/views/controls/views/control-date-time-view/views/control-date-time-api/control-date-time-api.component';
import { ControlFileSelectViewComponent } from './main/views/controls/views/control-file-select-view/control-file-select-view.component';
import { ControlFileSelectExamplesComponent } from './main/views/controls/views/control-file-select-view/views/control-file-select-examples/control-file-select-examples.component';
import { ControlFileSelectApiComponent } from './main/views/controls/views/control-file-select-view/views/control-file-select-api/control-file-select-api.component';
import { ControlColorPickerViewComponent } from './main/views/controls/views/control-color-picker-view/control-color-picker-view.component';
import { ControlColorPickerExamplesComponent } from './main/views/controls/views/control-color-picker-view/views/control-color-picker-examples/control-color-picker-examples.component';
import { ControlColorPickerApiComponent } from './main/views/controls/views/control-color-picker-view/views/control-color-picker-api/control-color-picker-api.component';
import { ControlDropDownViewComponent } from './main/views/controls/views/control-drop-down-view/control-drop-down-view.component';
import { ControlDropDownExamplesComponent } from './main/views/controls/views/control-drop-down-view/views/control-drop-down-examples/control-drop-down-examples.component';
import { ControlDropDownApiComponent } from './main/views/controls/views/control-drop-down-view/views/control-drop-down-api/control-drop-down-api.component';
import { ControlEditorViewComponent } from './main/views/controls/views/control-editor-view/control-editor-view.component';
import { ControlEditorExamplesComponent } from './main/views/controls/views/control-editor-view/views/control-editor-examples/control-editor-examples.component';
import { ControlEditorApiComponent } from './main/views/controls/views/control-editor-view/views/control-editor-api/control-editor-api.component';
import { ControlTimeViewComponent } from './main/views/controls/views/control-time-view/control-time-view.component';
import { ControlTimeExamplesComponent } from './main/views/controls/views/control-time-view/views/control-time-examples/control-time-examples.component';
import { ControlTimeApiComponent } from './main/views/controls/views/control-time-view/views/control-time-api/control-time-api.component';
import { ControlCronExamplesComponent } from './main/views/controls/views/control-cron-view/views/control-cron-examples/control-cron-examples.component';
import { ControlCronApiComponent } from './main/views/controls/views/control-cron-view/views/control-cron-api/control-cron-api.component';
import { ControlRadioSelectExamplesComponent } from './main/views/controls/views/control-radio-select/views/control-radio-select-examples/control-radio-select-examples.component';
import { ControlRadioSelectApiComponent } from './main/views/controls/views/control-radio-select/views/control-radio-select-api/control-radio-select-api.component';
import { ControlOtpViewComponent } from './main/views/controls/views/control-otp-view/control-otp-view.component';
import { ControlOtpExamplesComponent } from './main/views/controls/views/control-otp-view/views/control-otp-examples/control-otp-examples.component';
import { ControlOtpApiComponent } from './main/views/controls/views/control-otp-view/views/control-otp-api/control-otp-api.component';
import { ControlSliderViewComponent } from './main/views/controls/views/control-slider-view/control-slider-view.component';
import { ControlSliderExamplesComponent } from './main/views/controls/views/control-slider-view/views/control-slider-examples/control-slider-examples.component';
import { ControlSliderApiComponent } from './main/views/controls/views/control-slider-view/views/control-slider-api/control-slider-api.component';
import { DropdownButtonsComponent } from './main/views/dropdown-buttons/dropdown-buttons.component';
import { FormComponent } from './main/views/form/form.component';
import { FormExamplesComponent } from './main/views/form/views/form-examples/form-examples.component';
import { FormApiComponent } from './main/views/form/views/form-api/form-api.component';
import { FileUploadButtonViewComponent } from './main/views/general/views/file-upload-button-view/file-upload-button-view.component';
import { FileUploadButtonExamplesComponent } from './main/views/general/views/file-upload-button-view/views/file-upload-button-examples/file-upload-button-examples.component';
import { FileUploadButtonApiComponent } from './main/views/general/views/file-upload-button-view/views/file-upload-button-api/file-upload-button-api.component';
import { GeneralComponent } from './main/views/general/general.component';
import { MainComponent } from './main/main.component';
import { IconsComponent } from './main/views/icons/icons.component';
import { PopupsComponent } from './main/views/popups/popups.component';
import { PopupsNotificationsComponent } from './main/views/popups/views/popups-notifications/popups-notifications.component';
import { PopupsNotificationsExamplesComponent } from './main/views/popups/views/popups-notifications/views/popups-notifications-examples/popups-notifications-examples.component';
import { PopupsNotificationsApiComponent } from './main/views/popups/views/popups-notifications/views/popups-notifications-api/popups-notifications-api.component';
import { PopupsFormDialogComponent } from './main/views/popups/views/popups-form-dialog/popups-form-dialog.component';
import { PopupsFormDialogExamplesComponent } from './main/views/popups/views/popups-form-dialog/views/popups-form-dialog-examples/popups-form-dialog-examples.component';
import { PopupsFormDialogApiComponent } from './main/views/popups/views/popups-form-dialog/views/popups-form-dialog-api/popups-form-dialog-api.component';
import { PopupsConfirmationDialogComponent } from './main/views/popups/views/popups-confirmation-dialog/popups-confirmation-dialog.component';
import { PopupsConfirmationExamplesComponent } from './main/views/popups/views/popups-confirmation-dialog/views/popups-confirmation-examples/popups-confirmation-examples.component';
import { PopupsConfirmationDialogApiComponent } from './main/views/popups/views/popups-confirmation-dialog/views/popups-confirmation-dialog-api/popups-confirmation-dialog-api.component';
import { TablesComponent } from './main/views/tables/tables.component';
import { ToolbarViewComponent } from './main/views/toolbar/toolbar.component';
import { DialogTableComponent } from './main/views/popups/views/dialog-table/dialog-table.component';
import { DialogTableExamplesComponent } from './main/views/popups/views/dialog-table/views/dialog-table-examples/dialog-table-examples.component';
import { DialogTableApiComponent } from './main/views/popups/views/dialog-table/views/dialog-table-api/dialog-table-api.component';
import { TableExample1Component } from './main/views/tables/views/table-example-1/table-example-1.component';
import { TableExample2Component } from './main/views/tables/views/table-example-2/table-example-2.component';
import { TableExample3Component } from './main/views/tables/views/table-example-3/table-example-3.component';
import { TableApiComponent } from './main/views/tables/views/table-api/table-api.component';
import { ToolbarExamplesComponent } from './main/views/toolbar/views/toolbar-examples/toolbar-examples.component';
import { ToolbarApiComponent } from './main/views/toolbar/views/toolbar-api/toolbar-api.component';
import { PlaceholderComponent } from './main/components/placeholder/placeholder.component';
import { ButtonExamplesComponent } from './main/views/button/button-examples/button-examples.component';
import { ButtonApiComponent } from './main/views/button/button-api/button-api.component';
import { DropdownButtonExamplesComponent } from './main/views/dropdown-buttons/views/dropdown-button-examples/dropdown-button-examples.component';
import { DropdownButtonApiComponent } from './main/views/dropdown-buttons/views/dropdown-button-api/dropdown-button-api.component';
import { IconExamplesComponent } from './main/views/icons/views/icon-examples/icon-examples.component';
import { IconApiComponent } from './main/views/icons/views/icon-api/icon-api.component';
import { SvgIconsComponent } from './main/views/icons/views/svg-icons/svg-icons.component';
import { CardExamplesComponent } from './main/views/card/views/card-examples/card-examples.component';
import { CardApiComponent } from './main/views/card/views/card-api/card-api.component';
import { ControlCronViewComponent } from './main/views/controls/views/control-cron-view/control-cron-view.component';
import { SchedulerComponent } from './main/views/scheduler/scheduler.component';
import { SchedulerExamplesComponent } from './main/views/scheduler/views/scheduler-examples/scheduler-examples.component';
import { SchedulerApiComponent } from './main/views/scheduler/views/scheduler-api/scheduler-api.component';
import { ControlRadioSelectComponent } from './main/views/controls/views/control-radio-select/control-radio-select.component';
import { DocumentationComponent } from './main/views/documentation/documentation.component';
import { DocumentationExamplesComponent } from './main/views/documentation/documentation-examples/documentation-examples.component';
import { DocumentationApiComponent } from './main/views/documentation/documentation-api/documentation-api.component';
import { PixelEditorComponent } from './main/views/pixel-editor/pixel-editor.component';
import { PixelEditorExamplesComponent } from './main/views/pixel-editor/views/pixel-editor-examples/pixel-editor-examples.component';
import { PixelEditorApiComponent } from './main/views/pixel-editor/views/pixel-editor-api/pixel-editor-api.component';
import { TableExample4Component } from './main/views/tables/views/table-example-4/table-example-4.component';
import { BackgroundsComponent } from './main/views/general/views/backgrounds/backgrounds.component';
import { BackgroundSquaresComponent } from './main/views/general/views/backgrounds/views/background-squares/background-squares.component';
import { FormExample2Component } from './main/views/form/views/form-example-2/form-example-2.component';
import { TableExample5Component } from './main/views/tables/views/table-example-5/table-example-5.component';
import { TableResponsiveComponent } from './main/views/tables/views/table-responsive/table-responsive.component';
import { ProgressBarComponent } from './main/views/progress-bar/progress-bar.component';
import { ProgressBarExamples } from './main/views/progress-bar/views/progress-bar-examples/progress-bar-examples';
import { ProgressBarApi } from './main/views/progress-bar/views/progress-bar-api/progress-bar-api';
import { TreeViewExample } from './main/views/tree-view/views/tree-view-example/tree-view-example';
import { TreeViewApi } from './main/views/tree-view/views/tree-view-api/tree-view-api';
import { TreeView } from './main/views/tree-view/tree-view';
import { BreadcrumbsComponent } from './main/views/breadcrumbs/breadcrumbs';
import { BreadcrumbsExample1Component } from './main/views/breadcrumbs/views/breadcrumbs-example-1/breadcrumbs-example-1';
import { BreadcrumbsApiComponent } from './main/views/breadcrumbs/views/breadcrumbs-api/breadcrumbs-api';
import { BcE1Settings } from './main/views/breadcrumbs/views/breadcrumbs-example-1/views/bc-e1-settings/bc-e1-settings';
import { BcE1PersonalSettings } from './main/views/breadcrumbs/views/breadcrumbs-example-1/views/bc-e1-settings/views/bc-e1-personal-settings/bc-e1-personal-settings';
import { BcE1SystemSettings } from './main/views/breadcrumbs/views/breadcrumbs-example-1/views/bc-e1-settings/views/bc-e1-system-settings/bc-e1-system-settings';
import { BcE1Dashboard } from './main/views/breadcrumbs/views/breadcrumbs-example-1/views/bc-e1-dashboard/bc-e1-dashboard';
// Constants
import { BC_E1_SETTINGS_BREADCRUMB_DATA } from './main/views/breadcrumbs/views/breadcrumbs-example-1/views/bc-e1-settings/bc-e1-settings.constants';
import { BC_E1_PERSONAL_SETTINGS_BREADCRUMB_DATA } from './main/views/breadcrumbs/views/breadcrumbs-example-1/views/bc-e1-settings/views/bc-e1-personal-settings/bc-e1-personal-settings.constants';
import { BC_E1_SYSTEM_SETTINGS_BREADCRUMB_DATA } from './main/views/breadcrumbs/views/breadcrumbs-example-1/views/bc-e1-settings/views/bc-e1-system-settings/bc-e1-system-settings.constants';
import { NetworkDiagramComponent } from './main/views/network-diagram/network-diagram';
import { NetworkDiagramExample1Component } from './main/views/network-diagram/views/network-diagram-example-1/network-diagram-example-1';
import { NetworkDiagramApiComponent } from './main/views/network-diagram/views/network-diagram-api/network-diagram-api';
import { NetworkDiagramExample2Component } from './main/views/network-diagram/views/network-diagram-example-2/network-diagram-example-2';

export const routes: Routes = [
  {
    path: '',
    component: MainComponent,
    children: [
      {
        path: 'buttons',
        component: ButtonComponent,
        children: [
          { path: 'button-examples', component: ButtonExamplesComponent },
          { path: 'button-api', component: ButtonApiComponent },
        ],
      },
      {
        path: 'dropdown-buttons',
        component: DropdownButtonsComponent,
        children: [
          { path: 'dropdown-button-examples', component: DropdownButtonExamplesComponent },
          { path: 'dropdown-button-api', component: DropdownButtonApiComponent },
        ],
      },
      {
        path: 'card',
        component: CardComponent,
        children: [
          { path: 'card-examples', component: CardExamplesComponent },
          { path: 'card-api', component: CardApiComponent },
        ],
      },
      {
        path: 'controls',
        component: ControlsComponent,
        children: [
          { path: 'control-overview', component: ControlOverviewComponent },
          {
            path: 'control-text-view',
            component: ControlTextViewComponent,
            children: [
              { path: 'text-examples', component: ControlTextExamplesComponent },
              { path: 'text-api', component: ControlTextApiComponent },
            ],
          },
          {
            path: 'control-masked-text-view',
            component: ControlMaskedTextViewComponent,
            children: [
              { path: 'masked-text-examples', component: ControlMaskedTextExamplesComponent },
              { path: 'masked-text-api', component: ControlMaskedTextApiComponent },
            ],
          },
          {
            path: 'control-textarea-view',
            component: ControlTextareaViewComponent,
            children: [
              { path: 'text-area-examples', component: ControlTextareaExamplesComponent },
              { path: 'text-area-api', component: ControlTextareaApiComponent },
            ],
          },
          {
            path: 'control-number-view',
            component: ControlNumberViewComponent,
            children: [
              { path: 'number-examples', component: ControlNumberExamplesComponent },
              { path: 'number-api', component: ControlNumberApiComponent },
            ],
          },
          {
            path: 'control-password-view',
            component: ControlPasswordViewComponent,
            children: [
              { path: 'password-examples', component: ControlPasswordExamplesComponent },
              { path: 'password-api', component: ControlPasswordApiComponent },
            ],
          },
          {
            path: 'control-otp-view',
            component: ControlOtpViewComponent,
            children: [
              { path: 'otp-examples', component: ControlOtpExamplesComponent },
              { path: 'otp-api', component: ControlOtpApiComponent },
            ],
          },
          {
            path: 'control-date-view',
            component: ControlDateViewComponent,
            children: [
              { path: 'date-examples', component: ControlDateExamplesComponent },
              { path: 'date-api', component: ControlDateApiComponent },
            ],
          },
          {
            path: 'control-date-time-view',
            component: ControlDateTimeViewComponent,
            children: [
              { path: 'date-time-examples', component: ControlDateTimeExamplesComponent },
              { path: 'date-time-api', component: ControlDateTimeApiComponent },
            ],
          },
          {
            path: 'control-time-view',
            component: ControlTimeViewComponent,
            children: [
              { path: 'time-examples', component: ControlTimeExamplesComponent },
              { path: 'time-api', component: ControlTimeApiComponent },
            ],
          },
          {
            path: 'control-select-view',
            component: ControlSelectViewComponent,
            children: [
              { path: 'select-examples', component: ControlSelectExamplesComponent },
              { path: 'select-api', component: ControlSelectApiComponent },
            ],
          },
          {
            path: 'control-radio-select-view',
            component: ControlRadioSelectComponent,
            children: [
              { path: 'radio-select-examples', component: ControlRadioSelectExamplesComponent },
              { path: 'radio-select-api', component: ControlRadioSelectApiComponent },
            ],
          },
          {
            path: 'control-multiselect-view',
            component: ControlMultiselectViewComponent,
            children: [
              { path: 'multiselect-examples', component: ControlMultiselectExamplesComponent },
              { path: 'multiselect-api', component: ControlMultiselectApiComponent },
            ],
          },
          {
            path: 'control-drop-down-view',
            component: ControlDropDownViewComponent,
            children: [
              { path: 'drop-down-examples', component: ControlDropDownExamplesComponent },
              { path: 'drop-down-api', component: ControlDropDownApiComponent },
            ],
          },
          {
            path: 'control-boolean-view',
            component: ControlBooleanViewComponent,
            children: [
              { path: 'boolean-examples', component: ControlBooleanExamplesComponent },
              { path: 'boolean-api', component: ControlBooleanApiComponent },
            ],
          },
          {
            path: 'control-email-view',
            component: ControlEmailViewComponent,
            children: [
              { path: 'email-examples', component: ControlEmailExamplesComponent },
              { path: 'email-api', component: ControlEmailApiComponent },
            ],
          },
          {
            path: 'control-file-select-view',
            component: ControlFileSelectViewComponent,
            children: [
              { path: 'file-select-examples', component: ControlFileSelectExamplesComponent },
              { path: 'file-select-api', component: ControlFileSelectApiComponent },
            ],
          },
          {
            path: 'control-color-picker-view',
            component: ControlColorPickerViewComponent,
            children: [
              { path: 'color-picker-examples', component: ControlColorPickerExamplesComponent },
              { path: 'color-picker-api', component: ControlColorPickerApiComponent },
            ],
          },
          {
            path: 'control-editor-view',
            component: ControlEditorViewComponent,
            children: [
              { path: 'editor-examples', component: ControlEditorExamplesComponent },
              { path: 'editor-api', component: ControlEditorApiComponent },
            ],
          },
          {
            path: 'control-slider-view',
            component: ControlSliderViewComponent,
            children: [
              { path: 'slider-examples', component: ControlSliderExamplesComponent },
              { path: 'slider-api', component: ControlSliderApiComponent },
            ],
          },
          {
            path: 'control-cron-view',
            component: ControlCronViewComponent,
            children: [
              { path: 'cron-examples', component: ControlCronExamplesComponent },
              { path: 'cron-api', component: ControlCronApiComponent },
            ],
          },
        ],
      },
      {
        path: 'icons',
        component: IconsComponent,
        children: [
          { path: 'icon-examples', component: IconExamplesComponent },
          { path: 'svg-icons', component: SvgIconsComponent },
          { path: 'icon-api', component: IconApiComponent },
        ],
      },
      {
        path: 'progress-bar',
        component: ProgressBarComponent,
        children: [
          { path: 'progress-bar-examples', component: ProgressBarExamples },
          { path: 'progress-bar-api', component: ProgressBarApi },
        ],
      },
      {
        path: 'toolbar',
        component: ToolbarViewComponent,
        children: [
          { path: 'toolbar-examples', component: ToolbarExamplesComponent },
          { path: 'toolbar-api', component: ToolbarApiComponent },
        ],
      },
      {
        path: 'breadcrumbs',
        component: BreadcrumbsComponent,
        children: [
          { path: 'breadcrumbs-api', component: BreadcrumbsApiComponent },
          {
            path: 'breadcrumbs-example-1',
            component: BreadcrumbsExample1Component,
            children: [
              {
                path: 'bc-e1-dashboard',
                component: BcE1Dashboard,
              },
              {
                path: 'bc-e1-settings',
                component: BcE1Settings,
                data: {
                  breadcrumb: BC_E1_SETTINGS_BREADCRUMB_DATA,
                },
                children: [
                  {
                    path: 'bc-e1-personal-settings',
                    component: BcE1PersonalSettings,
                    data: {
                      breadcrumb: BC_E1_PERSONAL_SETTINGS_BREADCRUMB_DATA,
                    },
                  },
                  {
                    path: 'bc-e1-system-settings',
                    component: BcE1SystemSettings,
                    data: {
                      breadcrumb: BC_E1_SYSTEM_SETTINGS_BREADCRUMB_DATA,
                    },
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        path: 'form',
        component: FormComponent,
        children: [
          { path: 'form-examples', component: FormExamplesComponent },
          { path: 'form-example-2', component: FormExample2Component },
          { path: 'form-api', component: FormApiComponent },
        ],
      },
      {
        path: 'tree-view',
        component: TreeView,
        children: [
          { path: 'tree-view-example', component: TreeViewExample },
          { path: 'tree-view-api', component: TreeViewApi },
        ],
      },
      {
        path: 'tables',
        component: TablesComponent,
        children: [
          { path: 'table-example-1', component: TableExample1Component },
          { path: 'table-example-2', component: TableExample2Component },
          { path: 'table-example-3', component: TableExample3Component },
          { path: 'table-example-4', component: TableExample4Component },
          { path: 'table-example-5', component: TableExample5Component },
          { path: 'table-example-6', component: TableResponsiveComponent },
          { path: 'table-api', component: TableApiComponent },
        ],
      },
      {
        path: 'popups',
        component: PopupsComponent,
        children: [
          {
            path: 'notifications',
            component: PopupsNotificationsComponent,
            children: [
              { path: 'notifications-examples', component: PopupsNotificationsExamplesComponent },
              { path: 'notifications-api', component: PopupsNotificationsApiComponent },
            ],
          },
          {
            path: 'form-dialog',
            component: PopupsFormDialogComponent,
            children: [
              { path: 'form-dialog-examples', component: PopupsFormDialogExamplesComponent },
              { path: 'form-dialog-api', component: PopupsFormDialogApiComponent },
            ],
          },
          {
            path: 'confirmation-dialog',
            component: PopupsConfirmationDialogComponent,
            children: [
              { path: 'confirmation-dialog-examples', component: PopupsConfirmationExamplesComponent },
              { path: 'confirmation-dialog-api', component: PopupsConfirmationDialogApiComponent },
            ],
          },
          {
            path: 'table-dialog',
            component: DialogTableComponent,
            children: [
              { path: 'table-dialog-examples', component: DialogTableExamplesComponent },
              { path: 'table-dialog-api', component: DialogTableApiComponent },
            ],
          },
        ],
      },
      { path: 'dashboard', component: DashboardComponent },
      { path: 'map', component: MapComponent },
      {
        path: 'scheduler',
        component: SchedulerComponent,
        children: [
          { path: 'scheduler-examples', component: SchedulerExamplesComponent },
          { path: 'scheduler-api', component: SchedulerApiComponent },
        ],
      },
      {
        path: 'network-diagram',
        component: NetworkDiagramComponent,
        children: [
          { path: 'network-diagram-example-1', component: NetworkDiagramExample1Component },
          { path: 'network-diagram-example-2', component: NetworkDiagramExample2Component },
          { path: 'network-diagram-api', component: NetworkDiagramApiComponent },
        ],
      },
      {
        path: 'pixel-editor',
        component: PixelEditorComponent,
        children: [
          { path: 'pixel-editor-examples', component: PixelEditorExamplesComponent },
          { path: 'pixel-editor-api', component: PixelEditorApiComponent },
        ],
      },
      {
        path: 'documentation',
        component: DocumentationComponent,
        children: [
          { path: 'documentation-examples', component: DocumentationExamplesComponent },
          { path: 'documentation-api', component: DocumentationApiComponent },
        ],
      },
      {
        path: 'general',
        component: GeneralComponent,
        children: [
          {
            path: 'file-upload-button-view',
            component: FileUploadButtonViewComponent,
            children: [
              { path: 'file-upload-button-examples', component: FileUploadButtonExamplesComponent },
              { path: 'file-upload-button-api', component: FileUploadButtonApiComponent },
            ],
          },
          { path: 'placeholder', component: PlaceholderComponent },
          {
            path: 'backgrounds',
            component: BackgroundsComponent,
            children: [{ path: 'squares', component: BackgroundSquaresComponent }],
          },
        ],
      },
    ],
  },
  { path: '**', pathMatch: 'full', redirectTo: '' },
];
