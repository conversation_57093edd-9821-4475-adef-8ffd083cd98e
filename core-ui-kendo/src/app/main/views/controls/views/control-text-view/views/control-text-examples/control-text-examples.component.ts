import { Component } from '@angular/core';
import { CoreInputsModule, CoreInputText, CoreInputValidators } from '../../../../../../../core/components/core-inputs';
import { CoreCardModule } from '../../../../../../../core/components/core-card';

@Component({
  selector: 'app-control-text-examples',
  templateUrl: './control-text-examples.component.html',
  styleUrl: './control-text-examples.component.scss',
  host: { class: 'view-component' },
  imports: [CoreInputsModule, CoreCardModule],
})
export class ControlTextExamplesComponent {
  public textControl = new CoreInputText({
    label: 'Full name',
    placeholder: 'Please enter your full name here...',
    info: { text: 'Please enter your full name in this field', title: 'Full name' },
    hideMessage: true,
    labelPosition: 'left',
    showRequiredIndicator: true,
    validators: [
      CoreInputValidators.required('Required field'),
      CoreInputValidators.minLength(5, 'Must be at least 5 characters long'),
    ],
  });

  public emailTextControl = new CoreInputText({
    label: 'Email',
    validators: [CoreInputValidators.email('Must be a valid email address')],
  });
}
