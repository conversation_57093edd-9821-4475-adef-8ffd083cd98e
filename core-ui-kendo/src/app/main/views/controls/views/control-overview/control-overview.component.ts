// Angular
import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
// Core UI
import {
  CoreInpuColorPicker,
  CoreInputCheckbox,
  CoreInputCron,
  CoreInputDate,
  CoreInputDateTime,
  CoreInputDropDown,
  CoreInputEditor,
  CoreInputEmail,
  CoreInputFileSelect,
  CoreInputMaskedText,
  CoreInputMultiselect,
  CoreInputNumeric,
  CoreInputOtp,
  CoreInputPassword,
  CoreInputRadioSelect,
  CoreInputSelect,
  CoreInputSlider,
  CoreInputsModule,
  CoreInputSwitch,
  CoreInputText,
  CoreInputTextarea,
  CoreInputTime,
  CoreInputValidators,
} from '../../../../../core/components/core-inputs';
import { CoreCardModule } from '../../../../../core/components/core-card';
import { CoreNotificationModule, CoreNotificationService } from '../../../../../core/popups/core-notification';
// Pipes
import { ControlOverviewFileValuePipe } from './control-overview.file-value.pipe';

@Component({
  selector: 'app-control-overview',
  styleUrl: './control-overview.component.scss',
  templateUrl: './control-overview.component.html',
  host: { class: 'view-component' },
  imports: [CommonModule, CoreInputsModule, CoreCardModule, ControlOverviewFileValuePipe, CoreNotificationModule],
})
export class ControlOverviewComponent {
  private _notify = inject(CoreNotificationService);

  public text = new CoreInputText({
    label: 'Text',
  });

  public textMask = new CoreInputMaskedText({
    label: 'Masked Text',
    mask: '(###) 000-0000',
  });

  public textArea = new CoreInputTextarea({
    label: 'Text Area',
    rows: 2,
  });

  public date = new CoreInputDate({
    label: 'Date',
    value: new Date(),
  });

  public dateTime = new CoreInputDateTime({
    label: 'Date Time',
    value: new Date(),
  });

  public password = new CoreInputPassword({
    label: 'Password',
    validators: [CoreInputValidators.required('This field is required.')],
  });

  public number = new CoreInputNumeric({
    label: 'Number',
  });

  public select = new CoreInputSelect({
    label: 'Select',
    options: [
      { value: 1, label: 'Option 1' },
      { value: 2, label: 'Option 2' },
      { value: 3, label: 'Option 3' },
      { value: 4, label: 'Option 4' },
    ],
  });

  public multiselect = new CoreInputMultiselect({
    label: 'Multiselect',
    checkboxes: true,
    options: [
      { value: 1, label: 'Option 1' },
      { value: 2, label: 'Option 2' },
      { value: 3, label: 'Option 3' },
      { value: 4, label: 'Option 4' },
    ],
  });

  public checkbox = new CoreInputCheckbox({
    label: 'Checkbox',
  });

  public switch = new CoreInputSwitch({
    label: 'Switch',
  });

  public colorPicker = new CoreInpuColorPicker({
    label: 'Color Picker ',
  });

  public dropDownSelect = new CoreInputDropDown({
    label: 'Dropdown Select',
    filterable: true,
    options: [
      { value: 1, label: 'Option 1' },
      { value: 2, label: 'Option 2' },
      { value: 3, label: 'Option 3' },
    ],
  });

  public fileSelect = new CoreInputFileSelect({
    label: 'File Select',
    value: [
      { uid: 'abc', name: 'file1.txt', size: 1024 },
      { name: 'image1.png', size: 512 },
    ],
    onFilesSelect: (files) => {
      console.log('select', files);
    },
    onFileRemove: (file) => {
      this._notify.success(`Remove: ${JSON.stringify(file, null, 2)}`, { duration: 5000 });
    },
    onFileClick: (file) => {
      this._notify.success(`Click: ${JSON.stringify(file, null, 2)}`, { duration: 5000 });
    },
  });

  public otpControl = new CoreInputOtp({
    label: 'OTP',
  });

  public timeControl = new CoreInputTime({
    label: 'Time',
    format: 'hh:mm a',
  });

  public radioSelect = new CoreInputRadioSelect({
    label: 'Radio Select',
    options: [
      { value: 'Admin', label: 'Admin' },
      { value: 'User', label: 'User' },
      { value: 'Guest', label: 'Guest' },
    ],
    direction: 'horizontal',
  });

  public emailControl = new CoreInputEmail({
    label: 'Email',
    validators: [CoreInputValidators.email('Please enter a valid email address.')],
  });

  public textEditor = new CoreInputEditor({
    label: 'Text Editor',
  });

  public sliderControl = new CoreInputSlider({
    label: 'Slider',
    min: 0,
    max: 100,
    smallStep: 5,
    largeStep: 10,
  });

  public cronControl = new CoreInputCron({
    label: 'Cron',
  });
}
