<core-card class="card">
  <core-card-header>Controls</core-card-header>
  <core-card-body class="controls">
    <core-input-text [control]="text" />
    <core-input-textarea [control]="textArea" />
    <core-input-masked-text [control]="textMask" />
    <core-input-date [control]="date" />
    <core-input-date-time [control]="dateTime" />
    <core-input-time [control]="timeControl" />
    <core-input-numeric [control]="number" />
    <core-input-password [control]="password" />
    <core-input-select [control]="select" />
    <core-input-multiselect [control]="multiselect" />
    <core-input-drop-down [control]="dropDownSelect" />
    <core-input-radio-select [control]="radioSelect" />
    <core-input-otp [control]="otpControl" />
    <core-input-email [control]="emailControl" />
    <core-input-slider [control]="sliderControl" />
    <core-input-switch [control]="switch" />
    <core-input-checkbox [control]="checkbox" />
    <core-input-color-picker [control]="colorPicker" />
    <core-input-file-select [control]="fileSelect" />
    <core-input-editor [control]="textEditor" />
    <core-input-cron [control]="cronControl" />
  </core-card-body>
</core-card>

<core-card class="card">
  <core-card-header>Controls Values</core-card-header>
  <core-card-body>
    <pre>
    <b>Text:</b> {{ text.value }}
    <b>TextArea:</b> {{ textArea.value }}
    <b>Masked Text:</b> {{ textMask.value }}
    <b>Date:</b> {{ date.value | date: "MM/dd/yyyy" }}
    <b>Date Time:</b> {{ dateTime.value | date: 'MM/dd/yyyy hh:mm:ss a' }}
    <b>Time:</b> {{ timeControl.value | date: 'hh:mm a' }}
    <b>Number:</b> {{ number.value }}
    <b>Password:</b> {{ password.value }}
    <b>Select:</b> {{ select.value }}
    <b>Multiselect:</b> {{ multiselect.value }}
    <b>DropdownSelect:</b> {{ dropDownSelect.value }}
    <b>Radio Select:</b> {{ radioSelect.value }}
    <b>OTP:</b> {{ otpControl.value }}
    <b>Email:</b> {{ emailControl.value }}
    <b>Slider:</b> {{ sliderControl.value }}
    <b>Switch:</b> {{ switch.value }}
    <b>Checkbox:</b> {{ checkbox.value }}
    <b>Color Picker:</b> {{ colorPicker.value }}
    <b>File Select:</b> {{ fileSelect.value | fileValue }}
    <b>Text Editor:</b> <div [innerHTML]="textEditor.value"></div>
    <b>Cron:</b> {{ cronControl.value }}
    </pre>
  </core-card-body>
</core-card>
