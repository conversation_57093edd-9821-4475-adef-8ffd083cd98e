<!-- Simple List View -->
<kendo-splitter orientation="horizontal">
  <kendo-splitter-pane>
    <kendo-splitter>
      <kendo-splitter-pane>
        <!-- Card -->
        <core-card class="card">
          <core-card-header>
            <span>List View</span>
          </core-card-header>
          <!-- Card Body -->
          <core-card-body>
            <div class="card-content">
              <fieldset>
                <!-- Switch Button -->
                <core-button
                  [label]="listViewAlign() === 'vertical' ? 'Switch to Horizontal' : 'Switch to Vertical'"
                  (click)="switchAlign()"
                />
              </fieldset>
              <fieldset>
                <legend>List View Example</legend>
                <!-- List View -->
                <core-list-view
                  [items]="listViewItems"
                  [checkbox]="'multiple'"
                  [align]="listViewAlign()"
                  (checkedChange)="onCheckedChange($event, 'simple')"
                />
              </fieldset>
            </div>
          </core-card-body>
        </core-card>
      </kendo-splitter-pane>

      <!-- Events Log -->
      <kendo-splitter-pane [collapsible]="true" [resizable]="false" [collapsed]="true">
        <app-event-console [events]="eventLogs()" />
      </kendo-splitter-pane>
    </kendo-splitter>
  </kendo-splitter-pane>
</kendo-splitter>

<!-- Custom Template List View -->
<kendo-splitter orientation="horizontal">
  <kendo-splitter-pane>
    <kendo-splitter>
      <kendo-splitter-pane>
        <!-- Card -->
        <core-card class="card">
          <core-card-header>
            <span>List View (Custom Template)</span>
          </core-card-header>
          <!-- Card Body -->
          <core-card-body>
            <div class="card-content">
              <fieldset>
                <legend>List View Example</legend>
                <!-- List View -->
                <core-list-view
                  [items]="customListViewItems"
                  [checkbox]="'single'"
                  (checkedChange)="onCheckedChange($event, 'custom')"
                >
                  <!-- Custom Template -->
                  <ng-template coreListViewItemTemplate let-dataItem>
                    <div class="item-container">
                      <div class="details-container">
                        <div class="row">
                          <span class="label">Full Name:</span>
                          <span>{{ dataItem.fullName }}</span>
                        </div>
                        <div class="row">
                          <span class="label">Age:</span>
                          <span>{{ dataItem.age }}</span>
                        </div>
                        <div class="row">
                          <span class="label">City:</span>
                          <span>{{ dataItem.city }}</span>
                        </div>
                      </div>
                      <core-button
                        [label]="'Edit'"
                        [color]="'secondary'"
                        [icon]="pencilIcon"
                        (click)="onEdit(dataItem.fullName)"
                      />
                    </div>
                  </ng-template>
                </core-list-view>
              </fieldset>
            </div>
          </core-card-body>
        </core-card>
      </kendo-splitter-pane>

      <!-- Events Log -->
      <kendo-splitter-pane [collapsible]="true" [resizable]="false" [collapsed]="true">
        <app-event-console [events]="eventLogsCustom()" [height]="317" />
      </kendo-splitter-pane>
    </kendo-splitter>
  </kendo-splitter-pane>
</kendo-splitter>
