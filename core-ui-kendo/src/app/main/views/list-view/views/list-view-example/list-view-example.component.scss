:host {
  display: flex;
  flex-direction: row;
  align-items: start;
  padding: 0.5rem;
  gap: 0.8rem;
  overflow: auto;
}

kendo-splitter {
  width: fit-content;
}

.card {
  width: 350px;
  box-shadow: var(--kendo-elevation-2);
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  overflow: auto;
}

fieldset {
  border-radius: 0.25rem;
  border-color: var(--border-color);
}

// [Template styles]

.item-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.details-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.row {
  display: grid;
  grid-template-columns: 67px 1fr;
  gap: 4px;
}

.label {
  color: var(--accent-primary-color-alt);
}

core-button {
  width: 6rem;
}
