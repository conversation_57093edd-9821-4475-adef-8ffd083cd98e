// Angular
import { Component, signal } from '@angular/core';
import { CoreListViewModule } from '../../../../../core/components/core-list-view/core-list-view.module';
import { CoreCardModule } from '../../../../../core/components/core-card';
// Core UI
import { CoreNotificationModule } from '../../../../../core/popups/core-notification';
import { CoreListViewItem } from '../../../../../core/components/core-list-view/core-list-view';
import { CoreButtonModule } from '../../../../../core/components/core-button';
// Kendo UI
import { SplitterModule } from '@progress/kendo-angular-layout';
import { SVGIconModule } from '@progress/kendo-angular-icons';
// Icons
import { pencilIcon, userIcon } from '@progress/kendo-svg-icons';
import { key, palm } from '../../../../../core/components/core-icon';
// Components
import { EventConsoleComponent } from '../../../../components/event-console/event-console.component';
// Interfaces
import { EventLogItem } from '../../../../components/event-console/event-console.interfaces';

@Component({
  selector: 'app-list-view-example',
  templateUrl: './list-view-example.component.html',
  styleUrl: './list-view-example.component.scss',
  host: { class: 'view-component' },
  imports: [
    CoreListViewModule,
    CoreCardModule,
    CoreNotificationModule,
    CoreButtonModule,
    SVGIconModule,
    EventConsoleComponent,
    SplitterModule,
  ],
})
export class ListViewExampleComponent {
  // Signals
  public listViewAlign = signal<'vertical' | 'horizontal'>('horizontal');
  public eventLogs = signal<EventLogItem[]>([]);
  public eventLogsCustom = signal<EventLogItem[]>([]);
  // Icons
  public pencilIcon = pencilIcon;

  // [ Events ]

  public onCheckedChange(ids: (string | number)[], type: 'simple' | 'custom'): void {
    if (type === 'simple') {
      this.eventLogs.set([{ value: `Checked IDs: ${ids.join(', ')}` }, ...this.eventLogs()]);
    } else {
      this.eventLogsCustom.set([{ value: `Checked IDs: ${ids.join(', ')}` }, ...this.eventLogsCustom()]);
    }
  }

  public onEdit(name: string): void {
    this.eventLogsCustom.set([{ value: `Edit user: ${name}` }, ...this.eventLogsCustom()]);
  }

  public switchAlign(): void {
    this.listViewAlign.set(this.listViewAlign() === 'vertical' ? 'horizontal' : 'vertical');
  }

  // [ Helpers ]

  public listViewItems: CoreListViewItem[] = [
    {
      id: 1,
      icon: userIcon,
      fields: [
        { labelText: 'Full Name', labelProperty: 'Andrew Fuller' },
        { labelText: 'Age', labelProperty: 41 },
        { labelText: 'City', labelProperty: 'Seattle' },
      ],
    },
    {
      id: 2,
      icon: key,
      fields: [
        { labelText: 'Full Name', labelProperty: 'Mark Adams' },
        { labelText: 'Age', labelProperty: 30 },
        { labelText: 'City', labelProperty: 'New York' },
      ],
    },
    {
      id: 3,
      icon: palm,
      fields: [
        { labelText: 'Full Name', labelProperty: 'Nancy Davolio' },
        { labelText: 'Age', labelProperty: 25 },
        { labelText: 'City', labelProperty: 'Los Angeles' },
      ],
    },
  ];

  public customListViewItems: CoreListViewItem[] = [
    {
      id: 1,
      icon: userIcon,
      fullName: 'Andrew Fuller',
      age: 41,
      city: 'Seattle',
    },
    {
      id: 2,
      icon: key,
      fullName: 'Mark Adams',
      age: 30,
      city: 'New York',
    },
    {
      id: 3,
      icon: palm,
      fullName: 'Nancy Davolio',
      age: 25,
      city: 'Los Angeles',
    },
  ];
}
