// Angular
import { Component } from '@angular/core';
// Components
import { ApiViewComponent } from '../../../../components/api-view/api-view.component';
// Interfaces
import { APIEventData, APIInputData } from '../../../../components/api-view/api-view.interfaces';
// Constants
import { CORE_LIST_VIEW_API_EVENTS, CORE_LIST_VIEW_API_INPUTS } from './list-view-api.constants';

@Component({
  selector: 'app-list-view-api',
  templateUrl: './list-view-api.component.html',
  styleUrl: './list-view-api.component.scss',
  host: { class: 'view-component' },
  imports: [ApiViewComponent],
})
export class ListViewApiComponent {
  public inputsData: APIInputData[] = CORE_LIST_VIEW_API_INPUTS;
  public eventsData: APIEventData[] = CORE_LIST_VIEW_API_EVENTS;
}
