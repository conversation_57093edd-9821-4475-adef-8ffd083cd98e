import { APIEventData, APIInputData, APIInterface } from '../../../../components/api-view/api-view.interfaces';
import { CORE_ICON_INTERFACE } from '../../../icons/views/icon-api/icon-api.constants';

export const CORE_LIST_VIEW_ITEM_INTERFACE: APIInterface = {
  name: 'CoreListViewItem',
  fields: [
    {
      name: 'id',
      type: 'string | number',
      description: 'Unique id of the item',
    },
    {
      name: 'icon',
      type: 'interface',
      interfaces: [CORE_ICON_INTERFACE],
      description: 'Icon of the item',
    },
    {
      name: 'fields',
      type: '{ labelText: string; labelProperty: string | number }[ ]',
      description: 'Fields of the item',
    },
    { name: '[key: string]', type: 'any', description: 'Additional properties' },
  ],
};

export const CORE_LIST_VIEW_API_INPUTS: APIInputData[] = [
  {
    name: 'items',
    type: 'CoreListViewItem[ ]',
    default: 'No default, must be provided',
    description: 'This input is required',
    interfaces: [CORE_LIST_VIEW_ITEM_INTERFACE],
  },
  {
    name: 'checkbox',
    type: `'none' | 'single' | 'multiple'`,
    default: `'none'`,
    description: 'Defines the checkbox selection mode.',
  },
  {
    name: 'align',
    type: `'vertical' | 'horizontal'`,
    default: `'vertical'`,
    description: 'Defines the alignment of the items.',
  },
];

export const CORE_LIST_VIEW_API_EVENTS: APIEventData[] = [
  {
    name: 'checkedChange',
    description: 'Emitted when the checked state of an item changes.',
  },
];
