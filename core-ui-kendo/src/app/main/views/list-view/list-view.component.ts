import { Component, Signal, signal } from '@angular/core';
import { CoreNavigationTabsModule, CoreNavigationTabs } from '../../../core/components/core-navigation-tabs';

@Component({
  selector: 'app-list-view',
  templateUrl: './list-view.component.html',
  styleUrl: './list-view.component.scss',
  host: { class: 'view-component' },
  imports: [CoreNavigationTabsModule],
})
export class ListViewComponent {
  public navigationTabs: Signal<CoreNavigationTabs>;

  constructor() {
    this.navigationTabs = signal(
      new CoreNavigationTabs({
        tabs: [
          { label: 'List View Example', route: 'list-view-example' },
          { label: 'API', route: 'list-view-api' },
        ],
        autoNavigate: true,
      }),
    );
  }
}
