import { Component } from '@angular/core';
import { ApiViewComponent } from '../../../../components/api-view/api-view.component';
import {
  CORE_TREE_VIEW_API_EVENTS,
  CORE_TREE_VIEW_API_INPUTS,
  CORE_TREE_VIEW_API_METHODS,
} from './tree-view-api.constants';
import { APIEventData, APIInputData, APIMethodData } from '../../../../components/api-view/api-view.interfaces';

@Component({
  selector: 'app-tree-view-api',
  templateUrl: './tree-view-api.html',
  styleUrl: './tree-view-api.scss',
  host: { class: 'view-component' },
  imports: [ApiViewComponent],
})
export class TreeViewApi {
  public inputsData: APIInputData[] = CORE_TREE_VIEW_API_INPUTS;
  public eventsData: APIEventData[] = CORE_TREE_VIEW_API_EVENTS;
  public methodsData: APIMethodData[] = CORE_TREE_VIEW_API_METHODS;
}
