import {
  APIEventData,
  APIInputData,
  APIInterface,
  APIMethodData,
} from '../../../../components/api-view/api-view.interfaces';
import { CORE_ICON_INTERFACE } from '../../../icons/views/icon-api/icon-api.constants';

export const CORE_TREE_VIEW_ITEM_INTERFACE: APIInterface = {
  name: 'CoreTreeViewItem',
  fields: [
    {
      name: 'id',
      type: 'string | number',
      description: 'Unique id of the item',
    },
    {
      name: 'label',
      type: 'string',
      description: 'Label of the item',
    },
    {
      name: 'icon',
      type: 'interface',
      interface: CORE_ICON_INTERFACE,
      description: 'Icon of the item',
    },
    {
      name: 'children',
      type: 'CoreTreeViewItem[]',
      description: 'Children of the item',
    },
  ],
};

export const CORE_TREE_VIEW_ITEMS_INPUT: APIInputData = {
  name: 'items',
  type: 'CoreTreeViewItem[]',
  default: 'No default, must be provided',
  description: 'This input is required',
  interface: CORE_TREE_VIEW_ITEM_INTERFACE,
};

export const CORE_TREE_VIEW_CHECKABLE_INPUT: APIInputData = {
  name: 'checkable',
  type: 'boolean',
  default: 'false',
  description: 'Enables the checkable functionality when set to "true"',
};

export const CORE_TREE_VIEW_API_INPUTS: APIInputData[] = [CORE_TREE_VIEW_ITEMS_INPUT, CORE_TREE_VIEW_CHECKABLE_INPUT];

export const CORE_TREE_VIEW_API_EVENTS: APIEventData[] = [
  {
    name: 'expandChange',
    description: 'Emitted when the expand state of an item changes',
  },
  {
    name: 'checkedChange',
    description: 'Emitted when the checked state of an item changes',
  },
];

export const CORE_TREE_VIEW_API_METHODS: APIMethodData[] = [
  {
    name: 'expandItems',
    description: 'Expands the provided items',
  },
  {
    name: 'collapseItems',
    description: 'Collapses the provided items',
  },
  {
    name: 'expandAll',
    description: 'Expands all items',
  },
  {
    name: 'collapseAll',
    description: 'Collapses all items',
  },
  {
    name: 'checkItems',
    description: 'Checks the provided items',
  },
  {
    name: 'uncheckItems',
    description: 'Unchecks the provided items',
  },
  {
    name: 'checkAll',
    description: 'Checks all items',
  },
  {
    name: 'uncheckAll',
    description: 'Unchecks all items',
  },
];
