:host {
  display: flex;
  flex-direction: row;
  align-items: start;
  padding: 0.5rem;
  gap: 0.8rem;
  overflow: auto;
}

kendo-splitter {
  width: fit-content;
}

.card {
  width: 350px;
  box-shadow: var(--kendo-elevation-2);
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  overflow: auto;
}

fieldset {
  border-radius: 0.25rem;
  border-color: var(--border-color);
}

.actions {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
  justify-content: stretch;
  gap: 0.5rem;
}

.hint {
  display: block;
  text-align: center;
  margin-top: 1rem;
}
