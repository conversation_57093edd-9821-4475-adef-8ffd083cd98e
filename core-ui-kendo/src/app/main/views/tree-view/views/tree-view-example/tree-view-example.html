<core-card class="card">
  <core-card-header>
    <span>Tree View</span>
  </core-card-header>
  <core-card-body>
    <div class="card-content">
      <fieldset>
        <legend>Methods</legend>
        <div class="actions">
          <core-button [label]="'Expand All'" (click)="onExpandAll()" />
          <core-button [label]="'Collapse All'" (click)="onCollapseAll()" />
          <core-button [label]="'Expand Some'" (click)="expandSome()" />
          <core-button [label]="'Check All'" (click)="onCheckAll()" />
          <core-button [label]="'Uncheck All'" (click)="onUncheckAll()" />
          <core-button [label]="'Check Some'" (click)="checkSome()" />
        </div>
        <span class="hint">Check the console for the events.</span>
      </fieldset>
      <fieldset>
        <legend>Tree View Example</legend>
        <core-tree-view
          [items]="treeViewItems()"
          [checkable]="true"
          (expandChange)="onExpandChange($event)"
          (checkedChange)="onCheckedChange($event)"
        />
      </fieldset>
    </div>
  </core-card-body>
</core-card>
