// Angular
import { Component, signal, viewChild } from '@angular/core';
// Kendo UI
import { fileIcon, folderIcon } from '@progress/kendo-svg-icons';
import { SplitterModule } from '@progress/kendo-angular-layout';
// Core UI
import { CoreCardModule } from '../../../../../core/components/core-card';
import { CoreTreeViewItem, CoreTreeViewModule } from '../../../../../core/components/core-tree-view';
import { CoreTreeViewFlatItem } from '../../../../../core/components/core-tree-view/core-tree-view.interfaces';
import { CoreTreeView } from '../../../../../core/components/core-tree-view/core-tree-view';
import { CoreButtonModule } from '../../../../../core/components/core-button';
// Directives
import { CoreSizeChangeDirective } from '../../../../../core/directives/core-size-change.directive';
// Components
import { EventConsoleComponent } from '../../../../components/event-console/event-console.component';
import { EventLogItem } from '../../../../components/event-console/event-console.interfaces';

@Component({
  selector: 'app-tree-view-example',
  templateUrl: './tree-view-example.html',
  styleUrl: './tree-view-example.scss',
  host: { class: 'view-component' },
  imports: [
    CoreCardModule,
    CoreTreeViewModule,
    CoreButtonModule,
    EventConsoleComponent,
    SplitterModule,
    CoreSizeChangeDirective,
  ],
})
export class TreeViewExample {
  // Signals
  public treeViewItems = signal<CoreTreeViewItem[]>(this.getTreelistItems());
  public eventConsoleData = signal<EventLogItem[]>([]);
  public cardHeight = signal(400);

  // ViewChild
  private treeViewRef = viewChild(CoreTreeView);

  public onCheckedChange(e: CoreTreeViewFlatItem[]): void {
    this.eventConsoleData.set([
      { value: `Checked items: ${e.map((i) => i.id).join(', ')}` },
      ...this.eventConsoleData(),
    ]);
    console.log('Checked items: ', e);
  }

  public onExpandChange(e: CoreTreeViewFlatItem[]): void {
    this.eventConsoleData.set([
      { value: `Expanded items: ${e.map((i) => i.label).join(', ')}` },
      ...this.eventConsoleData(),
    ]);
    console.log('Expanded items: ', e);
  }

  public onExpandAll(): void {
    if (!this.treeViewRef()) return;
    this.treeViewRef()!.expandAll();
  }

  public onCollapseAll(): void {
    if (!this.treeViewRef()) return;
    this.treeViewRef()!.collapseAll();
  }

  public expandSome(): void {
    if (!this.treeViewRef()) return;
    this.treeViewRef()!.expandItems([this.treeViewItems()[0].id]);
  }

  public onCheckAll(): void {
    if (!this.treeViewRef()) return;
    this.treeViewRef()!.checkAll();
  }

  public onUncheckAll(): void {
    if (!this.treeViewRef()) return;
    this.treeViewRef()!.uncheckAll();
  }

  public checkSome(): void {
    if (!this.treeViewRef()) return;
    const ids = [121, 122, 2];
    this.treeViewRef()!.checkItems(ids);
  }

  // Event - Card Size Change

  public onCardSizeChange(height?: number): void {
    this.cardHeight.set(height ?? 400);
  }

  // Helpers

  private getTreelistItems(): CoreTreeViewItem[] {
    return [
      {
        id: 1,
        label: 'Root Item 1',
        icon: folderIcon,
        children: [
          {
            id: 11,
            label: 'Child 1 of Root 1',
            icon: fileIcon,
          },
          {
            id: 12,
            label: 'Child 2 of Root 1',
            icon: folderIcon,
            children: [
              {
                id: 121,
                label: 'Child 1 of Child 2 of Root 1',
                icon: fileIcon,
              },
              {
                id: 122,
                label: 'Child 2 of Child 2 of Root 1',
                icon: fileIcon,
              },
              {
                id: 123,
                label: 'Child 3 of Child 2 of Root 1',
                icon: fileIcon,
              },
            ],
          },
        ],
      },
      {
        id: 2,
        label: 'Root Item 2',
        icon: folderIcon,
      },
    ];
  }
}
