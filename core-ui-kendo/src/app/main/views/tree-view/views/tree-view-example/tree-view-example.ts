// Angular
import { Component, signal, viewChild } from '@angular/core';
// Kendo UI
import { fileIcon, folderIcon } from '@progress/kendo-svg-icons';
// Core UI
import { CoreCardModule } from '../../../../../core/components/core-card';
import { CoreTreeViewItem, CoreTreeViewModule } from '../../../../../core/components/core-tree-view';
import { CoreTreeViewFlatItem } from '../../../../../core/components/core-tree-view/core-tree-view.interfaces';
import { CoreTreeView } from '../../../../../core/components/core-tree-view/core-tree-view';
import { CoreButtonModule } from '../../../../../core/components/core-button';

@Component({
  selector: 'app-tree-view-example',
  templateUrl: './tree-view-example.html',
  styleUrl: './tree-view-example.scss',
  host: { class: 'view-component' },
  imports: [CoreCardModule, CoreTreeViewModule, CoreButtonModule],
})
export class TreeViewExample {
  public treeViewItems = signal<CoreTreeViewItem[]>(this.getTreelistItems());
  private treeViewRef = viewChild(CoreTreeView);

  public onCheckedChange(e: CoreTreeViewFlatItem[]): void {
    console.log('Checked items: ', e);
  }

  public onExpandChange(e: CoreTreeViewFlatItem[]): void {
    console.log('Expanded items: ', e);
  }

  public onExpandAll(): void {
    if (!this.treeViewRef()) return;
    this.treeViewRef()!.expandAll();
  }

  public onCollapseAll(): void {
    if (!this.treeViewRef()) return;
    this.treeViewRef()!.collapseAll();
  }

  public expandSome(): void {
    if (!this.treeViewRef()) return;
    this.treeViewRef()!.expandItems([this.treeViewItems()[0].id]);
  }

  public onCheckAll(): void {
    if (!this.treeViewRef()) return;
    this.treeViewRef()!.checkAll();
  }

  public onUncheckAll(): void {
    if (!this.treeViewRef()) return;
    this.treeViewRef()!.uncheckAll();
  }

  public checkSome(): void {
    if (!this.treeViewRef()) return;
    const ids = [121, 122, 2];
    this.treeViewRef()!.checkItems(ids);
  }

  // Helpers

  private getTreelistItems(): CoreTreeViewItem[] {
    return [
      {
        id: 1,
        label: 'Root Item 1',
        icon: folderIcon,
        children: [
          {
            id: 11,
            label: 'Child 1 of Root 1',
            icon: fileIcon,
          },
          {
            id: 12,
            label: 'Child 2 of Root 1',
            icon: folderIcon,
            children: [
              {
                id: 121,
                label: 'Child 1 of Child 2 of Root 1',
                icon: fileIcon,
              },
              {
                id: 122,
                label: 'Child 2 of Child 2 of Root 1',
                icon: fileIcon,
              },
              {
                id: 123,
                label: 'Child 3 of Child 2 of Root 1',
                icon: fileIcon,
              },
            ],
          },
        ],
      },
      {
        id: 2,
        label: 'Root Item 2',
        icon: folderIcon,
      },
    ];
  }
}
