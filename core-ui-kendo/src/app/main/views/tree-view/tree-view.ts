import { Component, Signal, signal } from '@angular/core';
import { CoreNavigationTabs, CoreNavigationTabsModule } from '../../../core/components/core-navigation-tabs';

@Component({
  selector: 'app-tree-view',
  templateUrl: './tree-view.html',
  styleUrl: './tree-view.scss',
  host: { class: 'view-component' },
  imports: [CoreNavigationTabsModule],
})
export class TreeView {
  public navigationTabs: Signal<CoreNavigationTabs>;

  constructor() {
    this.navigationTabs = signal(
      new CoreNavigationTabs({
        tabs: [
          { label: 'Tree View Example', route: 'tree-view-example' },
          { label: 'API', route: 'tree-view-api' },
        ],
        autoNavigate: true,
      }),
    );
  }
}
