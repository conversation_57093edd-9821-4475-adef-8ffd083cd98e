// Angular
import { Component, inject } from '@angular/core';
// Core UI
import {
  CoreDialogConfirmModule,
  CoreDialogConfirmService,
} from '../../../../../../../core/popups/core-dialog-confirm';
import { CoreCardModule } from '../../../../../../../core/components/core-card';
import { CoreButtonModule } from '../../../../../../../core/components/core-button';
import { CoreNotificationModule, CoreNotificationService } from '../../../../../../../core/popups/core-notification';

@Component({
  selector: 'app-popups-confirmation-examples',
  templateUrl: './popups-confirmation-examples.component.html',
  styleUrl: './popups-confirmation-examples.component.scss',
  host: { class: 'view-component' },
  imports: [CoreCardModule, CoreButtonModule, CoreDialogConfirmModule, CoreNotificationModule],
})
export class PopupsConfirmationExamplesComponent {
  private _dialogConfirmService = inject(CoreDialogConfirmService);
  private _notify = inject(CoreNotificationService);

  public async showConfirmationDialog(): Promise<void> {
    const dialogResult = await this._dialogConfirmService.open({
      title: 'Confirmation Dialog',
      message: 'Are you sure you want to continue?',
      actionLabel: 'Yes',
      secondaryActionLabel: 'No',
      actionBusyLabel: 'Wait',
      onAction() {
        return new Promise<boolean>((resolve) => {
          setTimeout(() => {
            resolve(true);
          }, 3000);
        });
      },
      onSecondaryAction() {
        return new Promise<boolean>((resolve) => {
          setTimeout(() => {
            resolve(true);
          }, 3000);
        });
      },
    });
    this._notify.success(`Dialog result: ${dialogResult}`, { duration: 10000 });
  }
}
