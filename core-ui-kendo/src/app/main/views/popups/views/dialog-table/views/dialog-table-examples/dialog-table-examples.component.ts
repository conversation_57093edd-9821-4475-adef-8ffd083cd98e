// Angular
import { Component, inject } from '@angular/core';
// Core UI
import { CoreButtonModule } from '../../../../../../../core/components/core-button';
import { CoreCardModule } from '../../../../../../../core/components/core-card';
import { CoreTable, CoreTablePageableData } from '../../../../../../../core/components/core-table';
import { CoreDialogTableModule, CoreDialogTableService } from '../../../../../../../core/popups/core-dialog-table';
// Interfaces
interface ExampleUser {
  id: string;
  fullName: string;
}

@Component({
  selector: 'app-dialog-table-examples',
  templateUrl: './dialog-table-examples.component.html',
  styleUrl: './dialog-table-examples.component.scss',
  host: { class: 'view-component' },
  imports: [CoreCardModule, CoreButtonModule, CoreDialogTableModule],
})
export class DialogTableExamplesComponent {
  private _dialogTable = inject(CoreDialogTableService);
  private selectedUserIds: string[] = [];

  public async onShowDialog(): Promise<void> {
    const dialogResult = await this._dialogTable.open<ExampleUser>({
      title: 'Select Users',
      table: new CoreTable<ExampleUser>({
        columns: [CoreTable.ColumnString({ field: 'fullName', label: 'Full Name' })],
        pageable: true,
        data: async () => {
          const users = await this.getUsers();
          return users;
        },
        selectable: 'multiple',
        showSelectColumn: true,
        onSelectionChange: (users) => {
          this.selectedUserIds = users.map((u) => u.id);
        },
      }),
      actionLabel: 'Select',
      width: '500px',
      height: 'min(calc(100dvh - 2rem), 600px)',
      onAction() {
        return new Promise<boolean>((resolve) => {
          setTimeout(() => {
            resolve(true);
          }, 3000);
        });
      },
    });
    if (!dialogResult) return;
  }

  // Internal

  private async getUsers(): Promise<CoreTablePageableData<ExampleUser>> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: [
            { id: '1', fullName: 'John Smith' },
            { id: '2', fullName: 'Alice Smith' },
          ],
          total: 2,
        });
      }, 200);
    });
  }
}
