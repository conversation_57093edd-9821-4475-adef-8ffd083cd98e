// Angular
import { Component, inject, Signal, signal } from '@angular/core';
// Core UI
import { CoreDialogFormModule } from '../../../core/popups/core-dialog-form';
import { CoreDashboard, CoreDashboardModule, CoreDashboardWidget } from '../../../core/components/core-dashboard';
import { CoreNotificationModule, CoreNotificationService } from '../../../core/popups/core-notification';
import { CoreDashboardWidgetText } from '../../../core/components/core-dashboard/components/core-dashboard-widget-text/core-dashboard-widget-text';
import { CoreDashboardWidgetCustom } from '../../../core/components/core-dashboard/components/core-dashboard-widget-custom/core-dashboard-widget-custom';
import { CoreTable } from '../../../core/components/core-table';
import { CoreDashboardWidgetTable } from '../../../core/components/core-dashboard/components/core-dashboard-widget-table/core-dashboard-widget-table';
import { CoreTableColumn } from '../../../core/components/core-table/classes/core-table-column';
import { CoreDashboardWidgetChart } from '../../../core/components/core-dashboard/components/core-dashboard-widget-chart/core-dashboard-widget-chart';
// Icons
import { route, signsPost } from '../../../core/components/core-icon';
// Services
import { DashboardService } from './dashboard.service';
// Interfaces
import { UserTable } from './dashboard.interfaces';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
  host: { class: 'view-component' },
  providers: [DashboardService],
  imports: [CoreDialogFormModule, CoreDashboardModule, CoreNotificationModule],
})
export class DashboardComponent {
  public dashboard!: Signal<CoreDashboard>;

  // Dependencies
  private _service = inject(DashboardService);
  private _notify = inject(CoreNotificationService);

  constructor() {
    this.dashboard = signal(
      new CoreDashboard({
        widgets: [
          // Text widget
          new CoreDashboardWidgetText({
            id: '1',
            type: 'text',
            title: 'Example 1',
            values: { text: 'Text content', color: 'blue' },
            x: 0, // Default 0
            y: 0, // Default 0
            icon: route,
            onRemoveClick: (widget) => {
              this.removeWidget(widget);
            },
            onSettingsClick: async () => {
              await this.onSettingsButton();
            },
          }),
          // Text widget
          new CoreDashboardWidgetText({
            id: '2',
            type: 'text',
            title: 'Example 2',
            values: { color: 'red', text: 'Text content' },
            x: 2,
            width: 3,
            icon: route,
            onRemoveClick: (widget) => {
              this.removeWidget(widget);
            },
          }),
          // Custom widget
          new CoreDashboardWidgetCustom({
            id: '3',
            type: 'custom',
            title: 'Example (Custom)',
            icon: signsPost,
            selector: 'app-example',
            x: 5,
          }),
          // Chart widget (bar)
          new CoreDashboardWidgetChart({
            id: '4',
            type: 'chart',
            title: 'Chart (Bar)',
            chartType: 'bar',
            width: 5,
            height: 3,
            data: {
              labels: ['January', 'February', 'March', 'April', 'May'],
              datasets: [
                {
                  label: 'Sales 2024',
                  data: [50, 75, 100, 125, 150],
                  backgroundColor: 'rgb(54, 162, 235)',
                  borderColor: 'rgb(36, 123, 190)',
                  hoverOffset: 4,
                },
                {
                  label: 'Sales 2023',
                  data: [45, 70, 95, 120, 140],
                  backgroundColor: 'rgb(6, 187, 3)',
                  borderWidth: 1,
                  hoverOffset: 4,
                },
              ],
            },
            options: {
              responsive: true,
              plugins: {
                legend: { display: true, position: 'top' },
                title: { display: true, text: 'Monthly Sales Comparison' },
              },
              scales: {
                x: {
                  title: { display: true, text: 'Months' },
                  grid: { display: false },
                },
                y: {
                  title: { display: true, text: 'Sales (in USD)' },
                  min: 0,
                  max: 200,
                  ticks: { stepSize: 40 }, // Step size of the y-axis
                },
              },
            },
          }),
          // Table widget
          new CoreDashboardWidgetTable({
            id: '5',
            type: 'table',
            title: 'Example (Table)',
            width: 5,
            height: 3,
            x: 7,
            table: new CoreTable<UserTable>({
              columns: this.loadTableColums(),
              state: {
                sorting: { field: 'username', direction: 'ASC' },
              },
              data: () => {
                // Mock data
                const users: UserTable[] = [
                  { id: '1', username: 'johndoe', fullName: 'John Doe', isActive: true },
                  { id: '2', username: 'janedoe', fullName: 'Jane Doe', isActive: false },
                  { id: '3', username: 'alice', fullName: 'Alice Smith', isActive: true },
                ];
                return users;
              },
              selectable: 'single',
            }),
            onRemoveClick: (widget) => {
              this.removeWidget(widget);
            },
          }),
          // Chart widget (doughnut)
          new CoreDashboardWidgetChart({
            id: '6',
            type: 'chart',
            title: 'Chart (Doughnut)',
            chartType: 'doughnut',
            x: 5,
            width: 2,
            height: 3,
            data: {
              labels: ['Jan', 'Feb', 'March', 'Apr'],
              datasets: [
                {
                  label: 'Sales',
                  data: [10, 100, 80, 40],
                  backgroundColor: ['rgb(235, 28, 73)', 'rgb(21, 224, 51)', 'rgb(8, 148, 241)', 'rgb(201, 203, 207)'],
                  borderWidth: 2,
                  hoverOffset: 5,
                },
              ],
            },
            options: {
              plugins: {
                legend: { display: true, position: 'right', title: { text: 'Legend', display: true } },
              },
            },
          }),
          // Chart widget (line)
          new CoreDashboardWidgetChart({
            id: '7',
            type: 'chart',
            title: 'Chart (Line)',
            chartType: 'line',
            x: 7,
            width: 5,
            height: 2,
            data: {
              labels: ['Jan', 'Feb', 'March', 'Apr'],
              datasets: [
                {
                  label: 'Revenue',
                  data: [50, 60, 70, 90],
                  borderColor: '#2600FF',
                  backgroundColor: '#2600FF',
                  borderWidth: 2,
                  tension: 0.4,
                  pointBackgroundColor: [
                    '#FF6384', // Color of the points 1
                    '#36A2EB', // Color of the points 2
                    '#FFCE56',
                    '#4BC0C0',
                  ],
                  pointBorderColor: '#FFFFFF',
                  pointBorderWidth: 2,
                },
              ],
            },
            options: {
              plugins: {
                legend: {
                  display: true,
                  position: 'top',
                  labels: {
                    color: '#FFFFFF', // Color of the text in the legend
                  },
                },
              },
              scales: {
                x: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: 'Months',
                  },
                  grid: {
                    color: '#6D6D6D', // Grid color for X axis
                  },
                },
                y: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: 'Revenue',
                  },
                  grid: {
                    color: '#6D6D6D', // Grid color for Y axis
                  },
                },
              },
            },
            onRemoveClick: (widget) => {
              this.removeWidget(widget);
            },
          }),
        ],
        breakpoints: [
          { w: 1200, c: 7, layout: 'compact' },
          { w: 900, c: 5, layout: 'compact' },
          { w: 700, c: 4, layout: 'compact' },
          { w: 600, c: 1, layout: 'compact' },
        ],
      }),
    );

    setInterval(() => {
      // Random number between 0 and 99
      const randomValueOne = Math.floor(Math.random() * 100);
      const randomValueTwo = Math.floor(Math.random() * 100);

      const widgetText = this.dashboard().widgets.find((widget) => widget.id === '1') as CoreDashboardWidgetText;
      const widgetChartDoughnut = this.dashboard().widgets.find(
        (widget) => widget.id === '6',
      ) as CoreDashboardWidgetChart;
      const widgetChartLine = this.dashboard().widgets.find((widget) => widget.id === '7') as CoreDashboardWidgetChart;

      if (widgetText) {
        widgetText.setWidgetTextValue({
          values: {
            text: `Text content ${randomValueOne}`,
            color: randomValueOne > 50 ? 'green' : 'yellow',
          },
        });
      }
      if (widgetChartDoughnut && widgetChartLine) {
        const data = {
          labels: ['Jan', 'Feb', 'March', 'Apr'],
          datasets: [
            {
              data: [10, randomValueOne, 80, randomValueTwo],
            },
          ],
        };
        widgetChartDoughnut.setNewData(data);
        widgetChartLine.setNewData(data);
      }
    }, 3000);
  }

  // [ Private ]

  private async onSettingsButton(): Promise<void> {
    // Open the  dialog
    const success = await this._service.showConfigForm();
    if (!success) return;
  }

  private removeWidget(widget: CoreDashboardWidget): void {
    this.dashboard().widgets = this.dashboard().widgets.filter((w) => w.id !== widget.id);

    this._notify.success(`Widget ${widget.title} has been successfully removed.`);
  }

  private loadTableColums(): CoreTableColumn[] {
    const columns: CoreTableColumn[] = [
      CoreTable.ColumnSwitch({
        field: 'isActive',
        label: 'Active',
        reorderable: false,
        resizable: false,
        click: (row: UserTable) => !row.isActive,
      }),
      CoreTable.ColumnString({ field: 'username', label: 'Username' }),
      CoreTable.ColumnString({ field: 'fullName', label: 'Full Name' }),
      CoreTable.ColumnActions({
        actions: [
          { label: 'Edit', click: (row: UserTable) => this._notify.success(`Edit ${row.fullName}`) },
          { label: 'Delete', click: (row: UserTable) => this._notify.success(`Delete ${row.fullName}`) },
        ],
      }),
    ];
    return columns;
  }
}
