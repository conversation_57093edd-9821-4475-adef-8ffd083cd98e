import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, signal } from '@angular/core';
import { CoreCardModule } from '../../../../../core/components/core-card';
import { CoreProgressBarModule } from '../../../../../core/components/core-progress-bar/core-progress-bar.module';
import { CoreInputSlider, CoreInputsModule } from '../../../../../core/components/core-inputs';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-progress-bar-examples',
  templateUrl: './progress-bar-examples.html',
  styleUrl: './progress-bar-examples.scss',
  host: { class: 'view-component' },
  imports: [CoreCardModule, CoreProgressBarModule, CoreInputsModule],
})
export class ProgressBarExamples implements OnInit, OnDestroy {
  public slider = new CoreInputSlider({
    label: 'Change Value',
    min: 0,
    max: 100,
    smallStep: 1,
    largeStep: 25,
    value: 0,
  });

  public progressBarValue = signal<number>(0);
  public progressBarLabelFormatFn = (value: number): string => {
    if (value === 100) {
      return 'Successfully uploaded all files!';
    } else {
      return `Uploaded ${value} files of 100`;
    }
  };

  private subscriptions$ = new Subscription();

  public ngOnInit(): void {
    this.subscriptions$.add(this.slider.valueChanges.subscribe((value) => this.progressBarValue.set(value as number)));
  }

  public ngOnDestroy(): void {
    this.subscriptions$.unsubscribe();
  }
}
