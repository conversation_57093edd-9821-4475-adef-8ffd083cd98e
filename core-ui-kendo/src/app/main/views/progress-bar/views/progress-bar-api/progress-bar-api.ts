import { Component } from '@angular/core';
import { ApiViewComponent } from '../../../../components/api-view/api-view.component';
import { APIInputData } from '../../../../components/api-view/api-view.interfaces';
import { CORE_PROGRESS_BAR_API_INPUTS } from './progress-bar-api.contants';

@Component({
  selector: 'app-progress-bar-api',
  templateUrl: './progress-bar-api.html',
  styleUrl: './progress-bar-api.scss',
  host: { class: 'view-component' },
  imports: [ApiViewComponent],
})
export class ProgressBarApi {
  public inputsData: APIInputData[] = CORE_PROGRESS_BAR_API_INPUTS;
}
