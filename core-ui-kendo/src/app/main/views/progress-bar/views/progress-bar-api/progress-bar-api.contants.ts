import { APIInputData } from '../../../../components/api-view/api-view.interfaces';

export const CORE_PROGRESS_BAR_VALUE_INPUT: APIInputData = {
  name: 'value',
  type: 'number',
  default: 'No default value',
  description: 'This input is required',
};

export const CORE_PROGRESS_BAR_MIN_INPUT: APIInputData = {
  name: 'min',
  type: 'number',
  default: '0',
  description: 'The maximum value of the Progress Bar',
};

export const CORE_PROGRESS_BAR_MAX_INPUT: APIInputData = {
  name: 'max',
  type: 'number',
  default: '100',
  description: 'The minimum value of the Progress Bar',
};

export const CORE_PROGRESS_BAR_LABEL_VISIBLE_INPUT: APIInputData = {
  name: 'labelVisible',
  type: 'boolean',
  default: 'true',
  description: 'Shows or hides the label of the Progress Bar',
};

export const CORE_PROGRESS_BAR_LABEL_FORMAT_INPUT: APIInputData = {
  name: 'labelFormat',
  type: `'value' | 'percent' | (value: number) => string`,
  default: 'true',
  description: 'Formatting for the label, see example for details',
};

export const CORE_PROGRESS_BAR_LABEL_POSITION_INPUT: APIInputData = {
  name: 'labelPosition',
  type: `'start' | 'end' | 'center'`,
  default: `'center'`,
  description: '',
};

export const CORE_PROGRESS_BAR_INDETERMINATE_INPUT: APIInputData = {
  name: 'indeterminate',
  type: 'boolean',
  default: 'false',
  description: 'Shows an indeterminate progress bar state if set to true',
};

export const CORE_PROGRESS_BAR_API_INPUTS: APIInputData[] = [
  CORE_PROGRESS_BAR_VALUE_INPUT,
  CORE_PROGRESS_BAR_MIN_INPUT,
  CORE_PROGRESS_BAR_MAX_INPUT,
  CORE_PROGRESS_BAR_LABEL_VISIBLE_INPUT,
  CORE_PROGRESS_BAR_LABEL_FORMAT_INPUT,
  CORE_PROGRESS_BAR_LABEL_POSITION_INPUT,
  CORE_PROGRESS_BAR_INDETERMINATE_INPUT,
];
