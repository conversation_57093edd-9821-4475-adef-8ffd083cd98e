import { Component, signal, Signal } from '@angular/core';
import { CoreNavigationTabs, CoreNavigationTabsModule } from '../../../core/components/core-navigation-tabs';

@Component({
  selector: 'app-progress-bar',
  templateUrl: './progress-bar.component.html',
  styleUrl: './progress-bar.component.scss',
  host: { class: 'view-component' },
  imports: [CoreNavigationTabsModule],
})
export class ProgressBarComponent {
  public navigationTabs: Signal<CoreNavigationTabs>;

  constructor() {
    this.navigationTabs = signal(
      new CoreNavigationTabs({
        tabs: [
          { label: 'Progress Bar Examples', route: 'progress-bar-examples' },
          { label: 'API', route: 'progress-bar-api' },
        ],
        autoNavigate: true,
      }),
    );
  }
}
