// Angular
import { Component, inject, signal } from '@angular/core';
import { ActivatedRoute, Router, RouterOutlet } from '@angular/router';
// Core UI
import { CoreButtonModule } from '../../../../../core/components/core-button';
import { CoreIconModule } from '../../../../../core/components/core-icon';
import { CoreBreadcrumbsComponent } from '../../../../../core/components/core-breadcrumbs';
// Interfaces
export interface View {
  label: string;
  route: string;
}

@Component({
  selector: 'app-breadcrumbs-example-1',
  templateUrl: './breadcrumbs-example-1.html',
  styleUrl: './breadcrumbs-example-1.scss',
  host: { class: 'view-component' },
  imports: [CoreBreadcrumbsComponent, CoreButtonModule, RouterOutlet, CoreIconModule],
})
export class BreadcrumbsExample1Component {
  public views = signal<View[]>(this.getViews());

  private _router = inject(Router);
  private _activatedRoute = inject(ActivatedRoute);

  public async selectView(view: View): Promise<void> {
    await this._router.navigate([view.route], { relativeTo: this._activatedRoute });
  }

  private getViews(): View[] {
    return [
      {
        label: 'Dashboard',
        route: 'bc-e1-dashboard',
      },
      {
        label: 'Settings',
        route: 'bc-e1-settings',
      },
    ];
  }
}
