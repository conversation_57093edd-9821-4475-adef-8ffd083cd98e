import { Component, inject, signal } from '@angular/core';
import { ActivatedRoute, Router, RouterOutlet } from '@angular/router';
import { CoreButtonModule } from '../../../../../../../core/components/core-button';
import { View } from '../../breadcrumbs-example-1';

@Component({
  selector: 'app-bc-e1-settings',
  templateUrl: './bc-e1-settings.html',
  styleUrl: './bc-e1-settings.scss',
  host: { class: 'view-component' },
  imports: [CoreButtonModule, RouterOutlet],
})
export class BcE1Settings {
  public views = signal<View[]>([
    { label: 'Personal Settings', route: 'bc-e1-personal-settings' },
    { label: 'System Settings', route: 'bc-e1-system-settings' },
  ]);

  private _router = inject(Router);
  private _activatedRoute = inject(ActivatedRoute);

  public async selectView(view: View): Promise<void> {
    await this._router.navigate([view.route], { relativeTo: this._activatedRoute });
  }

  public async navigateTo(route: string): Promise<void> {
    await this._router.navigate([route], { relativeTo: this._activatedRoute });
  }
}
