<app-api-view [title]="'Core Breadcrumbs Component'" [selectorName]="'core-breadcrumbs'" [customContent]="true">
  <h2>How to use the Core Breadcrumbs Component</h2>
  <ol>
    <li>Import the CoreBreadcrumbsComponent in your view module (Required)</li>
    <li>Add the CoreBreadcrumbsComponent to your template (Required)</li>
    <li>
      Provide breadcrumb data for each view (Optional). See example 1 view and Router configuration. If breadcrumb data
      is not provided, the component will try to generate it from the route path.
    </li>
  </ol>
  <h3>Note</h3>
  <p>The component is intended to be used with router-outlet, see example 1.</p>
</app-api-view>
