import { Component, Signal, signal } from '@angular/core';
import { CoreNavigationTabs, CoreNavigationTabsModule } from '../../../core/components/core-navigation-tabs';

@Component({
  selector: 'app-breadcrumbs',
  templateUrl: './breadcrumbs.html',
  styleUrl: './breadcrumbs.scss',
  host: { class: 'view-component' },
  imports: [CoreNavigationTabsModule],
})
export class BreadcrumbsComponent {
  public navigationTabs: Signal<CoreNavigationTabs>;

  constructor() {
    this.navigationTabs = signal(
      new CoreNavigationTabs({
        tabs: [
          { label: 'Breadcrums Example 1', route: 'breadcrumbs-example-1' },
          { label: 'API', route: 'breadcrumbs-api' },
        ],
        autoNavigate: true,
      }),
    );
  }
}
