// Angular
import { Component, inject } from '@angular/core';
// Core UI
import { CoreForm, CoreFormValidators, CoreFormModule } from '../../../../../core/components/core-form';
import { CoreInputValidators } from '../../../../../core/components/core-inputs';
import { CoreNotificationService, CoreNotificationModule } from '../../../../../core/popups/core-notification';
// Constants
import { FORM_EXAMPLE_2_LAYOUT } from './form-example-2.constants';
import { CoreButtonModule } from '../../../../../core/components/core-button';
import { CoreCardModule } from '../../../../../core/components/core-card';
import { CoreDropDownButtonModule } from '../../../../../core/components/core-dropdown-button';

@Component({
  selector: 'app-form-example-2',
  imports: [CoreCardModule, CoreFormModule, CoreDropDownButtonModule, CoreButtonModule, CoreNotificationModule],
  templateUrl: './form-example-2.component.html',
  styleUrl: './form-example-2.component.scss',
  host: { class: 'view-component' },
})
export class FormExample2Component {
  public form = this.getForm();
  public formActions = this.getFormActions();
  // Dependencies
  private _notify = inject(CoreNotificationService);

  // [ Events ]

  public setFormValue(): void {
    this.form.setValueAsObject({
      firstName: 'Micky',
      lastName: 'Mouse',
      phone: '1234567890',
      date: new Date(2024, 11, 31),
      dateTime: new Date(2026, 11, 31, 23, 59, 59),
      time: new Date(),
      password: 'password',
      confirmPassword: 'password',
      otp: '1234',
      select: 'option2',
      multiselect: [1, 3],
      dropdownSelect: 'option2',
      radioSelect: '2',
      email: {
        emails: ['<EMAIL>', '<EMAIL>'],
      },
      newField: 'Text 123',
    });
  }

  // Internal

  private toggleDisabledState(): void {
    if (this.form.disabled) {
      this.form.enable();
    } else {
      this.form.disable();
    }
  }

  private clearForm(): void {
    this.form.reset();
  }

  private removeNewControl(): void {
    this.form.removeControlByName('newField');
  }

  private addNewElement(): void {
    const newControl = CoreForm.Text({ name: 'newField', label: 'New Field', placeholder: 'Enter text here...' });
    this.form.addControl(newControl);
  }

  private onAdminSelected(): void {
    const newControl = CoreForm.Text({
      name: 'adminUsername',
      label: 'Admin Username',
      placeholder: 'Enter username here...',
    });
    this.form.addControl(newControl);

    const group = this.form.getLayoutGroupByControlName('radioSelect');

    group?.items.splice(-2, 1, {
      controlName: 'radioSelect',
      style: {
        border: '1px solid gray',
        padding: '2px',
      },
    });
  }

  private onAdminDeselected(): void {
    this.form.removeControlByName('adminUsername');
    const group = this.form.getLayoutGroupByControlName('radioSelect');
    group?.items.splice(-2, 1, 'radioSelect');
  }

  private getForm(): CoreForm {
    return new CoreForm(
      [
        CoreForm.Text({ name: 'firstName', label: 'First Name', info: { text: 'Text info', title: 'Title info' } }),
        CoreForm.Text({ name: 'lastName', label: 'Last Name', optional: true }),
        CoreForm.MaskedText({ label: 'Phone Number', name: 'phone', mask: '(###) 000-0000' }),
        CoreForm.Date({ name: 'date', label: 'Date' }),
        CoreForm.DateTime({ name: 'dateTime', label: 'Date Time' }),
        CoreForm.Time({ name: 'time', label: 'Time' }),
        CoreForm.Password({
          name: 'password',
          label: 'Password',
          validators: [CoreInputValidators.required('Password is required.')],
        }),
        CoreForm.Password({
          name: 'confirmPassword',
          label: 'Password (confirm)',
        }),
        CoreForm.Otp({
          name: 'otp',
          label: 'OTP',
        }),
        CoreForm.Select({
          name: 'select',
          label: 'Select',
          options: [
            { value: 'option1', label: 'Option 1' },
            { value: 'option2', label: 'Option 2' },
            { value: 'option3', label: 'Option 3' },
            { value: 'option4', label: 'Option 4' },
          ],
        }),
        CoreForm.Multiselect({
          name: 'multiselect',
          label: 'Multiselect',
          checkboxes: true,
          options: [
            { value: 1, label: 'Option 1' },
            { value: 2, label: 'Option 2' },
            { value: 3, label: 'Option 3' },
            { value: 4, label: 'Option 4' },
          ],
        }),
        CoreForm.DropdownSelect({
          name: 'dropdownSelect',
          label: 'DropdownSelect',
          filterable: true,
          options: [
            { value: 'option1', label: 'Option 1' },
            { value: 'option2', label: 'Option 2' },
            { value: 'option3', label: 'Option 3' },
            { value: 'option4', label: 'Option 4' },
          ],
          validators: [CoreInputValidators.required('This field is required.')],
        }),
        CoreForm.RadioSelect({
          name: 'radioSelect',
          label: 'Radio Select',
          options: [
            { value: '1', label: 'Administrator' },
            { value: '2', label: 'Supervisor' },
            { value: '3', label: 'Staff' },
            { value: '4', label: 'Guest' },
          ],
          onValueChange: (value) => {
            if (value === '1') this.onAdminSelected();
            else this.onAdminDeselected();
          },
        }),
        CoreForm.Email({
          name: 'email',
          label: 'Email',
          contacts: [
            { value: '1', label: 'John Smith' },
            { value: '2', label: 'Anne Lawrence' },
          ],
          value: {
            contacts: ['1'],
            emails: ['<EMAIL>', '<EMAIL>'],
          },
        }),
      ],
      {
        validators: [CoreFormValidators.password('password', 'confirmPassword')],
        focused: true,
        labelPosition: 'top',
        layout: FORM_EXAMPLE_2_LAYOUT,
      },
    );
  }

  private getFormActions() {
    return [
      { label: 'Set Form Value', click: () => this.setFormValue() },
      { label: 'Clear Form', click: () => this.clearForm() },
      { label: 'Add New Control', click: () => this.addNewElement() },
      { label: 'Remove New Control', click: () => this.removeNewControl() },
      { label: 'Toggle Disabled State', click: () => this.toggleDisabledState() },
    ];
  }
}
