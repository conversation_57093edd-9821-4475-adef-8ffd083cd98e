:host {
  display: flex;
  flex-direction: row;
  align-items: start;
  justify-content: center;
  padding: 0.5rem;
  gap: 0.8rem;
  overflow: auto;
}

.card {
  // width: 400px;
  box-shadow: var(--kendo-elevation-2);
}

core-card-header {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

core-card-footer {
  display: flex;
  justify-content: space-between;

  & core-button {
    min-width: 6rem;
  }
}
