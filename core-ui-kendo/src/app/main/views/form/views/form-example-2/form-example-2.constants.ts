import { CoreFormLayoutGroup } from '../../../../../core/components/core-form/components/core-form-layout-group/core-form-layout-group.interfaces';

export const FORM_EXAMPLE_2_LAYOUT: CoreFormLayoutGroup = {
  layout: 'horizontal',
  style: {
    'align-items': 'flex-start',
  },
  items: [
    {
      label: 'Personal Information',
      items: [
        {
          items: ['password', 'confirmPassword'],
          borderStyle: '1px solid red',
          style: {
            padding: '0.35em 0.75em 0.625em 0.75em',
          },
        },
        'email',
        'phone',
        'newField',
        {
          label: 'Full Name',
          layout: 'horizontal',
          style: {
            'align-items': 'center',
            'justify-content': 'flex-end',
            'flex-wrap': 'nowrap',
          },
          items: [
            { controlName: 'firstName', style: { 'flex-basis': '50%' } },
            { controlName: 'lastName', style: { 'flex-basis': '50%' } },
          ],
        },
        'otp',
      ],
    },
    {
      label: 'Date & Time',
      items: [
        { label: 'Dates', items: ['date', 'dateTime'] },
        {
          label: 'Time',
          items: ['time'],
        },
      ],
    },
    {
      label: 'Selects',
      borderStyle: '1px solid blue',
      items: ['select', 'multiselect', 'dropdownSelect', 'radioSelect', 'adminUsername'],
    },
  ],
};
