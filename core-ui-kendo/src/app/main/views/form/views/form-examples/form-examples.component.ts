// Angular
import { Component, inject, On<PERSON><PERSON>roy, OnInit, signal } from '@angular/core';
import { Subscription } from 'rxjs';
import { CommonModule } from '@angular/common';
// Core UI
import { CoreButtonModule } from '../../../../../core/components/core-button';
import { CoreCardModule } from '../../../../../core/components/core-card';
import { CoreDropDownButtonModule } from '../../../../../core/components/core-dropdown-button';
import { CoreForm, CoreFormModule, CoreFormValidators } from '../../../../../core/components/core-form';
import { CoreInputValidators } from '../../../../../core/components/core-inputs';
import { CoreNotificationModule, CoreNotificationService } from '../../../../../core/popups/core-notification';

@Component({
  selector: 'app-form-examples',
  templateUrl: './form-examples.component.html',
  styleUrl: './form-examples.component.scss',
  host: { class: 'view-component' },
  imports: [
    CoreCardModule,
    CoreFormModule,
    CoreDropDownButtonModule,
    CoreButtonModule,
    CoreNotificationModule,
    CommonModule,
  ],
})
export class FormExamplesComponent implements OnInit, OnDestroy {
  public form = this.getForm();
  public formActions = this.getFormActions();
  // Signals
  public formValue = signal({});
  public isValid = signal(false);
  // Dependencies
  private _notify = inject(CoreNotificationService);

  private _subscription$ = new Subscription();

  public ngOnInit(): void {
    // Subscribe to form value changes
    this._subscription$.add(
      this.form.valueChanges.subscribe(() => {
        this.formValue.set(this.form.getValueAsObject());
      }),
    );
    // Subscribe to form status changes
    this._subscription$.add(
      this.form.statusChanges.subscribe(() => {
        this.isValid.set(this.form.valid);
      }),
    );
  }

  // [ Events ]

  public setFormValue(): void {
    this.form.setValueAsObject({
      firstName: 'Micky',
      lastName: 'Mouse',
      phone: '1234567890',
      colorPicker: '#ff0000',
      dateTime: new Date(2026, 11, 31, 23, 59, 59),
      date: new Date(2024, 11, 31),
      time: new Date(),
      age: 28,
      password: 'password',
      confirmPassword: 'password',
      select: 'option2',
      multiselect: [1, 3],
      dropdownSelect: 'option2',
      checkbox: true,
      switch: false,
      fileSelect: [
        { uid: 'abc', name: 'file1.txt', size: 1024 },
        { name: 'image1.png', size: 512 },
      ],
      textEditor: 'The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from Japan.',
      description: 'This is a description.',
      newField: 'Text 123',
      cron: '0 12 * * 1',
      radioSelect: '2',
      otp: '1234',
      slider: 65,
      email: {
        emails: ['<EMAIL>', '<EMAIL>'],
      },
    });
  }

  // Internal

  private toggleDisabledState(): void {
    if (this.form.disabled) {
      this.form.enable();
    } else {
      this.form.disable();
    }
  }

  private clearForm(): void {
    this.form.reset();
  }

  private removeSecondControl(): void {
    if (this.form.controls.length > 1) {
      this.form.removeAt(1);
    }
  }

  private addNewElement(): void {
    const newControl = CoreForm.Text({ name: 'newField', label: 'New Field', placeholder: 'Enter text here...' });
    this.form.insert(1, newControl);
  }

  private getForm(): CoreForm {
    return new CoreForm(
      [
        CoreForm.Text({ name: 'firstName', label: 'First Name', info: { text: 'Text info', title: 'Title info' } }),
        CoreForm.Text({ name: 'lastName', label: 'Last Name', optional: true }),
        CoreForm.Numeric({ name: 'age', label: 'Age', decimals: 0 }),
        CoreForm.MaskedText({ label: 'Phone Number', name: 'phone', mask: '(###) 000-0000' }),
        CoreForm.Date({ name: 'date', label: 'Date' }),
        CoreForm.DateTime({ name: 'dateTime', label: 'Date Time' }),
        CoreForm.Time({ name: 'time', label: 'Time' }),
        CoreForm.Password({
          name: 'password',
          label: 'Password',
          validators: [CoreInputValidators.required('Password is required.')],
        }),
        CoreForm.Password({
          name: 'confirmPassword',
          label: 'Password (confirm)',
        }),
        CoreForm.Otp({
          name: 'otp',
          label: 'OTP',
        }),
        CoreForm.Select({
          name: 'select',
          label: 'Select',
          options: [
            { value: 'option1', label: 'Option 1' },
            { value: 'option2', label: 'Option 2' },
            { value: 'option3', label: 'Option 3' },
            { value: 'option4', label: 'Option 4' },
          ],
        }),
        CoreForm.Multiselect({
          name: 'multiselect',
          label: 'Multiselect',
          checkboxes: true,
          options: [
            { value: 1, label: 'Option 1' },
            { value: 2, label: 'Option 2' },
            { value: 3, label: 'Option 3' },
            { value: 4, label: 'Option 4' },
          ],
        }),
        CoreForm.DropdownSelect({
          name: 'dropdownSelect',
          label: 'DropdownSelect',
          filterable: true,
          options: [
            { value: 'option1', label: 'Option 1' },
            { value: 'option2', label: 'Option 2' },
            { value: 'option3', label: 'Option 3' },
            { value: 'option4', label: 'Option 4' },
          ],
          validators: [CoreInputValidators.required('This field is required.')],
        }),
        CoreForm.Checkbox({
          name: 'checkbox',
          label: 'Select to receive notifications',
        }),
        CoreForm.RadioSelect({
          name: 'radioSelect',
          label: 'Radio Select',
          options: [
            { value: '1', label: 'Administrator' },
            { value: '2', label: 'Supervisor' },
            { value: '3', label: 'Staff' },
            { value: '4', label: 'Guest' },
          ],
        }),
        CoreForm.Slider({
          name: 'slider',
          label: 'Slider',
          min: 0,
          max: 100,
          smallStep: 5,
          largeStep: 10,
          showButtons: true,
          showNumericInput: false,
          validators: [CoreInputValidators.required('This field is required.')],
        }),
        CoreForm.Cron({ name: 'cron', label: 'Cron Expression', modes: ['Daily', 'Monthly', 'Weekly'] }),
        CoreForm.Switch({ name: 'switch', label: 'Enable notifications' }),
        CoreForm.ColorPicker({ name: 'colorPicker', label: 'Color Picker' }),
        CoreForm.FileSelect({
          name: 'fileSelect',
          label: 'File Select',
          disabled: false,
          onFilesSelect: (files) => {
            this._notify.success(`Select: ${JSON.stringify(files, null, 2)}`, { duration: 5000 });
          },
          onFileRemove: (file) => {
            this._notify.success(`Remove: ${JSON.stringify(file, null, 2)}`, { duration: 5000 });
          },
          onFileClick: (file) => {
            this._notify.success(`Click: ${JSON.stringify(file, null, 2)}`, { duration: 5000 });
          },
        }),
        CoreForm.TextEditor({
          name: 'textEditor',
          label: 'Editor',
          insertOptions: [{ label: 'Current Date', text: new Date().toLocaleDateString() }],
        }),
        CoreForm.Textarea({ name: 'description', label: 'Description', rows: 5 }),
        CoreForm.Email({
          name: 'email',
          label: 'Email',
          contacts: [
            { value: '1', label: 'John Smith' },
            { value: '2', label: 'Anne Lawrence' },
          ],
          value: {
            contacts: ['1'],
            emails: ['<EMAIL>', '<EMAIL>'],
          },
        }),
      ],
      {
        validators: [CoreFormValidators.password('password', 'confirmPassword')],
        focused: true,
        labelPosition: 'top',
      },
    );
  }

  private getFormActions() {
    return [
      { label: 'Set Form Value', click: () => this.setFormValue() },
      { label: 'Add New Control', click: () => this.addNewElement() },
      { label: 'Remove 2nd Control', click: () => this.removeSecondControl() },
      { label: 'Toggle Disabled State', click: () => this.toggleDisabledState() },
      { label: 'Clear Form', click: () => this.clearForm() },
    ];
  }

  public ngOnDestroy(): void {
    this._subscription$.unsubscribe();
  }
}
