import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';

import { ButtonTestingComponent } from './button-testing.component';

describe('ButtonTestingComponent', () => {
  let view: ButtonTestingComponent;
  let fixture: ComponentFixture<ButtonTestingComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ButtonTestingComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(ButtonTestingComponent);
    view = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create view component', () => {
    expect(view).toBeTruthy();
  });

  it('should find the create user button in the DOM', () => {
    const button = fixture.debugElement.query(By.css('#create-user-btn'));
    expect(button).toBeTruthy();
  });

  it('create user button click should call onCreateUser function', () => {
    const createUserSpy = spyOn(view, 'onCreateUser');
    const button = fixture.debugElement.query(By.css('#create-user-btn'));
    button.triggerEventHandler('click', null);
    expect(createUserSpy).toHaveBeenCalled();
  });
});
