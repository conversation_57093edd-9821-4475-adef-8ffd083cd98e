// Angular
import { Component, Signal, signal } from '@angular/core';
// Core UI
import { CoreNavigationTabs, CoreNavigationTabsModule } from '../../../core/components/core-navigation-tabs';

@Component({
  selector: 'app-tables',
  templateUrl: './tables.component.html',
  styleUrl: './tables.component.scss',
  host: { class: 'view-component' },
  imports: [CoreNavigationTabsModule],
})
export class TablesComponent {
  public navigationTabs: Signal<CoreNavigationTabs>;

  constructor() {
    this.navigationTabs = signal(
      new CoreNavigationTabs({
        tabs: [
          { label: 'Simple Table (Example 1)', route: 'table-example-1' },
          { label: 'Complex Table (Example 2)', route: 'table-example-2' },
          { label: 'Complex Table (Example 3)', route: 'table-example-3' },
          { label: 'Draggable Table (Example 4)', route: 'table-example-4' },
          { label: 'Search Bar Table (Example 5)', route: 'table-example-5' },
          { label: 'Responsive Table (Example 6)', route: 'table-example-6' },
          { label: 'API', route: 'table-api' },
        ],
        autoNavigate: true,
      }),
    );
  }
}
