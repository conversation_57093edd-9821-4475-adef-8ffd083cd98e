// Angular
import { inject, Injectable } from '@angular/core';
// Services
import { TableExample3ApiService } from './table-example-3.api.service';
// Core UI
import { CoreDialogConfirmService } from '../../../../../core/popups/core-dialog-confirm';
import { CoreDialogFormService } from '../../../../../core/popups/core-dialog-form';
import { CoreForm } from '../../../../../core/components/core-form';
import { CoreInputValidators } from '../../../../../core/components/core-inputs';
// Interfaces
import { User, UserUpdateForm, UserUpdateModel } from '../table-example-4/table-example-4.interfaces';

@Injectable()
export class TableExample3Service {
  // Dependencies
  private _apiService = inject(TableExample3ApiService);
  private _dialogConfirm = inject(CoreDialogConfirmService);
  private _dialogForm = inject(CoreDialogFormService);

  public async toggleUserActiveState(user: User): Promise<boolean> {
    // Confirm the user wants to change the active state
    const actionName = user.isActive ? 'Deactivate' : 'Activate';
    const dialogResult = await this._dialogConfirm.open({
      title: `${actionName} User Account`,
      message: `Are you sure you would like to ${actionName.toLowerCase()} the user account for "${user.fullName}"?`,
      actionLabel: user.isActive ? 'Deactivate' : 'Activate',
      onAction: () => {
        return new Promise((res) => res(this._apiService.setUserIsActive(user.id, !user.isActive)));
      },
    });

    // Return true if the user confirmed the action
    return dialogResult === 'primary';
  }

  public async updateUserDialog(model: User): Promise<boolean> {
    // Create the form
    const form = this._createForm(model);

    // Open the dialog
    const dialogResult = await this._dialogForm.open({
      title: 'Update User',
      form,
      actionLabel: 'Update',
      onAction: () => {
        const rawData: UserUpdateForm = form.getValueAsObject();
        const formData: UserUpdateModel = {
          ...rawData,
          id: model.id,
          role: { value: rawData.role, label: rawData.role },
        };
        return new Promise((res) => res(this._apiService.updateUser(formData)));
      },
    });

    // Return true if the user successfully created the mileage cost
    return dialogResult != null;
  }

  public async deleteUserDialog(user: User): Promise<boolean> {
    // Confirm the user wants to change the active state
    const dialogResult = await this._dialogConfirm.open({
      title: 'Delete User',
      message: `Are you sure you would like to delete the user ${user.fullName}?`,
      actionLabel: 'Delete',
      onAction: () => {
        return new Promise((res) => res(this._apiService.deleteUser(user.id)));
      },
    });

    // Return true if the user confirmed the action
    return dialogResult === 'primary';
  }

  public _createForm(existingUser?: User): CoreForm {
    const formFields = [
      CoreForm.Text({
        name: 'fullName',
        label: 'Full Name',
        value: existingUser?.fullName ?? '',
        validators: [CoreInputValidators.required('Full Name is required.')],
      }),
      CoreForm.Text({
        name: 'email',
        label: 'Email',
        value: existingUser?.email ?? '',
        validators: [
          CoreInputValidators.required('Email is required.'),
          CoreInputValidators.email('Must be a valid email address.'),
        ],
      }),
      CoreForm.Select({
        name: 'role',
        label: 'Role',
        value: existingUser?.role.value ?? '',
        options: [
          { value: 'Admin', label: 'Admin' },
          { value: 'Manager', label: 'Manager' },
          { value: 'User', label: 'User' },
          { value: 'Guest', label: 'Guest' },
        ],
        validators: [CoreInputValidators.required('Role is required.')],
      }),
      CoreForm.Switch({
        name: 'isActive',
        label: 'Active',
        value: existingUser ? existingUser.isActive : false,
      }),
    ];
    const form = new CoreForm(formFields, { focused: true });

    return form;
  }
}
