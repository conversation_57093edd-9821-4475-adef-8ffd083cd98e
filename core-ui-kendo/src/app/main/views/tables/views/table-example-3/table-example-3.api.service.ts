// Angular
import { Injectable } from '@angular/core';
// Core UI
import { UserTable, UserUpdateModel } from '../table-example-4/table-example-4.interfaces';
import { CoreTablePageableData, CoreTableState } from '../../../../../core/components/core-table';

@Injectable()
export class TableExample3ApiService {
  private _UsersData: UserTable[] = [
    {
      id: '1',
      isActive: true,
      fullName: '<PERSON>',
      email: '<EMAIL>',
      role: { value: 'Admin', label: 'Admin' },
    },
    {
      id: '2',
      isActive: false,
      fullName: '<PERSON>',
      email: '<EMAIL>',
      role: { value: 'Manager', label: 'Manager' },
    },
    {
      id: '3',
      isActive: true,
      fullName: '<PERSON>',
      email: '<EMAIL>',
      role: { value: 'User', label: 'User' },
    },
    {
      id: '4',
      isActive: false,
      fullName: '<PERSON>',
      email: '<EMAIL>',
      role: { value: 'Guest', label: 'Guest' },
    },
    {
      id: '5',
      isActive: true,
      fullName: '<PERSON>',
      email: '<EMAIL>',
      role: { value: 'User', label: 'User' },
    },
  ];

  public roleOptions = [
    { value: 'Admin', label: 'Admin' },
    { value: 'Manager', label: 'Manager' },
    { value: 'User', label: 'User' },
    { value: 'Guest', label: 'Guest' },
  ];

  public getUsersPageable(state: CoreTableState): CoreTablePageableData<UserTable> {
    const sortedData = [...this._UsersData];

    // Apply sorting
    const sorting = state.sort();
    if (sorting) {
      sortedData.sort((a, b) => {
        const field = sorting.field as keyof UserTable;
        const direction = sorting.direction === 'ASC' ? 1 : -1;
        return a[field] > b[field] ? direction : a[field] < b[field] ? -direction : 0;
      });
    }

    const pagedData = sortedData.slice(state.paging()?.skip, (state.paging()?.skip ?? 0) + (state.paging()?.take ?? 0));
    return { data: pagedData, total: sortedData.length };
  }

  public deleteUser(id: string): boolean {
    this._UsersData = this._UsersData.filter((user) => user.id !== id);
    return true;
  }

  public setUserIsActive(id: string, isActive: boolean): boolean {
    const user = this._UsersData.find((user) => user.id === id);
    if (!user) return false;

    user.isActive = isActive;
    return true;
  }

  public updateUser(updatedUser: UserUpdateModel): boolean {
    const index = this._UsersData.findIndex((user) => user.id === updatedUser.id);
    if (index === -1) return false;

    this._UsersData[index] = { ...this._UsersData[index], ...updatedUser };
    return true;
  }

  public getUserById(id: string): UserTable | null {
    return this._UsersData.find((user) => user.id === id) ?? null;
  }
}
