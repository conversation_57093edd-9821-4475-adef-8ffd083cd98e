// Angular
import { Component, inject, OnInit, signal, Signal, TemplateRef, viewChild } from '@angular/core';
import { Subscription } from 'rxjs';
import { <PERSON>sonPipe } from '@angular/common';
// Core UI
import { CoreTable, CoreTableModule, CoreTableState } from '../../../../../core/components/core-table';
import { CoreCardModule } from '../../../../../core/components/core-card';
import { CoreNotificationModule, CoreNotificationService } from '../../../../../core/popups/core-notification';
import { CoreDialogConfirmModule } from '../../../../../core/popups/core-dialog-confirm';
import { CoreDialogFormModule } from '../../../../../core/popups/core-dialog-form';
// Kenod UI
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';
// Services
import { TableExample3ApiService } from './table-example-3.api.service';
import { TableExample3Service } from './table-example-3.service';
// Interfaces
import { User, UserTable } from '../table-example-4/table-example-4.interfaces';
import { CoreTableContextMenuOptions } from '../../../../../core/components/core-table/interfaces/core-table-context-menu-options';
// Icons
import { pencilIcon, trashIcon, userIcon } from '@progress/kendo-svg-icons';

@Component({
  selector: 'app-table-example-3',
  templateUrl: './table-example-3.component.html',
  styleUrl: './table-example-3.component.scss',
  host: { class: 'view-component' },
  providers: [TableExample3ApiService, TableExample3Service],
  imports: [
    CoreTableModule,
    CoreCardModule,
    CoreNotificationModule,
    CoreDialogConfirmModule,
    CoreDialogFormModule,
    JsonPipe,
    DropDownsModule,
  ],
})
export class TableExample3Component implements OnInit {
  // Elements
  public table!: Signal<CoreTable<UserTable>>;
  // Dependencies
  public _api = inject(TableExample3ApiService);
  private _service = inject(TableExample3Service);
  private _notify = inject(CoreNotificationService);
  // View Children
  private _tableTemplate = viewChild.required('tableTemplate', { read: TemplateRef });

  private _subscriptions$ = new Subscription();

  public ngOnInit(): void {
    this.table = signal(
      new CoreTable<UserTable>({
        columns: [
          CoreTable.ColumnSwitch({
            field: 'isActive',
            label: 'Active',
            falseLabel: 'Inactive',
            reorderable: false,
            filter: false,
            resizable: false,
            click: (row: UserTable) => this._setUserActive(row),
          }),
          CoreTable.ColumnString({ field: 'fullName', label: 'Full Name', filter: false, width: 200 }),
          CoreTable.ColumnString({ field: 'email', label: 'Email', showMenu: false, width: 200 }),
          CoreTable.ColumnNumber({
            field: 'role',
            label: 'Role',
            templateRef: this._tableTemplate(),
            showMenu: false,
            width: 200,
          }),
          CoreTable.ColumnActions({
            actions: [
              { label: 'Edit', icon: pencilIcon, click: (row: UserTable) => this.onUpdateUser(row) },
              {
                label: 'Delete',
                icon: trashIcon,
                click: async (row: UserTable) => this.onDeleteUser(row),
              },
            ],
          }),
        ],
        state: {
          sorting: { field: 'fullName', direction: 'ASC' },
          /*
          columns: [
            { field: 'role', visible: true },
            { field: 'fullName', visible: true },
            { field: 'email', visible: true },
            { field: 'isActive', visible: true },
          ]
          */
        },
        pageable: true,
        data: (state: CoreTableState) => {
          const data = this._api.getUsersPageable(state);
          return data;
        },
        showSelectColumn: true,
        showSelectedCount: true,
        showDetails: true,
        reorderable: true,
        customFooter: { type: 'info', position: 'right' },
        resizable: true,
        // contextMenuOptions: [
        //   { id: 1, label: 'Edit', svgIcon: pencilIcon },
        //   { id: '', label: '', separator: true },
        //   { id: 2 , label: 'Delete', svgIcon: trashIcon },
        // ],
        selectable: 'multiple',
        onSelectionChange: (columns) => {
          this._notify.success(`${JSON.stringify(columns, null, 2)}`, { duration: 3000 });
        },
        onDataLoaded: () => {
          this._notify.success(`Data loaded successfully`, { duration: 3000 });
        },
        onColumnsChange: (columns) => {
          this._notify.success(`Columns changed: ${JSON.stringify(columns, null, 2)}`, { duration: 3000 });
        },
        onContextMenu: (row) => {
          return this.getContextMenuOptions(row);
        },
        onContextMenuItemClick: async (item) => {
          await this.onContextMenuItemClick(item);
        },
      }),
    );
    // Subscribe to column changes
    this._subscriptions$.add(
      this.table().onColumnsChange.subscribe((event) => {
        // Filter out the columns that are not visible
        const hiddenColumns = event.filter((column) => !column.visible);

        const hiddenColumnsMessage = hiddenColumns.length
          ? `Hidden columns: ${hiddenColumns.map((column) => column.field).join(', ')}`
          : 'No hidden columns';

        this._notify.success(hiddenColumnsMessage, { duration: 3000 });
      }),
    );
  }

  // [ Private Methods ]

  private async _setUserActive(user: User): Promise<boolean> {
    const success = await this._service.toggleUserActiveState(user);
    if (!success) return user.isActive;

    const updatedUser = this._api.getUserById(user.id);
    if (!updatedUser) return user.isActive;

    user.isActive = updatedUser.isActive;
    this._notify.success(`The user has been successfully ${user.isActive ? 'activated' : 'deactivated'}.`);
    return user.isActive;
  }

  private async onUpdateUser(user: User): Promise<void> {
    const success = await this._service.updateUserDialog(user);
    if (!success) return;

    this.table()?.refresh();
    this._notify.success('The user has been successfully updated.');
  }

  private async onDeleteUser(user: User): Promise<void> {
    const success = await this._service.deleteUserDialog(user);
    if (!success) return;

    this.table()?.refresh();
    this._notify.success('The user has been successfully deleted.');
  }

  private getContextMenuOptions(row: UserTable | null): CoreTableContextMenuOptions[] {
    if (!row)
      return [
        {
          id: 0,
          label: 'No row selected',
        },
      ];
    switch (row?.role.value) {
      case 'Admin':
        return [
          {
            id: 1,
            label: 'Edit',
            svgIcon: pencilIcon,
            children: [
              {
                id: 3,
                label: 'Edit Admin',
                svgIcon: pencilIcon,
              },
            ],
          },
          { id: 2, label: 'Delete Admin', svgIcon: trashIcon },
        ];
      case 'Manager':
        return [
          { id: 4, label: 'Edit Manager', svgIcon: pencilIcon },
          { id: 5, label: 'Delete Manager', svgIcon: trashIcon },
        ];
      case 'User':
        return [{ id: 6, label: 'View User Profile', svgIcon: userIcon }];
      default:
        return [];
    }
  }

  private async onContextMenuItemClick(item: { id: string | number; row: UserTable | null }): Promise<void> {
    if (!item.row) return this._notify.error('No row selected', { duration: 3000 });

    switch (item.id) {
      case 1: // Edit
        await this.onUpdateUser(item.row);
        break;
      case 2: // Delete
        await this.onDeleteUser(item.row);
        break;
      case 3: // Edit Admin
        await this.onUpdateUser(item.row);
        break;
      case 4: // Edit Manager
        await this.onUpdateUser(item.row);
        break;
      case 5: // Delete Manager
        await this.onDeleteUser(item.row);
        break;
      case 6: // View User Profile
        this._notify.success(`Viewing profile for ${item.row.fullName}`, { duration: 3000 });
        break;
      case 0:
        this._notify.error('No row selected', { duration: 3000 });
        break;
      default:
        this._notify.error(`Unknown action: ${item.id}`, { duration: 3000 });
    }
  }
}
