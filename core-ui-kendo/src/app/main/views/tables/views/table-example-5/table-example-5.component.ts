// Angular
import { Component, inject, signal } from '@angular/core';
// Kendo UI
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';
// Core
import { CoreCardModule } from '../../../../../core/components/core-card';
import { CoreDialogConfirmModule } from '../../../../../core/popups/core-dialog-confirm';
import { CoreTableModule, CoreTable } from '../../../../../core/components/core-table';
import { CoreNotificationModule, CoreNotificationService } from '../../../../../core/popups/core-notification';
import { CoreDialogFormModule } from '../../../../../core/popups/core-dialog-form';
// Services
import { TableExample5Service } from './table-example-5.service';
// Interfaces
import { TableExample5User } from './table-example-5.interfaces';
// Constants
import { TABLE_EXAMPLE_5_CITIES_OPTIONS } from './table-example-5.constants';
import { CoreToolbar } from '../../../../../core/components/core-toolbar';
import { pencilIcon, plusIcon, trashIcon } from '@progress/kendo-svg-icons';

@Component({
  selector: 'app-table-example-5',
  templateUrl: './table-example-5.component.html',
  styleUrl: './table-example-5.component.scss',
  host: { class: 'view-component' },
  imports: [
    CoreTableModule,
    CoreCardModule,
    CoreNotificationModule,
    CoreDialogConfirmModule,
    CoreDialogFormModule,
    DropDownsModule,
  ],
  providers: [TableExample5Service],
})
export class TableExample5Component {
  // [ Dependencies ]
  private _service = inject(TableExample5Service);
  private _notify = inject(CoreNotificationService);

  // [ Controls ]
  public table = signal<CoreTable<TableExample5User>>(this._getTableControl());

  // [ Helpers ]
  private _getTableControl(): CoreTable<TableExample5User> {
    return new CoreTable<TableExample5User>({
      columns: [
        CoreTable.ColumnSwitch({
          field: 'isActive',
          label: 'Active',
          falseLabel: 'Inactive',
          showMenu: false,
          sort: false,
          resizable: false,
          click: () => true,
        }),
        CoreTable.ColumnString({ field: 'fullName', label: 'Full Name', showMenu: false, sort: false }),
        CoreTable.ColumnNumber({
          field: 'salary',
          label: 'Salary',
          showMenu: false,
          sort: false,
          format: 'currency',
        }),
        CoreTable.ColumnBoolean({
          field: 'isEmployed',
          label: 'Employed',
          showMenu: false,
          sort: false,
          trueLabel: 'Yes',
          falseLabel: 'No',
        }),
        CoreTable.ColumnDate({
          field: 'birthDate',
          label: 'Birth Date',
          showMenu: false,
          sort: false,
        }),
        CoreTable.ColumnDateTime({
          field: 'appointmentTime',
          label: 'Appointment Time',
          showMenu: false,
          sort: false,
        }),
        CoreTable.ColumnString({
          field: 'city',
          label: 'City',
          showMenu: false,
          sort: false,
          filter: 'multiselect',
          filterOptions: TABLE_EXAMPLE_5_CITIES_OPTIONS,
        }),
      ],
      data: async () => await this._service.getUsers(),
      selectable: 'single',
      showSelectColumn: true,
      onSelectionChange: (rows) => {
        if (rows.length === 0) {
          this.table().toolbar()?.setItemsVisibility(false, ['update', 'delete']);
        } else {
          this.table().toolbar()?.setItemsVisibility(true, ['update', 'delete']);
        }
      },
      toolbarOptions: {
        style: { border: 'none', padding: '0 6px 0 0' },
        items: [
          CoreToolbar.Button({ id: 'create', label: 'Create', color: 'primary', icon: plusIcon, visible: true }),
          CoreToolbar.Button({ id: 'update', label: 'Update', color: 'secondary', icon: pencilIcon, visible: false }),
          CoreToolbar.Button({ id: 'delete', label: 'Delete', icon: trashIcon, visible: false }),
        ],
      },
      searchBarOptions: {
        searchButtonLabel: 'Search',
        placeholder: 'Search Users...',
        // value: 'John Doe',
        // disabled: true,
        showBarCodeReader: true,
        showAdvancedFilter: true,
        advancedFilterValue: {
          logic: 'AND',
          filters: [
            {
              field: 'fullName',
              operator: 'CONTAINS',
              value: 'John',
            },
            {
              field: 'salary',
              operator: 'GREATER_THAN',
              value: 50000,
            },
          ],
        },
        onSearch: (value) => {
          this._notify.success(`Searching for: ${value}`);
        },
        onSearchValueChange: (value) => {
          this._notify.success(`Search Value Changed: ${value}`);
        },
        onSearchValueChanged: (value) => {
          this._notify.success(`Search Value Changed (Final): ${value}`);
        },
        onApplyAdvancedFilters: (filters) => {
          this._notify.success(`Applied Filters: ${JSON.stringify(filters)}`);
        },
      },
    });
  }
}
