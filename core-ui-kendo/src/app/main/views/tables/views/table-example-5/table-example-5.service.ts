// Angular
import { inject, Injectable } from '@angular/core';
// Core UI
import { CoreDialogConfirmService } from '../../../../../core/popups/core-dialog-confirm';
import { TableExample5User } from './table-example-5.interfaces';
import { City } from './table-example-5.enums';

@Injectable()
export class TableExample5Service {
  // Dependencies
  private _dialogConfirm = inject(CoreDialogConfirmService);

  // [ Public Methods ]
  public async getUsers(): Promise<TableExample5User[]> {
    await new Promise((resolve) => setTimeout(resolve, 500));
    return this._UsersData;
  }

  // [ Dummy Data ]
  private _UsersData: TableExample5User[] = [
    {
      id: '1',
      isActive: true,
      fullName: '<PERSON>',
      salary: 50000,
      isEmployed: true,
      birthDate: new Date('1990-01-01'),
      appointmentTime: new Date('2023-10-01T10:00:00'),
      city: City.NY,
    },
    {
      id: '2',
      isActive: false,
      fullName: '<PERSON>',
      salary: 65000,
      isEmployed: true,
      birthDate: new Date('1985-05-15'),
      appointmentTime: new Date('2023-10-02T14:30:00'),
      city: City.CHI,
    },
    {
      id: '3',
      isActive: true,
      fullName: 'Michael Johnson',
      salary: 72000,
      isEmployed: false,
      birthDate: new Date('1988-11-22'),
      appointmentTime: new Date('2023-10-03T09:15:00'),
      city: City.LA,
    },
    {
      id: '4',
      isActive: true,
      fullName: 'Emily Davis',
      salary: 58000,
      isEmployed: true,
      birthDate: new Date('1992-03-08'),
      appointmentTime: new Date('2023-10-04T16:45:00'),
      city: City.HOU,
    },
    {
      id: '5',
      isActive: false,
      fullName: 'Robert Wilson',
      salary: 45000,
      isEmployed: false,
      birthDate: new Date('1987-09-30'),
      appointmentTime: new Date('2023-10-05T11:20:00'),
      city: City.MI,
    },
  ];
}
