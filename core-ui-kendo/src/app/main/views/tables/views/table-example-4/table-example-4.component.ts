// Angular
import { Component, inject, OnInit, Signal, signal } from '@angular/core';
// Services
import { TableExample4ApiService } from './table-example-4.api.service';
// Core UI
import { CoreTable, CoreTableModule } from '../../../../../core/components/core-table';
import { CoreNotificationModule, CoreNotificationService } from '../../../../../core/popups/core-notification';
// Interfaces
import { UserTable } from './table-example-4.interfaces';

@Component({
  selector: 'app-table-example-4',
  templateUrl: './table-example-4.component.html',
  styleUrl: './table-example-4.component.scss',
  host: { class: 'view-component' },
  providers: [TableExample4ApiService],
  imports: [CoreTableModule, CoreNotificationModule],
})
export class TableExample4Component implements OnInit {
  // Dependencies
  public _api = inject(TableExample4ApiService);
  private _notify = inject(CoreNotificationService);
  // Signals
  public table!: Signal<CoreTable<UserTable>>;
  public data = signal(this._api._UsersData);

  ngOnInit(): void {
    this.table = signal(
      new CoreTable<UserTable>({
        columns: [
          CoreTable.ColumnString({ field: 'fullName', label: 'Full Name' }),
          CoreTable.ColumnString({ field: 'email', label: 'Email' }),
        ],
        data: (state) => this._api.getUsersPageable(state),
        rowReorderable: true,
        selectable: 'multiple',
        onRowReorder: (e) => {
          const { oldIndex, newIndex } = e;
          this._notify.success(`Row moved from index ${oldIndex} to ${newIndex}`, { duration: 5000 });
        },
      }),
    );
  }
}
