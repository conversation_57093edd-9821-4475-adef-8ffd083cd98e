import { Injectable } from '@angular/core';

@Injectable()
export class TableResponsiveService {
  public userTableData = [
    {
      id: '1',
      isActive: true,
      username: 'joh<PERSON><PERSON>',
      fullName: '<PERSON>',
      email: '<EMAIL>',
      birthdate: '1990-05-15',
      salary: 3500,
      city: 'New York',
    },
    {
      id: '2',
      isActive: false,
      username: 'jane<PERSON>',
      fullName: '<PERSON>',
      email: '<EMAIL>',
      birthdate: '1985-09-22',
      salary: 4200,
      city: 'Los Angeles',
    },
    {
      id: '3',
      isActive: true,
      username: 'mikebrown',
      fullName: '<PERSON>',
      email: '<EMAIL>',
      birthdate: '1992-12-03',
      salary: 3900,
      city: 'Chicago',
    },
    {
      id: '4',
      isActive: false,
      username: 'sarawhite',
      fullName: '<PERSON>',
      email: '<EMAIL>',
      birthdate: '1988-07-19',
      salary: 4100,
      city: 'Los Angeles',
    },
  ];
}
