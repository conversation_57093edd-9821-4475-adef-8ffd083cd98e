// Angular
import { Component, inject, OnInit, Signal, signal } from '@angular/core';
// Core UI
import { CoreTable, CoreTableModule } from '../../../../../core/components/core-table';
import { CoreNotificationModule, CoreNotificationService } from '../../../../../core/popups/core-notification';
// Interfaces
import { UserTable } from './table-responsive.interfaces';
// Services
import { TableResponsiveService } from './table-responsive.service';

@Component({
  selector: 'app-table-responsive',
  templateUrl: './table-responsive.component.html',
  styleUrl: './table-responsive.component.scss',
  host: { class: 'view-component' },
  providers: [TableResponsiveService],
  imports: [CoreTableModule, CoreNotificationModule],
})
export class TableResponsiveComponent implements OnInit {
  // Elements
  public table!: Signal<CoreTable<UserTable>>;
  // Dependencies
  private _notify = inject(CoreNotificationService);
  private _service = inject(TableResponsiveService);

  public ngOnInit(): void {
    this.table = signal(
      new CoreTable<UserTable>({
        columns: [
          CoreTable.ColumnSwitch({
            field: 'isActive',
            label: 'Active',
            click: (row: UserTable) => !row.isActive,
          }),
          CoreTable.ColumnString({ field: 'fullName', label: 'Full Name', width: 100 }),
          CoreTable.ColumnString({ field: 'username', label: 'Username', width: 100, fixed: true }),
          CoreTable.ColumnString({ field: 'email', label: 'Email', width: 200 }),
          CoreTable.ColumnString({ field: 'birthdate', label: 'Birthdate', width: 100 }),
          CoreTable.ColumnNumber({
            field: 'salary',
            label: 'Salary',
            format: 'c',
            align: 'left',
            width: 100,
            visible: false,
          }),
          CoreTable.ColumnString({ field: 'city', label: 'City', width: 100 }),
          CoreTable.ColumnActions({
            sticky: true,
            width: 60,
            actions: [
              { label: 'Edit', click: (row: UserTable) => this._notify.success(`Edit ${JSON.stringify(row)}`) },
              { label: 'Delete', click: (row: UserTable) => this._notify.success(`Delete ${JSON.stringify(row)}`) },
            ],
          }),
        ],
        data: () => this._service.userTableData,
        responsiveMaxWidth: 480,
        showResponsiveToolbar: true,
        selectable: 'single',
        resizable: true,
        pageable: true,
        showSelectColumn: true,
        // ngStyle: { 'min-width': '1000px' },
      }),
    );
  }
}
