// Angular
import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, signal, viewChild } from '@angular/core';
import { JsonPipe } from '@angular/common';
import { Subscription } from 'rxjs';
// Core UI
import { CoreCardModule } from '../../../../../core/components/core-card';
import { CoreButtonModule } from '../../../../../core/components/core-button';
import {
  CoreSchedulerComponent,
  CoreSchedulerModule,
  CoreSchedulerRepeatMode,
  CoreSchedulerValue,
} from '../../../../../core/components/core-scheduler';
import {
  CoreSchedulerDurationMode,
  CoreSchedulerRepeatMonthlyMode,
  CoreSchedulerRepeatYearlyMode,
} from '../../../../../core/components/core-scheduler/core-scheduler.enums';
import {
  CoreInputRadioSelect,
  CoreInputRadioSelectComponent,
  CoreInputRadioSelectOption,
} from '../../../../../core/components/core-inputs';
// Constants
const RANDOM_DATE = new Date('January 1, 2025 12:30:00');

@Component({
  selector: 'app-scheduler-examples',
  templateUrl: './scheduler-examples.component.html',
  styleUrl: './scheduler-examples.component.scss',
  host: { class: 'view-component' },
  imports: [CoreCardModule, CoreSchedulerModule, CoreButtonModule, JsonPipe, CoreInputRadioSelectComponent],
})
export class SchedulerExamplesComponent implements OnInit, OnDestroy {
  public _previewControl = new CoreInputRadioSelect({
    options: this._previewControlOptions(),
    direction: 'vertical',
  });
  public schedulerRef = viewChild(CoreSchedulerComponent);
  public timeMode: '12hrs' | '24hrs' = '12hrs';
  public value = signal<CoreSchedulerValue | null>(null);
  public displayedValue = signal<CoreSchedulerValue | null>(null);

  private _subscriptions$ = new Subscription();

  public ngOnInit(): void {
    // Subscribe to handle preview mode changes
    this._subscriptions$.add(
      this._previewControl.valueChanges.subscribe((previewMode: string) => this._setSchedulerValueByMode(previewMode)),
    );
    // Set initial values
    this._previewControl.setValue('once');
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  // Events

  public async onShowValues(): Promise<void> {
    this.displayedValue.set(await this.schedulerRef()!.getValue());
  }

  // Internal

  private _setSchedulerValueByMode(mode: string): void {
    // Destroy previous scheduler component
    this.value.set(null);
    // Set new value
    setTimeout(() => {
      switch (mode) {
        case 'once':
          this.value.set(this.getOnceSchedulerValue());
          break;
        case 'daily':
          this.value.set(this.getDailySchedulerValue());
          break;
        case 'weekly':
          this.value.set(this.getWeeklySchedulerValue());
          break;
        case 'monthly-by-month-days':
          this.value.set(this.getMonthlyByMonthDaysSchedulerValue());
          break;
        case 'monthly-by-week-days':
          this.value.set(this.getMonthlyByWeekDaysSchedulerValue());
          break;
        case 'yearly-by-month-days':
          this.value.set(this.getYearlyByMonthDaysSchedulerValue());
          break;
        case 'yearly-by-week-days':
          this.value.set(this.getYearlyByWeekDaysSchedulerValue());
          break;
        case 'advanced':
          this.value.set(this.getAdvancedSchedulerValue());
          break;
      }
    });
  }

  private getOnceSchedulerValue(): CoreSchedulerValue {
    return {
      startDateTime: RANDOM_DATE,
      repeat: {
        mode: CoreSchedulerRepeatMode.ONCE,
        value: null,
      },
      duration: {
        mode: CoreSchedulerDurationMode.ALL_DAY,
        value: null,
      },
      endDate: RANDOM_DATE,
    };
  }

  private getDailySchedulerValue(): CoreSchedulerValue {
    return {
      startDateTime: RANDOM_DATE,
      repeat: {
        mode: CoreSchedulerRepeatMode.DAILY,
        value: { hour: 9, minute: 45 },
      },
      duration: {
        mode: CoreSchedulerDurationMode.ALL_DAY,
        value: null,
      },
      endDate: null,
    };
  }

  private getWeeklySchedulerValue(): CoreSchedulerValue {
    return {
      startDateTime: RANDOM_DATE,
      repeat: {
        mode: CoreSchedulerRepeatMode.WEEKLY,
        value: { hour: 12, minute: 30, weekDays: [1, 3, 6] },
      },
      duration: {
        mode: CoreSchedulerDurationMode.END_TIME,
        value: {
          date: RANDOM_DATE,
        },
      },
      endDate: RANDOM_DATE,
    };
  }

  private getMonthlyByMonthDaysSchedulerValue(): CoreSchedulerValue {
    return {
      startDateTime: RANDOM_DATE,
      repeat: {
        mode: CoreSchedulerRepeatMode.MONTHLY,
        value: {
          minute: 25,
          hour: 1,
          mode: CoreSchedulerRepeatMonthlyMode.DAYS_OF_THE_MONTH,
          value: { monthDays: [1, 15, -1] },
        },
      },
      duration: {
        mode: CoreSchedulerDurationMode.TIME_SPAN,
        value: {
          days: 1,
          hours: 2,
          minutes: 3,
        },
      },
      endDate: new Date(),
    };
  }

  private getMonthlyByWeekDaysSchedulerValue(): CoreSchedulerValue {
    return {
      startDateTime: RANDOM_DATE,
      repeat: {
        mode: CoreSchedulerRepeatMode.MONTHLY,
        value: {
          minute: 25,
          hour: 1,
          mode: CoreSchedulerRepeatMonthlyMode.DAYS_OF_THE_WEEK,
          value: { weekDays: [1, 3, 6], weeks: [1, 3, -1] },
        },
      },
      duration: {
        mode: CoreSchedulerDurationMode.ALL_DAY,
        value: null,
      },
      endDate: new Date(),
    };
  }

  private getYearlyByMonthDaysSchedulerValue(): CoreSchedulerValue {
    return {
      startDateTime: RANDOM_DATE,
      repeat: {
        mode: CoreSchedulerRepeatMode.YEARLY,
        value: {
          minute: 25,
          hour: 1,
          mode: CoreSchedulerRepeatYearlyMode.DAYS_OF_THE_MONTH,
          value: { monthDays: [1, 15, -1] },
          months: [1, 6, 11],
        },
      },
      duration: {
        mode: CoreSchedulerDurationMode.ALL_DAY,
        value: null,
      },
      endDate: new Date(),
    };
  }

  private getYearlyByWeekDaysSchedulerValue(): CoreSchedulerValue {
    return {
      startDateTime: RANDOM_DATE,
      repeat: {
        mode: CoreSchedulerRepeatMode.YEARLY,
        value: {
          minute: 25,
          hour: 1,
          mode: CoreSchedulerRepeatYearlyMode.DAYS_OF_THE_WEEK,
          value: { weekDays: [1, 3, 6], weeks: [1, 3, -1] },
          months: [1, 6, 11],
        },
      },
      duration: {
        mode: CoreSchedulerDurationMode.ALL_DAY,
        value: null,
      },
      endDate: new Date(),
    };
  }

  private getAdvancedSchedulerValue(): CoreSchedulerValue {
    return {
      startDateTime: RANDOM_DATE,
      repeat: {
        mode: CoreSchedulerRepeatMode.ADVANCED,
        value: {
          cron: '0 0 * * *',
        },
      },
      duration: {
        mode: CoreSchedulerDurationMode.ALL_DAY,
        value: null,
      },
      endDate: new Date(),
    };
  }

  private _previewControlOptions(): CoreInputRadioSelectOption[] {
    return [
      { label: 'Once', value: 'once' },
      { label: 'Daily', value: 'daily' },
      { label: 'Weekly', value: 'weekly' },
      { label: 'Monthly (By Month Days)', value: 'monthly-by-month-days' },
      { label: 'Monthly (By Week Days)', value: 'monthly-by-week-days' },
      { label: 'Yearly (By Month Days)', value: 'yearly-by-month-days' },
      { label: 'Yearly (By Week Days)', value: 'yearly-by-week-days' },
      { label: 'Advanced', value: 'advanced' },
    ];
  }
}
