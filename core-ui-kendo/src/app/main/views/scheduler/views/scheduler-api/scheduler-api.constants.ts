import { APIInputData } from '../../../../components/api-view/api-view.interfaces';

const CORE_SCHEDULER_START_DATE_INPUT: APIInputData = {
  name: 'startDate',
  type: 'Date',
  description: 'The start date of the scheduler.',
};

const CORE_SCHEDULER_END_DATE_INPUT: APIInputData = {
  name: 'endDate',
  type: 'Date | null',
  description: 'The end date of the scheduler. If null, the scheduler will not end.',
};

const CORE_SCHEDULER_MINUTE_INPUT: APIInputData = {
  name: 'minute',
  type: 'number',
  description: 'The minute of the scheduler.',
};

const CORE_SCHEDULER_HOUR_INPUT: APIInputData = {
  name: 'hour',
  type: 'number',
  description: 'The hour of the scheduler.',
};

const CORE_SCHEDULER_REPEAT_WEEKLY_MODE: APIInputData = {
  name: 'CoreSchedulerRepeatWeekly',
  type: 'CoreSchedulerRepeatWeekly',
  description: 'The weekly repeat value of the scheduler.',
  interface: {
    name: 'CoreSchedulerRepeatWeekly',
    fields: [
      CORE_SCHEDULER_MINUTE_INPUT,
      CORE_SCHEDULER_HOUR_INPUT,
      {
        name: 'weekDays',
        type: 'number[ ]',
        description: 'The week days of the weekly repeat value of the scheduler.',
      },
    ],
  },
};

const CORE_SCHEDULER_REPEAT_MONTHLY_MODE: APIInputData = {
  name: 'CoreSchedulerRepeatMonthly',
  type: 'CoreSchedulerRepeatMonthly',
  description: 'The monthly repeat value of the scheduler.',
  interface: {
    name: 'CoreSchedulerRepeatMonthly',
    fields: [
      CORE_SCHEDULER_MINUTE_INPUT,
      CORE_SCHEDULER_HOUR_INPUT,
      {
        name: 'mode',
        type: 'CoreSchedulerRepeatMonthlyMode',
        description: "Supports: 'DAYS_OF_THE_MONTH' | 'DAYS_OF_THE_WEEK'",
      },
      {
        name: 'value',
        type: 'MonthlyRepeatDaysOfTheMonth | MonthlyRepeatDaysOfTheWeek',
        description: 'The value of the monthly repeat value of the scheduler.',
        interface: {
          name: 'CoreSchedulerRepeatMonthly',
          fields: [
            {
              name: 'MonthlyRepeatDaysOfTheMonth',
              type: 'MonthlyRepeatDaysOfTheMonth',
              description: 'The monthly repeat value of the scheduler.',
              interface: {
                name: 'MonthlyRepeatDaysOfTheMonth',
                fields: [
                  {
                    name: 'monthDays',
                    type: 'number[ ]',
                    description: 'The month days of the monthly repeat value of the scheduler.',
                  },
                ],
              },
            },
            {
              name: 'MonthlyRepeatDaysOfTheWeek',
              type: 'MonthlyRepeatDaysOfTheWeek',
              description: 'The monthly repeat value of the scheduler.',
              interface: {
                name: 'MonthlyRepeatDaysOfTheWeek',
                fields: [
                  {
                    name: 'weeks',
                    type: 'number[ ]',
                    description: 'The weeks of the monthly repeat value of the scheduler.',
                  },
                  {
                    name: 'weekDays',
                    type: 'number[ ]',
                    description: 'The week days of the monthly repeat value of the scheduler.',
                  },
                ],
              },
            },
          ],
        },
      },
    ],
  },
};

const CORE_SCHEDULER_REPEAT_YEARLY_MODE: APIInputData = {
  name: 'CoreSchedulerRepeatYearly',
  type: 'CoreSchedulerRepeatYearly',
  description: 'The yearly repeat value of the scheduler.',
  interface: {
    name: 'CoreSchedulerRepeatYearly',
    fields: [
      CORE_SCHEDULER_MINUTE_INPUT,
      CORE_SCHEDULER_HOUR_INPUT,
      {
        name: 'mode',
        type: 'CoreSchedulerRepeatYearlyMode',
        description: "Supports: 'DAYS_OF_THE_MONTH' | 'DAYS_OF_THE_WEEK'",
      },
      {
        name: 'value',
        type: 'YearlyRepeatDaysOfTheMonth | YearlyRepeatDaysOfTheWeek',
        description: 'The value of the yearly repeat value of the scheduler.',
        interface: {
          name: 'CoreSchedulerRepeatYearly',
          fields: [
            {
              name: 'YearlyRepeatDaysOfTheMonth',
              type: 'YearlyRepeatDaysOfTheMonth',
              description: 'The yearly repeat value of the scheduler.',
              interface: {
                name: 'YearlyRepeatDaysOfTheMonth',
                fields: [
                  {
                    name: 'monthDays',
                    type: 'number[ ]',
                    description: 'The month days of the yearly repeat value of the scheduler.',
                  },
                ],
              },
            },
            {
              name: 'YearlyRepeatDaysOfTheWeek',
              type: 'YearlyRepeatDaysOfTheWeek',
              description: 'The yearly repeat value of the scheduler.',
              interface: {
                name: 'YearlyRepeatDaysOfTheWeek',
                fields: [
                  {
                    name: 'weeks',
                    type: 'number[ ]',
                    description: 'The weeks of the yearly repeat value of the scheduler.',
                  },
                  {
                    name: 'weekDays',
                    type: 'number[ ]',
                    description: 'The week days of the yearly repeat value of the scheduler.',
                  },
                ],
              },
            },
          ],
        },
      },
    ],
  },
};

const SCHEDULER_VALUE: APIInputData = {
  name: 'value',
  type: 'CoreSchedulerRepeatDaily | CoreSchedulerRepeatWeekly | CoreSchedulerRepeatMonthly | CoreSchedulerRepeatYearly | CoreSchedulerRepeatAdvanced | null',
  description: 'The repeat value of the scheduler.',
  interface: {
    name: 'CoreSchedulerRepeatValue',
    fields: [
      {
        name: 'CoreSchedulerRepeatDaily',
        type: 'CoreSchedulerRepeatDaily',
        description: 'The daily repeat value of the scheduler.',
        interface: {
          name: 'CoreSchedulerRepeatDaily',
          fields: [CORE_SCHEDULER_MINUTE_INPUT, CORE_SCHEDULER_HOUR_INPUT],
        },
      },
      CORE_SCHEDULER_REPEAT_WEEKLY_MODE,
      CORE_SCHEDULER_REPEAT_MONTHLY_MODE,
      CORE_SCHEDULER_REPEAT_YEARLY_MODE,
      {
        name: 'CoreSchedulerRepeatAdvanced',
        type: 'CoreSchedulerRepeatAdvanced',
        description: 'The advanced repeat value of the scheduler.',
        interface: {
          name: 'CoreSchedulerRepeatAdvanced',
          fields: [
            {
              name: 'cron',
              type: 'string',
              description: 'The cron expression of the advanced repeat value of the scheduler.',
            },
          ],
        },
      },
    ],
  },
};

const CORE_SCHEDULER_REPEAT_INPUT: APIInputData = {
  name: 'repeat',
  type: 'CoreSchedulerRepeatValue',
  description: 'The repeat value of the scheduler.',
  interface: {
    name: 'CoreSchedulerRepeatValue',
    fields: [
      {
        name: 'mode',
        type: 'CoreSchedulerRepeatMode',
        description:
          "The repeat mode of the scheduler. Supports: 'ONCE', 'DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY', 'ADVANCED' ",
      },
      SCHEDULER_VALUE,
    ],
  },
};

const CORE_SCHEDULER_DURATION_INPUT: APIInputData = {
  name: 'duration',
  type: 'CoreSchedulerDurationValue',
  description: 'The duration value of the scheduler.',
  interface: {
    name: 'CoreSchedulerDurationValue',
    showDefaultColumn: true,
    fields: [
      {
        name: 'mode',
        type: 'CoreSchedulerDurationMode',
        description: "The duration mode of the scheduler. Supports: 'TIME_SPAN' | 'END_TIME' | 'ALL_DAY' | 'INFINITE'",
      },
      {
        name: 'value',
        type: 'CoreSchedulerDurationEndTime | CoreSchedulerDurationTimeSpan | null',
        description: 'The duration value of the scheduler.',
        interface: {
          name: 'CoreSchedulerDurationValue',
          showDefaultColumn: true,
          fields: [
            {
              name: 'CoreSchedulerDurationEndTime',
              type: 'CoreSchedulerDurationEndTime',
              description: 'The end time value of the scheduler.',
              interface: {
                name: 'CoreSchedulerDurationEndTime',
                fields: [
                  {
                    name: 'date',
                    type: 'Date',
                    description: 'The end date of the scheduler.',
                  },
                ],
              },
            },
            {
              name: 'CoreSchedulerDurationTimeSpan',
              type: 'CoreSchedulerDurationTimeSpan',
              description: 'The time span value of the scheduler.',
              interface: {
                name: 'CoreSchedulerDurationTimeSpan',
                fields: [
                  {
                    name: 'days',
                    type: 'number',
                    description: 'The number of days of the time span value of the scheduler.',
                  },
                  {
                    name: 'hours',
                    type: 'number',
                    description: 'The number of hours of the time span value of the scheduler.',
                  },
                  {
                    name: 'minutes',
                    type: 'number',
                    description: 'The number of minutes of the time span value of the scheduler.',
                  },
                  {
                    name: 'seconds',
                    type: 'number',
                    description: 'The number of seconds of the time span value of the scheduler.',
                  },
                ],
              },
            },
          ],
        },
      },
    ],
  },
};

export const CORE_SCHEDULER_TIME_MODE_INPUT: APIInputData = {
  name: 'timeMode',
  type: 'string',
  default: "'12hrs'",
  description: 'The time mode of the scheduler. Supports: "12hrs" | "24hrs"',
};

export const CORE_SCHEDULER_SHOW_SUMMARY_INPUT: APIInputData = {
  name: 'showSummary',
  type: 'boolean',
  default: 'true',
  description: 'Whether to show the summary of the scheduler.',
};

export const CORE_SCHEDULER_VALUE_INPUT: APIInputData = {
  name: 'value',
  type: 'CoreSchedulerValue',
  description: 'The value of the scheduler.',
  interface: {
    name: 'CoreSchedulerValue',
    fields: [
      CORE_SCHEDULER_START_DATE_INPUT,
      CORE_SCHEDULER_REPEAT_INPUT,
      CORE_SCHEDULER_DURATION_INPUT,
      CORE_SCHEDULER_END_DATE_INPUT,
    ],
  },
};
