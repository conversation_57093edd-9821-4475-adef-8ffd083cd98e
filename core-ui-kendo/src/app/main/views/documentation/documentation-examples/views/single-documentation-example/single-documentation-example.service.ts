// Angular
import { Injectable } from '@angular/core';
// Constants
export const CORE_KENDO_UI_GUIDE_ID = 'core-ui-kendo-developer-guide';

@Injectable()
export class SingleDocumentationExampleService {
  public getDocumentationContent(): string | null {
    return localStorage.getItem(CORE_KENDO_UI_GUIDE_ID);
  }

  public setDocumentationContent(htmlContent: string): boolean {
    try {
      localStorage.setItem(CORE_KENDO_UI_GUIDE_ID, htmlContent);
      return true;
    } catch {
      return false;
    }
  }
}
