// Angular
import { Injectable } from '@angular/core';

@Injectable()
export class MultipleDocumentationsExampleService {
  public getDocumentationContent(docId: string): string | null {
    return localStorage.getItem(docId);
  }

  public setDocumentationContent(docId: string, htmlContent: string): boolean {
    try {
      localStorage.setItem(docId, htmlContent);
      return true;
    } catch {
      return false;
    }
  }
}
