// Angular
import { Component, inject, OnInit, signal } from '@angular/core';
// Core UI
import { CoreDocumentationModule } from '../../../../../../core/components/core-documentation/core-documentation.module';
import { CoreNotificationModule, CoreNotificationService } from '../../../../../../core/popups/core-notification';
import { CoreInputsModule, CoreInputSwitch } from '../../../../../../core/components/core-inputs';
// Services
import { SingleDocumentationExampleService } from './single-documentation-example.service';

@Component({
  selector: 'app-single-documentation-example',
  templateUrl: './single-documentation-example.component.html',
  styleUrl: './single-documentation-example.component.scss',
  host: { class: 'view-component' },
  providers: [SingleDocumentationExampleService],
  imports: [CoreDocumentationModule, CoreNotificationModule, CoreInputsModule],
})
export class SingleDocumentationExampleComponent implements OnInit {
  // Deps
  private _service = inject(SingleDocumentationExampleService);
  private _notify = inject(CoreNotificationService);

  // Internal
  public documentationContent = signal('');
  public allowEditingControl = new CoreInputSwitch({
    label: 'Edit Mode',
    value: false,
  });

  public ngOnInit(): void {
    this.loadDocumentation();
  }

  // Events

  public onEditorSaveChanges(htmlContent: string): void {
    const success = this._service.setDocumentationContent(htmlContent);
    if (success) {
      this.loadDocumentation();
      this._notify.success('Changes saved successfully in local storage');
    } else {
      this._notify.error('Failed to save changes');
    }
  }

  public onEditorDiscardChanges(): void {
    // Reload previously saved data
    this.loadDocumentation();
    this._notify.success('Changes have been discarded');
  }

  // Internal

  private loadDocumentation(): void {
    this.documentationContent.set(this._service.getDocumentationContent() ?? '');
  }
}
