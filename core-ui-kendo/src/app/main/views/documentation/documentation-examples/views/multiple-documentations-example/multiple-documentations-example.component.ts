// Angular
import { AfterViewInit, Component, inject, viewChild } from '@angular/core';
// Core UI
import { CoreNotificationService } from '../../../../../../core/popups/core-notification';
import { CoreInputsModule, CoreInputSwitch } from '../../../../../../core/components/core-inputs';
import { CoreDocumentationModule } from '../../../../../../core/components/core-documentation/core-documentation.module';
// Services
import { MultipleDocumentationsExampleService } from './multitple-documentations-example.service';
// Constants
import { CORE_KENDO_UI_GUIDE_ID } from '../single-documentation-example/single-documentation-example.service';
import { CoreDocumentationComponent } from '../../../../../../core/components/core-documentation';
export const JTMC_USERS_MGMT_REQUESTER_USER_GUIDE_ID = 'jtmc-users-manual';

@Component({
  selector: 'app-multiple-documentations-example',
  templateUrl: './multiple-documentations-example.component.html',
  styleUrl: './multiple-documentations-example.component.scss',
  host: { class: 'view-component' },
  providers: [MultipleDocumentationsExampleService],
  imports: [CoreInputsModule, CoreDocumentationModule],
})
export class MultipleDocumentationsExampleComponent implements AfterViewInit {
  // Deps
  private _service = inject(MultipleDocumentationsExampleService);
  private _notify = inject(CoreNotificationService);

  // Internal
  public allowEditingControl = new CoreInputSwitch({
    label: 'Edit Mode',
    value: false,
  });

  public docs: { id: string; title: string }[] = [
    { id: CORE_KENDO_UI_GUIDE_ID, title: 'Core UI Kendo (Developer Guide)' },
    { id: JTMC_USERS_MGMT_REQUESTER_USER_GUIDE_ID, title: 'JTMC Users Management (Requester User Guide)' },
  ];
  public selectedDocId: string = CORE_KENDO_UI_GUIDE_ID;

  private documentationRef = viewChild(CoreDocumentationComponent);

  public ngAfterViewInit(): void {
    void this.loadDocumentation();
  }

  // Events

  public onSelectDoc(doc: { id: string; title: string }): void {
    this.selectedDocId = doc.id;
    this.loadDocumentation();
  }

  public onEditorSaveChanges(htmlContent: string): void {
    const success = this._service.setDocumentationContent(this.selectedDocId, htmlContent);
    if (success) {
      this.loadDocumentation();
      this._notify.success('Changes saved successfully in local storage');
    } else {
      this._notify.error('Failed to save changes');
    }
  }

  public onEditorDiscardChanges(): void {
    // Reload previously saved data
    this.loadDocumentation();
    this._notify.success('Changes have been discarded');
  }

  // Private

  private loadDocumentation(): void {
    const data = this._service.getDocumentationContent(this.selectedDocId) ?? '';
    this.documentationRef()?.setHtmlContent(data);
  }
}
