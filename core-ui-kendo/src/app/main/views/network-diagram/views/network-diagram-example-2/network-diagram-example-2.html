<core-network-diagram
  [nodes]="diagramNodes()"
  [containers]="diagramContainers()"
  [connections]="diagramConnections()"
  [state]="diagramState()"
  [options]="diagramOptions()"
  (stateChange)="onDiagramStateChange($event)"
  (connectionCreate)="onNewConnection($event)"
  (connectionFailed)="onNewConnectionFailed($event.error)"
  (connectionAction)="onConnectionAction($event)"
  (switchAction)="onSwitchAction($event)"
  (switchPortAction)="onSwitchPortAction($event)"
  (patchPanelAction)="onPatchPanelAction($event)"
  (patchPanelPortAction)="onPatchPanelPortAction($event)"
  (ethernetCableAction)="onEthernetCableAction($event)"
  (fiberCableAction)="onFiberCableAction($event)"
  (fiberTubeAction)="onFiberTubeAction($event)"
  (fiberStrandAction)="onFiberStrandAction($event)"
/>
