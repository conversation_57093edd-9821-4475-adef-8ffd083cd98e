core-network-diagram {
  flex: 1 1 0;
}

.connection-label {
  color: var(--text);
  background: var(--component-bg);
  padding: 0.25rem;
}

.blue-tube {
  border: 1px solid blue !important;

  .color-indicator {
    background: blue;
  }
}

.orange-tube {
  border: 1px solid orange !important;

  .color-indicator {
    background: orange;
  }
}

.green-tube {
  border: 1px solid green !important;

  .color-indicator {
    background: green;
  }
}

.brown-tube {
  border: 1px solid brown !important;

  .color-indicator {
    background: brown;
  }
}

.blue-strand {
  border: 1px solid blue !important;

  .color-indicator {
    background: blue;
  }
}

.orange-strand {
  border: 1px solid orange !important;

  .color-indicator {
    background: orange;
  }
}

.green-strand {
  border: 1px solid green !important;

  .color-indicator {
    background: green;
  }
}

.brown-strand {
  border: 1px solid brown !important;

  .color-indicator {
    background: brown;
  }
}

.slate-strand {
  border: 1px solid #aeaeae !important;

  .color-indicator {
    background: #aeaeae;
  }
}

.white-strand {
  border: 1px solid white !important;

  .color-indicator {
    background: white;
  }
}

.red-strand {
  border: 1px solid red !important;

  .color-indicator {
    background: red;
  }
}

.black-strand {
  border: 1px solid black !important;

  .color-indicator {
    background: black;
  }
}

.yellow-strand {
  border: 1px solid yellow !important;

  .color-indicator {
    background: yellow;
  }
}

.violet-strand {
  border: 1px solid violet !important;

  .color-indicator {
    background: violet;
  }
}

.rose-strand {
  border: 1px solid pink !important;

  .color-indicator {
    background: pink;
  }
}

.aqua-strand {
  border: 1px solid aqua !important;

  .color-indicator {
    background: aqua;
  }
}
