// Angular
import { Component, signal, viewChild, ViewEncapsulation } from '@angular/core';
// Third Party
import { generateGuid } from '@foblex/utils';
// Core UI
import { CoreNetworkDiagramComponent } from '../../../../../extra/components/core-network-diagram/core-network-diagram.component';
import {
  CoreNetworkDiagramConnection,
  CoreNetworkDiagramState,
  CoreNetworkPatchPanel,
  CoreNetworkPatchPanelPort,
  CoreNetworkPatchPanelPortStatus,
  CoreNetworkSwitch,
  CoreNetworkSwitchPort,
  CoreNetworkConnectionType,
  CoreNetworkSwitchPortStatus,
  CoreNetworkDiagramConnectionEvent,
  CoreNetworkDiagramContainer,
  CoreNetworkDiagramNode,
} from '../../../../../extra/components/core-network-diagram';
import {
  CoreNetworkDiagramActionItem,
  CoreNetworkDiagramFiberCable,
  CoreNetworkDiagramFiberCableTube,
  CoreNetworkDiagramFiberStrand,
  CoreNetworkDiagramOptions,
  CoreNetworkDiagramPort,
} from '../../../../../extra/components/core-network-diagram/core-network-diagram.interfaces';
import {
  CoreNetworkDiagramCableLayoutDirection,
  CoreNetworkDiagramCableType,
  CoreNetworkDiagramNodeType,
} from '../../../../../extra/components/core-network-diagram/core-network-diagram.enums';
import { eyeIcon, trashIcon } from '@progress/kendo-svg-icons';
// Constants
const LOCAL_STORAGE_KEY = 'diagram-state';

@Component({
  selector: 'app-network-diagram-example-2',
  templateUrl: './network-diagram-example-2.html',
  styleUrl: './network-diagram-example-2.scss',
  host: { class: 'view-component' },
  imports: [CoreNetworkDiagramComponent],
  encapsulation: ViewEncapsulation.None,
})
export class NetworkDiagramExample2Component {
  // Mock DB Data
  private _dbConnections: CoreNetworkDiagramConnection[] = [];
  private _dbNodes = this.generateDiagramNodes();
  private _dbContainers = this.generateDiagramContainers();

  // Actual Diagram Data
  public diagramNodes = signal<CoreNetworkDiagramNode[]>([...this._dbNodes]);
  public diagramContainers = signal<CoreNetworkDiagramContainer[]>([...this._dbContainers]);
  public diagramConnections = signal<CoreNetworkDiagramConnection[]>([...this._dbConnections]);
  public diagramOptions = signal<CoreNetworkDiagramOptions>(this.getDiagramOptions());
  // Diagram initial state
  public diagramState = signal<CoreNetworkDiagramState | undefined>(this.getDiagramState());
  public diagramRef = viewChild(CoreNetworkDiagramComponent);

  // [ Event Handlers ]

  public onDiagramStateChange(state: CoreNetworkDiagramState): void {
    this.diagramState.set(state);
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(state));
  }

  public async onNewConnection(connection: CoreNetworkDiagramConnectionEvent): Promise<void> {
    const newConnection = await this._createConnection(connection.outputId, connection.inputId);
    if (newConnection) {
      // Mock DB: update connections
      this._dbConnections.push(newConnection);
      // Set diagram data
      this.diagramRef()?.setConnections(this._getConnections());
      this.diagramRef()?.setNodes(this._getNodes(true));
      this.diagramRef()?.setContainers(this._getContainers(true));
    } else {
      // TODO: show error
    }
  }

  public onNewConnectionFailed(errorMessage: string): void {
    console.error(errorMessage);
  }

  public onConnectionAction(e: { actionId: string; connectionId: string }): void {
    switch (e.actionId) {
      case 'view-asset':
        console.log('View Connection Assrt');
        break;
      case 'delete':
        this.deleteConnection(e.connectionId);
        break;
    }
  }

  public onSwitchAction(e: { switchId: string; actionId: string }): void {
    console.log('On Switch action: ', e);
  }

  public onSwitchPortAction(e: { switchId: string; portId: string; actionId: string }): void {
    console.log('On Switch Port action: ', e);
  }

  public onPatchPanelAction(e: { patchPanelId: string; actionId: string }): void {
    console.log('On Patch Panel action: ', e);
  }

  public onPatchPanelPortAction(e: { patchPanelId: string; portId: string; actionId: string }): void {
    console.log('On Patch Panel Port action: ', e);
  }

  public onEthernetCableAction(e: { cableId: string; actionId: string }): void {
    console.log('On Ethernet Cable action: ', e);
  }

  public onFiberCableAction(e: { cableId: string; actionId: string }): void {
    console.log('On Fiber Cable action: ', e);
  }

  public onFiberTubeAction(e: { cableId: string; tubeId: string; actionId: string }): void {
    console.log('On Fiber Tube action: ', e);
  }

  public onFiberStrandAction(e: { cableId: string; tubeId: string; strandId: string; actionId: string }): void {
    console.log('On Fiber Strand action: ', e);
  }

  // Internal

  private deleteConnection(connectionId: string): void {
    const connections = this._getConnections().filter((c) => c.id !== connectionId);
    this._dbConnections = connections;
    this.diagramRef()?.setConnections(connections);
    // Update data
    this.diagramRef()?.setNodes(this._getNodes(true));
    this.diagramRef()?.setContainers(this._getContainers(true));
  }

  private getDiagramState(): CoreNetworkDiagramState | undefined {
    const state = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (state != null) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      return JSON.parse(state);
    } else {
      return undefined;
    }
  }

  private getDiagramOptions(): CoreNetworkDiagramOptions {
    const basicActions: CoreNetworkDiagramActionItem[] = [{ id: 'view-asset', label: 'View Asset', icon: eyeIcon }];
    const basicActionsAlt: CoreNetworkDiagramActionItem[] = [
      { id: 'view-asset', label: 'View Asset', icon: eyeIcon },
      { id: 'delete', label: 'Delete', icon: trashIcon },
    ];
    return {
      connectionCssClass: 'connection-label',
      actions: {
        connectionActions: basicActionsAlt,
        switchActions: basicActions,
        switchPortActions: basicActionsAlt,
        patchPanelActions: basicActions,
        patchPanelPortActions: basicActionsAlt,
        ethernetCableActions: basicActions,
        fiberCableActions: basicActionsAlt,
        fiberTubeActions: basicActions,
        fiberStrandActions: basicActionsAlt,
      },
    };
  }

  // [ Mock Data Makers ]

  private generateDiagramNodes(): CoreNetworkDiagramNode[] {
    // const switches = this.generateSwitches('Nodes', 2, 8, 8);
    // const patchPanels = this.generatePanels('Nodes', 1, 24, 12);
    // return [...switches, ...patchPanels];
    return [];
  }

  private generateDiagramContainers(): CoreNetworkDiagramContainer[] {
    return [
      {
        id: 'container-1',
        label: 'Cabinet 1',
        nodes: [...this.generateSwitches('C-1', 2, 8, 8), ...this.generatePatchPanels('C-1', 1, 24, 6)],
        cables: [
          ...this.generateFiberCables('C-1', 2, 2, 12),
          {
            cableType: CoreNetworkDiagramCableType.ETHERNET,
            id: 'C-1-Ethernet-Cable-1',
            label: '[C-1] Ethernet Cable 1',
            cssClass: 'ethernet-cable',
          },
        ],
      },
      // {
      //   id: 'container-2',
      //   label: 'Cabinet 2',
      //   nodes: [...this.generatePanels('C-2', 2, 8, 8)],
      // },
    ];
  }

  private generateSwitches(idPrefix: string, count: number, portsCount: number, columns: number): CoreNetworkSwitch[] {
    const items: CoreNetworkSwitch[] = [];
    for (let i = 0; i < count; i++) {
      const switchId = idPrefix + '-switch-' + i;
      const label = `[${idPrefix}] Switch ${i + 1}`;
      const ports = this.generateSwitchPorts(switchId, portsCount);
      items.push({
        id: switchId,
        label,
        ports,
        columns,
        nodeType: CoreNetworkDiagramNodeType.SWITCH,
      });
    }
    return items;
  }

  private generateSwitchPorts(parentId: string, count: number): CoreNetworkSwitchPort[] {
    const ports: CoreNetworkSwitchPort[] = [];
    for (let i = 0; i < count; i++) {
      ports.push({
        id: `${parentId}-port-${i + 1}`,
        label: `${i + 1}`,
        status: CoreNetworkSwitchPortStatus.NOT_IN_USE,
        connectionType: i % 2 === 0 ? CoreNetworkConnectionType.ETHERNET : CoreNetworkConnectionType.FIBER,
      });
    }
    return ports;
  }

  public generatePatchPanels(
    idPrefix: string,
    count: number,
    portsCount: number,
    columns: number,
  ): CoreNetworkPatchPanel[] {
    const panels: CoreNetworkPatchPanel[] = [];
    for (let i = 0; i < count; i++) {
      const panelId = idPrefix + '-panel-' + i;
      const label = `[${idPrefix}] Panel ${i + 1}`;
      const ports = this.generatePatchPanelPorts(panelId, portsCount);
      panels.push({
        id: panelId,
        label,
        ports,
        columns,
        nodeType: CoreNetworkDiagramNodeType.PATCH_PANEL,
      });
    }
    return panels;
  }

  private generatePatchPanelPorts(parentId: string, count: number): CoreNetworkPatchPanelPort[] {
    const ports: CoreNetworkPatchPanelPort[] = [];
    for (let i = 0; i < count; i++) {
      ports.push({
        id: `${parentId}-port-${i + 1}`,
        label: `${i + 1}`,
        status: CoreNetworkPatchPanelPortStatus.NOT_IN_USE,
        connectionType: i % 2 === 0 ? CoreNetworkConnectionType.ETHERNET : CoreNetworkConnectionType.FIBER,
      });
    }
    return ports;
  }

  private generateFiberCables(
    idPrefix: string,
    count: number,
    tubesCount: number,
    strandsCount: number, // MAX 12 supported
  ): CoreNetworkDiagramFiberCable[] {
    const cables: CoreNetworkDiagramFiberCable[] = [];
    for (let i = 0; i < count; i++) {
      const cableId = idPrefix + '-fiber-cable-' + i;
      const cableLabel = `[${idPrefix}] Fiber Cable ${i + 1}`;
      const cableTubes: CoreNetworkDiagramFiberCableTube[] = [];
      for (let j = 0; j < tubesCount; j++) {
        const tubeId = cableId + '-tube-' + j;
        const tubeLabel = `[${idPrefix}] FC ${i + 1} - Tube ${j + 1}`;
        const tubeStrands = this.generateFiberStrands(tubeId, strandsCount);
        cableTubes.push({
          id: tubeId,
          label: tubeLabel,
          strands: tubeStrands,
          cssClass: this.getTubeCssClass(j),
        });
      }
      cables.push({
        id: cableId,
        label: cableLabel,
        tubes: cableTubes,
        cableType: CoreNetworkDiagramCableType.FIBER,
        layoutDirection:
          i % 2 === 0
            ? CoreNetworkDiagramCableLayoutDirection.LEFT_TO_RIGHT
            : CoreNetworkDiagramCableLayoutDirection.RIGHT_TO_LEFT,
        cssClass: 'fiber-cable',
      });
    }
    return cables;
  }

  private generateFiberStrands(idPrefix: string, count: number): CoreNetworkDiagramFiberStrand[] {
    const FIBER_STRANDS: CoreNetworkDiagramFiberStrand[] = [
      {
        id: idPrefix + '-strand-1',
        label: 'Blue Fiber',
        cssClass: 'blue-strand',
      },
      {
        id: idPrefix + '-strand-2',
        label: 'Orange Fiber',
        cssClass: 'orange-strand',
      },
      {
        id: idPrefix + '-strand-3',
        label: 'Green Fiber',
        cssClass: 'green-strand',
      },
      {
        id: idPrefix + '-strand-4',
        label: 'Brown Fiber',
        cssClass: 'brown-strand',
      },
      {
        id: idPrefix + '-strand-5',
        label: 'Slate Fiber',
        cssClass: 'slate-strand',
      },
      {
        id: idPrefix + '-strand-6',
        label: 'White Fiber',
        cssClass: 'white-strand',
      },
      {
        id: idPrefix + '-strand-7',
        label: 'Red Fiber',
        cssClass: 'red-strand',
      },
      {
        id: idPrefix + '-strand-8',
        label: 'Black Fiber',
        cssClass: 'black-strand',
      },
      {
        id: idPrefix + '-strand-9',
        label: 'Yellow Fiber',
        cssClass: 'yellow-strand',
      },
      {
        id: idPrefix + '-strand-10',
        label: 'Violet Fiber',
        cssClass: 'violet-strand',
      },
      {
        id: idPrefix + '-strand-11',
        label: 'Rose Fiber',
        cssClass: 'rose-strand',
      },
      {
        id: idPrefix + '-strand-12',
        label: 'Aqua Fiber',
        cssClass: 'aqua-strand',
      },
    ];
    return FIBER_STRANDS.filter((s, index) => index < count);
  }

  // Mock Server API Requests

  private _getConnections(): CoreNetworkDiagramConnection[] {
    return this._dbConnections;
  }

  private _getNodes(checkConnections = false): CoreNetworkDiagramNode[] {
    if (checkConnections) {
      return this.patchNodesStatusByConnections(this._dbNodes, this._dbConnections);
    } else {
      return this._dbNodes;
    }
  }

  private _getContainers(checkConnections = false): CoreNetworkDiagramContainer[] {
    if (checkConnections) {
      return this._dbContainers.map((c) => ({
        ...c,
        nodes: c.nodes ? this.patchNodesStatusByConnections(c.nodes, this._dbConnections) : [],
        cables: c.cables ?? [],
      }));
    } else {
      return this._dbContainers;
    }
  }

  private patchNodesStatusByConnections(
    nodes: CoreNetworkDiagramNode[],
    connections: CoreNetworkDiagramConnection[],
  ): CoreNetworkDiagramNode[] {
    if (connections.length) {
      for (const connection of connections) {
        // Find ports for each connection
        for (const node of nodes) {
          if (node.ports.some((p) => p.id === connection.outputId)) {
            const outputPort = node.ports.find((p) => p.id === connection.outputId)!;
            switch (node.nodeType) {
              case CoreNetworkDiagramNodeType.SWITCH:
                outputPort.status = CoreNetworkSwitchPortStatus.ONLINE;
                break;
              case CoreNetworkDiagramNodeType.PATCH_PANEL:
                outputPort.status =
                  outputPort.status === CoreNetworkPatchPanelPortStatus.NOT_IN_USE
                    ? CoreNetworkPatchPanelPortStatus.ONE_CONNECTED
                    : CoreNetworkPatchPanelPortStatus.TWO_CONNECTED;
                break;
            }
          } else if (node.ports.some((p) => p.id === connection.inputId)) {
            const inputPort = node.ports.find((p) => p.id === connection.inputId)!;
            switch (node.nodeType) {
              case CoreNetworkDiagramNodeType.SWITCH:
                inputPort.status = CoreNetworkSwitchPortStatus.ONLINE;
                break;
              case CoreNetworkDiagramNodeType.PATCH_PANEL:
                inputPort.status =
                  inputPort.status === CoreNetworkPatchPanelPortStatus.NOT_IN_USE
                    ? CoreNetworkPatchPanelPortStatus.ONE_CONNECTED
                    : CoreNetworkPatchPanelPortStatus.TWO_CONNECTED;
                break;
            }
          } else {
            // Set all the rest of the ports as not in use
            this.resetAllPortsStatus(node.ports, node.nodeType);
          }
        }
      }
    } else {
      // If no connections exist, reset all statuses
      nodes.forEach((node) => this.resetAllPortsStatus(node.ports, node.nodeType));
    }
    return nodes;
  }

  private resetAllPortsStatus(
    ports: CoreNetworkDiagramPort[],
    nodeType: CoreNetworkDiagramNodeType.SWITCH | CoreNetworkDiagramNodeType.PATCH_PANEL,
  ): void {
    ports.forEach((p) => {
      switch (nodeType) {
        case CoreNetworkDiagramNodeType.SWITCH:
          p.status = CoreNetworkSwitchPortStatus.NOT_IN_USE;
          break;
        case CoreNetworkDiagramNodeType.PATCH_PANEL:
          p.status = CoreNetworkPatchPanelPortStatus.NOT_IN_USE;
          break;
      }
    });
  }

  private async _createConnection(outputPortId: string, inputPortId: string): Promise<CoreNetworkDiagramConnection> {
    return new Promise((res) =>
      res({
        id: generateGuid(),
        label: 'Label',
        outputId: outputPortId,
        inputId: inputPortId,
      }),
    );
  }

  // Utilities

  private getTubeCssClass(index: number): string {
    return ['blue-tube', 'orange-tube', 'green-tube', 'brown-tube'][index];
  }
}
