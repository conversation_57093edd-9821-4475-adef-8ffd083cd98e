import {
  APIEventData,
  APIInputData,
  APIInterface,
  APIMethodData,
} from '../../../../components/api-view/api-view.interfaces';
import { CORE_ICON_INPUT } from '../../../icons/views/icon-api/icon-api.constants';

// INPUTS

const ID_FIELD: APIInputData = {
  name: 'id',
  type: 'string',
};

const OUTPUT_ID_FIELD: APIInputData = {
  name: 'outputId',
  type: 'string',
};

const INPUT_ID_FIELD: APIInputData = {
  name: 'inputId',
  type: 'string',
};

const LABEL_FIELD: APIInputData = {
  name: 'label',
  type: 'string',
};

const CSS_CLASS_FIELD: APIInputData = {
  name: 'cssClass',
  type: 'string',
  description: 'Optional',
};

const NODE_LAYOUT_COLUMNS_FIELD: APIInputData = {
  name: 'columns',
  type: 'number',
  description:
    'This field is used to indicate maximum number of columns for displaying ports, if not provided, all ports are rendered in one row.',
};

const SWITCH_NODE_TYPE_FIELD: APIInputData = {
  name: 'nodeType',
  type: 'CoreNetworkDiagramNodeType.SWITCH',
  description: 'Used to differentiate internally between node types, required field',
};

const PATCH_PANEL_NODE_TYPE_FIELD: APIInputData = {
  name: 'nodeType',
  type: 'CoreNetworkDiagramNodeType.PATCH_PANEL',
  description: 'Used to differentiate internally between node types, required field',
};

const PORT_CONNECTION_TYPE_INPUT: APIInputData = {
  name: 'connectionType',
  type: 'CoreNetworkConnectionType',
  description: 'Supperts ETHERNET and FIBER',
};

const SWITCH_PORT_FIELDS: APIInputData[] = [
  ID_FIELD,
  LABEL_FIELD,
  {
    name: 'status',
    type: 'CoreNetworkSwitchPortStatus',
    description: 'Supports OFFLINE, ONLINE and NOT_IN_USE',
  },
  PORT_CONNECTION_TYPE_INPUT,
];

const PATCH_PANEL_PORT_FIELDS: APIInputData[] = [
  ID_FIELD,
  LABEL_FIELD,
  {
    name: 'status',
    type: 'CoreNetworkPatchPanelPortStatus',
    description: 'Supports ONE_CONNECTED, TWO_CONNECTED and NOT_IN_USE',
  },
  PORT_CONNECTION_TYPE_INPUT,
];

const SWITCH_PORT_INTERFACE: APIInterface = {
  name: 'CoreNetworkSwitchPort',
  fields: SWITCH_PORT_FIELDS,
};

const PATCH_PANEL_PORT_INTERFACE: APIInterface = {
  name: 'CoreNetworkPatchPanelPort',
  fields: PATCH_PANEL_PORT_FIELDS,
};

const SWITCH_PORT_INPUT: APIInputData = {
  name: 'ports',
  type: 'CoreNetworkSwitchPort[]',
  interfaces: [SWITCH_PORT_INTERFACE],
};

const PATCH_PANEL_PORT_INPUT: APIInputData = {
  name: 'ports',
  type: 'CoreNetworkPatchPanelPort[]',
  interfaces: [PATCH_PANEL_PORT_INTERFACE],
};

const CORE_NET_DGRM_SWITCH: APIInterface = {
  name: 'CoreNetworkSwitch',
  fields: [ID_FIELD, LABEL_FIELD, SWITCH_PORT_INPUT, NODE_LAYOUT_COLUMNS_FIELD, SWITCH_NODE_TYPE_FIELD],
};

const CORE_NET_DGRM_PATCH_PANEL: APIInterface = {
  name: 'CoreNetworkPatchPanel',
  fields: [ID_FIELD, LABEL_FIELD, PATCH_PANEL_PORT_INPUT, NODE_LAYOUT_COLUMNS_FIELD, PATCH_PANEL_NODE_TYPE_FIELD],
};

const FIBER_STRAND_INTERFACE: APIInterface = {
  name: 'CoreNetworkDiagramFiberStrand',
  fields: [ID_FIELD, LABEL_FIELD, CSS_CLASS_FIELD],
};

const FIBER_STRANDS_INPUT: APIInputData = {
  name: 'strands',
  type: 'CoreNetworkDiagramFiberStrand[]',
  description: 'Required field',
  interfaces: [FIBER_STRAND_INTERFACE],
};

const FIBER_TUBE_INTERFACE: APIInterface = {
  name: 'CoreNetworkDiagramFiberCableTube',
  fields: [ID_FIELD, LABEL_FIELD, FIBER_STRANDS_INPUT, CSS_CLASS_FIELD],
};

const FIBER_TUBES_INPUT: APIInputData = {
  name: 'tubes',
  type: 'CoreNetworkDiagramFiberCableTube[]',
  description: 'Required field',
  interfaces: [FIBER_TUBE_INTERFACE],
};

const CORE_NET_DGRM_FIBER_CABLE: APIInterface = {
  name: 'CoreNetworkDiagramFiberCable',
  fields: [
    ID_FIELD,
    LABEL_FIELD,
    FIBER_TUBES_INPUT,
    {
      name: 'cableType',
      type: 'CoreNetworkDiagramCableType.FIBER',
    },
    CSS_CLASS_FIELD,
    {
      name: 'layoutDirection',
      type: 'CoreNetworkDiagramFiberCableLayoutDirection',
      description: 'Supports LEFT_TO_RIGHT and RIGHT_TO_LEFT',
    },
  ],
};

const CORE_NET_DGRM_ETHERNET_CABLE: APIInterface = {
  name: 'CoreNetworkDiagramEthernetCable',
  fields: [
    ID_FIELD,
    LABEL_FIELD,
    {
      name: 'cableType',
      type: 'CoreNetworkDiagramCableType.ETHERNET',
    },
    CSS_CLASS_FIELD,
  ],
};

export const CORE_NET_DGRM_NODES: APIInputData = {
  name: 'nodes',
  type: 'CoreNetworkDiagramNode[]',
  default: 'Empty array',
  description: 'Nodes outside containers, see CoreNetworkSwitch and CoreNetworkPatchPanel',
  interfaces: [CORE_NET_DGRM_FIBER_CABLE, CORE_NET_DGRM_ETHERNET_CABLE],
};

const CORE_NET_DGRM_CABLES: APIInputData = {
  name: 'cables',
  type: 'CoreNetworkDiagramCable[]',
  default: 'Empty array',
  interfaces: [CORE_NET_DGRM_SWITCH, CORE_NET_DGRM_PATCH_PANEL],
};

const CORE_NET_DGRM_CONTAINER: APIInterface = {
  name: 'CoreNetworkDiagramContainer',
  fields: [ID_FIELD, LABEL_FIELD, CORE_NET_DGRM_NODES, CORE_NET_DGRM_CABLES],
};

export const CORE_NET_DGRM_CONTAINERS: APIInputData = {
  name: 'containers',
  type: 'CoreNetworkDiagramContainer[]',
  default: 'Empty array',
  interfaces: [CORE_NET_DGRM_CONTAINER],
};

const CORE_NET_DGRM_CONNECTION: APIInterface = {
  name: 'CoreNetworkDiagramConnection',
  fields: [ID_FIELD, OUTPUT_ID_FIELD, INPUT_ID_FIELD, LABEL_FIELD],
};

export const CORE_NET_DGRM_CONNECTIONS: APIInputData = {
  name: 'connections',
  type: 'CoreNetworkDiagramConnection[]',
  interfaces: [CORE_NET_DGRM_CONNECTION],
  default: 'Empty array',
};

const CORE_NET_DGRM_POSITION_INTERFACE: APIInterface = {
  name: 'CoreNetworkDiagramElementPosition',
  fields: [
    { name: 'x', type: 'number' },
    { name: 'y', type: 'number' },
  ],
};

const CORE_NET_DGRM_POSITION_FIELD: APIInputData = {
  name: 'position',
  type: 'CoreNetworkDiagramElementPosition',
  interfaces: [CORE_NET_DGRM_POSITION_INTERFACE],
};

const CORE_NET_DGRM_ELEMENT_STATE: APIInterface = {
  name: 'CoreNetworkDiagramElement',
  fields: [ID_FIELD, CORE_NET_DGRM_POSITION_FIELD],
};

const CORE_NET_DGRM_NODES_STATE: APIInputData = {
  name: 'nodesState',
  type: 'CoreNetworkDiagramElement[]',
  interfaces: [CORE_NET_DGRM_ELEMENT_STATE],
};

const CORE_NET_DGRM_CABLES_STATE: APIInputData = {
  name: 'cablesState',
  type: 'CoreNetworkDiagramElement[]',
  interfaces: [CORE_NET_DGRM_ELEMENT_STATE],
};

const CORE_NET_DGRM_CONTAINERS_STATE_INTERFACE: APIInterface = {
  name: 'CoreNetworkDiagramContainerState',
  fields: [ID_FIELD, CORE_NET_DGRM_NODES_STATE, CORE_NET_DGRM_CABLES_STATE],
};

const CORE_NET_DGRM_CONTAINERS_STATE: APIInputData = {
  name: 'containersState',
  type: 'CoreNetworkDiagramContainerState[]',
  interfaces: [CORE_NET_DGRM_CONTAINERS_STATE_INTERFACE],
};

const CORE_NET_DGRM_STATE_INTERFACE: APIInterface = {
  name: 'CoreNetworkDiagramState',
  fields: [CORE_NET_DGRM_NODES_STATE, CORE_NET_DGRM_CONTAINERS_STATE],
};

export const CORE_NET_DGRM_STATE: APIInputData = {
  name: 'state',
  type: 'CoreNetworkDiagramState',
  interfaces: [CORE_NET_DGRM_STATE_INTERFACE],
};

const CORE_NET_DGRM_ACTION_ITEM_INTERFACE: APIInterface = {
  name: 'CoreNetworkDiagramActionItem',
  fields: [ID_FIELD, LABEL_FIELD, CORE_ICON_INPUT],
};

const CORE_NET_DGRM_ACTIONS_INTERFACE: APIInterface = {
  name: 'CoreNetworkDiagramActions',
  fields: [
    {
      name: 'connectionActions',
      type: 'CoreNetworkDiagramActionItem[]',
      interfaces: [CORE_NET_DGRM_ACTION_ITEM_INTERFACE],
    },
    {
      name: 'switchActions',
      type: 'CoreNetworkDiagramActionItem[]',
      interfaces: [CORE_NET_DGRM_ACTION_ITEM_INTERFACE],
    },
    {
      name: 'switchPortActions',
      type: 'CoreNetworkDiagramActionItem[]',
      interfaces: [CORE_NET_DGRM_ACTION_ITEM_INTERFACE],
    },
    {
      name: 'patchPanelActions',
      type: 'CoreNetworkDiagramActionItem[]',
      interfaces: [CORE_NET_DGRM_ACTION_ITEM_INTERFACE],
    },
    {
      name: 'patchPanelPortActions',
      type: 'CoreNetworkDiagramActionItem[]',
      interfaces: [CORE_NET_DGRM_ACTION_ITEM_INTERFACE],
    },
    {
      name: 'fiberCableActions',
      type: 'CoreNetworkDiagramActionItem[]',
      interfaces: [CORE_NET_DGRM_ACTION_ITEM_INTERFACE],
    },
    {
      name: 'fiberTubeActions',
      type: 'CoreNetworkDiagramActionItem[]',
      interfaces: [CORE_NET_DGRM_ACTION_ITEM_INTERFACE],
    },
    {
      name: 'fiberStrandActions',
      type: 'CoreNetworkDiagramActionItem[]',
      interfaces: [CORE_NET_DGRM_ACTION_ITEM_INTERFACE],
    },
    {
      name: 'ethernetCableActions',
      type: 'CoreNetworkDiagramActionItem[]',
      interfaces: [CORE_NET_DGRM_ACTION_ITEM_INTERFACE],
    },
  ],
};

const CORE_NET_DGRM_ACTIONS: APIInputData = {
  name: 'actions',
  type: 'CoreNetworkDiagramActions',
  interfaces: [CORE_NET_DGRM_ACTIONS_INTERFACE],
};

const CORE_NET_DGRM_OPTIONS_INTERFACE: APIInterface = {
  name: 'CoreNetworkDiagramOptions',
  fields: [
    {
      name: 'connectionCssClass',
      type: 'string',
    },
    CORE_NET_DGRM_ACTIONS,
  ],
};

export const CORE_NET_DGRM_OPTIONS: APIInputData = {
  name: 'options',
  type: 'CoreNetworkDiagramOptions',
  interfaces: [CORE_NET_DGRM_OPTIONS_INTERFACE],
};

// OUTPUTS / EVENTS

export const CORE_NET_DGRM_STATE_CHANGE: APIEventData = {
  name: 'stateChange',
  description: 'Emits value of current state of type CoreNetworkDiagramState',
};

export const CORE_NET_DGRM_CONNECTION_CREATE: APIEventData = {
  name: 'connectionCreate',
  description: 'Emits value of type CoreNetworkDiagramConnectionEvent',
};

export const CORE_NET_DGRM_CONNECTION_FAILED: APIEventData = {
  name: 'connectionFailed',
  description: 'Emits value of type object: { error: string }',
};

// TODO: missing imports below

export const CORE_NET_DGRM_CONNECTION_ACTION: APIEventData = {
  name: 'connectionAction',
  description: 'Emits value of type object: { actionId: string; connectionId: string }',
};

export const CORE_NET_DGRM_SWITCH_ACTION: APIEventData = {
  name: 'switchAction',
  description: 'Emits value of type object: { switchId: string; actionId: string }',
};

export const CORE_NET_DGRM_SWITCH_PORT_ACTION: APIEventData = {
  name: 'switchPortAction',
  description: 'Emits value of type object: { switchId: string; portId: string; actionId: string }',
};

export const CORE_NET_DGRM_PATCH_PANEL_ACTION: APIEventData = {
  name: 'patchPanelAction',
  description: 'Emits value of type object: { patchPanelId: string; actionId: string }',
};

export const CORE_NET_DGRM_PATCH_PANEL_PORT_ACTION: APIEventData = {
  name: 'patchPanelPortAction',
  description: 'Emits value of type object: { patchPanelId: string; portId: string; actionId: string }',
};

export const CORE_NET_DGRM_ETHERNET_CABLE_ACTION: APIEventData = {
  name: 'ethernetCableAction',
  description: 'Emits value of type object: { cableId: string; actionId: string }',
};

export const CORE_NET_DGRM_FIBER_CABLE_ACTION: APIEventData = {
  name: 'fiberCableAction',
  description: 'Emits value of type object: { cableId: string; actionId: string }',
};

export const CORE_NET_DGRM_FIBER_TUBE_ACTION: APIEventData = {
  name: 'fiberTubeAction',
  description: 'Emits value of type object: { cableId: string; tubeId: string; actionId: string }',
};

export const CORE_NET_DGRM_FIBER_STRAND_ACTION: APIEventData = {
  name: 'fiberStrandAction',
  description: 'Emits value of type object: { cableId: string; tubeId: string; strandId: string; actionId: string }',
};

// METHODS

export const CORE_NET_DGRM_SET_NODES_METHOD: APIMethodData = {
  name: 'setNodes',
  description: `Required Params: 'nodes' of type CoreNetworkDiagramNode[]`,
};

export const CORE_NET_DGRM_SET_CONTAINERS_METHOD: APIMethodData = {
  name: 'setContainers',
  description: `Required Params: 'containers' of type CoreNetworkDiagramContainer[]`,
};

export const CORE_NET_DGRM_SET_CONNECTIONS_METHOD: APIMethodData = {
  name: 'setConnections',
  description: `Required Params: 'connections' of type CoreNetworkDiagramConnection[]`,
};

export const CORE_NET_DGRM_REFRESH_CANVAS_METHOD: APIMethodData = {
  name: 'refreshCanvas',
  description: 'Needs 100ms to complete',
};
