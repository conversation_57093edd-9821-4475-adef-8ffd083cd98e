import { Component } from '@angular/core';
import { ApiViewComponent } from '../../../../components/api-view/api-view.component';
import { APIInputData, APIEventData, APIMethodData } from '../../../../components/api-view/api-view.interfaces';
import {
  CORE_NET_DGRM_CONNECTION_ACTION,
  CORE_NET_DGRM_CONNECTION_CREATE,
  CORE_NET_DGRM_CONNECTION_FAILED,
  CORE_NET_DGRM_CONNECTIONS,
  CORE_NET_DGRM_CONTAINERS,
  CORE_NET_DGRM_ETHERNET_CABLE_ACTION,
  CORE_NET_DGRM_FIBER_CABLE_ACTION,
  CORE_NET_DGRM_FIBER_STRAND_ACTION,
  CORE_NET_DGRM_FIBER_TUBE_ACTION,
  CORE_NET_DGRM_NODES,
  CORE_NET_DGRM_OPTIONS,
  CORE_NET_DGRM_PATCH_PANEL_ACTION,
  CORE_NET_DGRM_PATCH_PANEL_PORT_ACTION,
  CORE_NET_DGRM_REFRESH_CANVAS_METHOD,
  CORE_NET_DGRM_SET_CONNECTIONS_METHOD,
  CORE_NET_DGRM_SET_CONTAINERS_METHOD,
  CORE_NET_DGRM_SET_NODES_METHOD,
  CORE_NET_DGRM_STATE,
  CORE_NET_DGRM_STATE_CHANGE,
  CORE_NET_DGRM_SWITCH_ACTION,
  CORE_NET_DGRM_SWITCH_PORT_ACTION,
} from './network-diagram-api.constants';

@Component({
  selector: 'app-network-diagram-api',
  imports: [ApiViewComponent],
  templateUrl: './network-diagram-api.html',
  styleUrl: './network-diagram-api.scss',
})
export class NetworkDiagramApiComponent {
  public inputsData: APIInputData[] = [
    CORE_NET_DGRM_NODES,
    CORE_NET_DGRM_CONTAINERS,
    CORE_NET_DGRM_CONNECTIONS,
    CORE_NET_DGRM_STATE,
    CORE_NET_DGRM_OPTIONS,
  ];

  public outputsData: APIEventData[] = [
    CORE_NET_DGRM_STATE_CHANGE,
    CORE_NET_DGRM_CONNECTION_CREATE,
    CORE_NET_DGRM_CONNECTION_FAILED,
    CORE_NET_DGRM_CONNECTION_ACTION,
    CORE_NET_DGRM_SWITCH_ACTION,
    CORE_NET_DGRM_SWITCH_PORT_ACTION,
    CORE_NET_DGRM_PATCH_PANEL_ACTION,
    CORE_NET_DGRM_PATCH_PANEL_PORT_ACTION,
    CORE_NET_DGRM_ETHERNET_CABLE_ACTION,
    CORE_NET_DGRM_FIBER_CABLE_ACTION,
    CORE_NET_DGRM_FIBER_TUBE_ACTION,
    CORE_NET_DGRM_FIBER_STRAND_ACTION,
  ];

  public methodsData: APIMethodData[] = [
    CORE_NET_DGRM_SET_NODES_METHOD,
    CORE_NET_DGRM_SET_CONTAINERS_METHOD,
    CORE_NET_DGRM_SET_CONNECTIONS_METHOD,
    CORE_NET_DGRM_REFRESH_CANVAS_METHOD,
  ];
}
