.switch {
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  background: rgb(82, 82, 82);
  color: var(--selected-text);
  height: 120px;
  width: 410px;
  overflow: hidden;
}

.patch-panel {
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  background: rgb(82, 82, 82);
  color: var(--selected-text);
  height: 160px;
  width: 410px;
  overflow: hidden;
}

.switch-header,
.patch-panel-header {
  display: flex;
  flex-flow: row nowrap;
  padding: 1rem;
  gap: 1rem;
  background: gray;
}

.spacer {
  flex: 1 1 0;
}

.port {
  border: 1px solid var(--border-color);
  height: 40px;
  width: 40px;
  background: #363636;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--selected-text);
}

.selected-port {
  border-color: var(--accent-primary-color);
}

.connection-ok {
  background: green;
}

.connection-error {
  background: #a84c4c;
}

// Connection Overrides
::ng-deep f-flow {
  .f-connection {
    text {
      fill: #fff;
    }

    .f-connection-drag-handle {
      fill: none;
      stroke: none;
    }

    .f-connection-selection {
      stroke-width: 20;
    }

    .f-connection-path {
      stroke: #ffffff;
      stroke-width: 3;

      &:hover {
        stroke: var(--accent-primary-color);
      }
    }

    &.selected-connection {
      .f-connection-path {
        stroke: var(--accent-primary-color);
      }

      text {
        fill: var(--accent-primary-color);
      }
    }
  }
}
