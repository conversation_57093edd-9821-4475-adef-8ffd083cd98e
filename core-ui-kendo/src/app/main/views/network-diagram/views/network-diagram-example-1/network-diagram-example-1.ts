import { Component, signal, viewChild } from '@angular/core';
import { KENDO_MENUS } from '@progress/kendo-angular-menu';
import { FCanvasComponent, FCreateConnectionEvent, FFlowModule } from '@foblex/flow';
import { CoreIcon, CoreIconModule } from '../../../../../core/components/core-icon';
import { CONNECTIONS, PATCH_PANEL_1, PortStatus, SWITCH_1 } from './network-diagram-example-1.constants';
import { generateGuid } from '@foblex/utils';

interface GraphElement {
  id: string;
  position: {
    x: number;
    y: number;
  };
}
export interface Port extends GraphElement {
  label: string;
  status: PortStatus;
}

export interface PortsGroup extends GraphElement {
  label: string;
  icon: CoreIcon;
  ports: Port[];
  totalPorts: number;
  portsInUse: number;
}

export interface Connection {
  id: string;
  outputId: string;
  inputId: string;
  label: string;
}

@Component({
  selector: 'app-network-diagram-example-1',
  templateUrl: './network-diagram-example-1.html',
  styleUrl: './network-diagram-example-1.scss',
  host: { class: 'view-component' },
  imports: [KENDO_MENUS, FFlowModule, CoreIconModule],
})
export class NetworkDiagramExample1Component {
  public switches = signal<PortsGroup[]>(this.getSwitches());
  public patchPanels = signal<PortsGroup[]>(this.getPatchPanels());
  public connections = signal<Connection[]>(this.getConnections());
  public selectedConnectionId = signal<string | null>(null);
  public selectedPortId = signal<string | null>(null);
  public fCanvas = viewChild(FCanvasComponent);

  public connectionMenuItems = [{ text: 'View Details' }, { text: 'Delete Connection' }];

  public portMenuItems = [{ text: 'View Details' }];

  public onLoaded(): void {
    this.fCanvas()?.resetScaleAndCenter(false);
  }

  public onConnectionAction(actionText: string): void {
    switch (actionText) {
      case 'View Details':
        console.log('View Details');
        break;
      case 'Delete Connection':
        this.onDeleteConnection();
        break;
      default:
        break;
    }
  }

  public onPortAction(actionText: string): void {
    switch (actionText) {
      case 'View Details':
        console.log('View Details');
        break;
      default:
        break;
    }
  }

  public addConnection(event: FCreateConnectionEvent): void {
    if (!event.fInputId) {
      return;
    }
    const connections = [
      ...this.connections(),
      {
        id: generateGuid(),
        outputId: event.fOutputId,
        inputId: event.fInputId,
        label: 'New Connection',
      },
    ];
    this.connections.set(connections);
    // Set nodes status as Connected OK
    this.switches()[0].ports.find((p) => p.id === event.fOutputId)!.status = PortStatus.CONNECTION_OK;
    this.patchPanels()[0].ports.find((p) => p.id === event.fInputId)!.status = PortStatus.CONNECTION_OK;
    // Update ports in use
    this.switches()[0].portsInUse = this.switches()[0].ports.filter(
      (p) => p.status !== PortStatus.NOT_CONNECTED,
    ).length;
    this.patchPanels()[0].portsInUse = this.patchPanels()[0].ports.filter(
      (p) => p.status !== PortStatus.NOT_CONNECTED,
    ).length;
  }

  public onDeleteConnection(): void {
    const connection = this.connections().find((c) => c.id === this.selectedConnectionId());
    if (connection) {
      // Remove connection
      const connections = this.connections().filter((c) => c.id !== connection.id);
      this.connections.set(connections);
      // Reset ports on each end
      this.switches()[0].ports.find((p) => p.id === connection.outputId)!.status = PortStatus.NOT_CONNECTED;
      this.patchPanels()[0].ports.find((p) => p.id === connection.inputId)!.status = PortStatus.NOT_CONNECTED;
      // Update ports in use
      this.switches()[0].portsInUse = this.switches()[0].ports.filter(
        (p) => p.status !== PortStatus.NOT_CONNECTED,
      ).length;
      this.patchPanels()[0].portsInUse = this.switches()[0].ports.filter(
        (p) => p.status !== PortStatus.NOT_CONNECTED,
      ).length;
    }
  }

  private getSwitches(): PortsGroup[] {
    return [SWITCH_1];
  }

  private getConnections(): Connection[] {
    return CONNECTIONS;
  }

  private getPatchPanels(): PortsGroup[] {
    return [PATCH_PANEL_1];
  }
}
