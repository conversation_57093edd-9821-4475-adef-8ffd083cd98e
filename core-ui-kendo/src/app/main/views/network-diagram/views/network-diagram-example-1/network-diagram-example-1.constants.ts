import { generateGuid } from '@foblex/utils';
import { connectorIcon } from '@progress/kendo-svg-icons';
import { Connection, Port, PortsGroup } from './network-diagram-example-1';

export enum PortStatus {
  NOT_CONNECTED = 'NOT_CONNECTED',
  CONNECTION_OK = 'CONNECTION_OK',
  CONNECTION_ERROR = 'CONNECTION_ERROR',
}

const SWITCH_1_PORT_1: Port = {
  id: generateGuid(),
  label: '1',
  position: {
    x: 10,
    y: 58,
  },
  status: PortStatus.CONNECTION_OK,
};

const SWITCH_1_PORT_2: Port = {
  id: generateGuid(),
  label: '2',
  position: {
    x: 60,
    y: 58,
  },
  status: PortStatus.CONNECTION_ERROR,
};

const SWITCH_1_PORT_3: Port = {
  id: generateGuid(),
  label: '3',
  position: {
    x: 110,
    y: 58,
  },
  status: PortStatus.CONNECTION_ERROR,
};

const SWITCH_1_PORT_4: Port = {
  id: generateGuid(),
  label: '4',
  position: {
    x: 160,
    y: 58,
  },
  status: PortStatus.CONNECTION_OK,
};

const SWITCH_1_PORT_5: Port = {
  id: generateGuid(),
  label: '5',
  position: {
    x: 210,
    y: 58,
  },
  status: PortStatus.NOT_CONNECTED,
};

const SWITCH_1_PORT_6: Port = {
  id: generateGuid(),
  label: '6',
  position: {
    x: 260,
    y: 58,
  },
  status: PortStatus.NOT_CONNECTED,
};

const SWITCH_1_PORT_7: Port = {
  id: generateGuid(),
  label: '7',
  position: {
    x: 310,
    y: 58,
  },
  status: PortStatus.NOT_CONNECTED,
};

const SWITCH_1_PORT_8: Port = {
  id: generateGuid(),
  label: '8',
  position: {
    x: 360,
    y: 58,
  },
  status: PortStatus.NOT_CONNECTED,
};

export const SWITCH_1: PortsGroup = {
  id: generateGuid(),
  icon: connectorIcon,
  label: 'Switch 1',
  position: {
    x: 0,
    y: 0,
  },
  ports: [
    SWITCH_1_PORT_1,
    SWITCH_1_PORT_2,
    SWITCH_1_PORT_3,
    SWITCH_1_PORT_4,
    SWITCH_1_PORT_5,
    SWITCH_1_PORT_6,
    SWITCH_1_PORT_7,
    SWITCH_1_PORT_8,
  ],
  totalPorts: 8,
  portsInUse: 4,
};

const PATCH_PANEL_1_PORT_1: Port = {
  id: generateGuid(),
  label: '1',
  position: {
    x: 10,
    y: 58 + 400,
  },
  status: PortStatus.CONNECTION_OK,
};

const PATCH_PANEL_1_PORT_2: Port = {
  id: generateGuid(),
  label: '2',
  position: {
    x: 60,
    y: 58 + 400,
  },
  status: PortStatus.CONNECTION_ERROR,
};

const PATCH_PANEL_1_PORT_3: Port = {
  id: generateGuid(),
  label: '3',
  position: {
    x: 110,
    y: 58 + 400,
  },
  status: PortStatus.CONNECTION_ERROR,
};

const PATCH_PANEL_1_PORT_4: Port = {
  id: generateGuid(),
  label: '4',
  position: {
    x: 160,
    y: 58 + 400,
  },
  status: PortStatus.CONNECTION_OK,
};

const PATCH_PANEL_1_PORT_5: Port = {
  id: generateGuid(),
  label: '5',
  position: {
    x: 210,
    y: 58 + 400,
  },
  status: PortStatus.NOT_CONNECTED,
};

const PATCH_PANEL_1_PORT_6: Port = {
  id: generateGuid(),
  label: '6',
  position: {
    x: 260,
    y: 58 + 400,
  },
  status: PortStatus.NOT_CONNECTED,
};

const PATCH_PANEL_1_PORT_7: Port = {
  id: generateGuid(),
  label: '7',
  position: {
    x: 310,
    y: 58 + 400,
  },
  status: PortStatus.NOT_CONNECTED,
};

const PATCH_PANEL_1_PORT_8: Port = {
  id: generateGuid(),
  label: '8',
  position: {
    x: 360,
    y: 58 + 400,
  },
  status: PortStatus.NOT_CONNECTED,
};

const PATCH_PANEL_1_PORT_9: Port = {
  id: generateGuid(),
  label: '9',
  position: {
    x: 10,
    y: 58 + 400 + 50,
  },
  status: PortStatus.NOT_CONNECTED,
};

const PATCH_PANEL_1_PORT_10: Port = {
  id: generateGuid(),
  label: '10',
  position: {
    x: 60,
    y: 58 + 400 + 50,
  },
  status: PortStatus.NOT_CONNECTED,
};

const PATCH_PANEL_1_PORT_11: Port = {
  id: generateGuid(),
  label: '11',
  position: {
    x: 110,
    y: 58 + 400 + 50,
  },
  status: PortStatus.NOT_CONNECTED,
};

const PATCH_PANEL_1_PORT_12: Port = {
  id: generateGuid(),
  label: '12',
  position: {
    x: 160,
    y: 58 + 400 + 50,
  },
  status: PortStatus.NOT_CONNECTED,
};

const PATCH_PANEL_1_PORT_13: Port = {
  id: generateGuid(),
  label: '13',
  position: {
    x: 210,
    y: 58 + 400 + 50,
  },
  status: PortStatus.NOT_CONNECTED,
};

const PATCH_PANEL_1_PORT_14: Port = {
  id: generateGuid(),
  label: '14',
  position: {
    x: 260,
    y: 58 + 400 + 50,
  },
  status: PortStatus.NOT_CONNECTED,
};

const PATCH_PANEL_1_PORT_15: Port = {
  id: generateGuid(),
  label: '15',
  position: {
    x: 310,
    y: 58 + 400 + 50,
  },
  status: PortStatus.NOT_CONNECTED,
};

const PATCH_PANEL_1_PORT_16: Port = {
  id: generateGuid(),
  label: '16',
  position: {
    x: 360,
    y: 58 + 400 + 50,
  },
  status: PortStatus.NOT_CONNECTED,
};

export const PATCH_PANEL_1: PortsGroup = {
  id: generateGuid(),
  icon: connectorIcon,
  totalPorts: 16,
  portsInUse: 4,
  label: 'Patch Panel 1',
  position: {
    x: 0,
    y: 400,
  },
  ports: [
    PATCH_PANEL_1_PORT_1,
    PATCH_PANEL_1_PORT_2,
    PATCH_PANEL_1_PORT_3,
    PATCH_PANEL_1_PORT_4,
    PATCH_PANEL_1_PORT_5,
    PATCH_PANEL_1_PORT_6,
    PATCH_PANEL_1_PORT_7,
    PATCH_PANEL_1_PORT_8,
    PATCH_PANEL_1_PORT_9,
    PATCH_PANEL_1_PORT_10,
    PATCH_PANEL_1_PORT_11,
    PATCH_PANEL_1_PORT_12,
    PATCH_PANEL_1_PORT_13,
    PATCH_PANEL_1_PORT_14,
    PATCH_PANEL_1_PORT_15,
    PATCH_PANEL_1_PORT_16,
  ],
};

export const CONNECTIONS: Connection[] = [
  {
    id: generateGuid(),
    outputId: SWITCH_1_PORT_1.id,
    inputId: PATCH_PANEL_1_PORT_1.id,
    label: 'Ethernet',
  },
  {
    id: generateGuid(),
    outputId: SWITCH_1_PORT_2.id,
    inputId: PATCH_PANEL_1_PORT_2.id,
    label: 'Ethernet',
  },
  {
    id: generateGuid(),
    outputId: SWITCH_1_PORT_3.id,
    inputId: PATCH_PANEL_1_PORT_3.id,
    label: 'Fiber',
  },
  {
    id: generateGuid(),
    outputId: SWITCH_1_PORT_4.id,
    inputId: PATCH_PANEL_1_PORT_4.id,
    label: 'Fiber',
  },
];
