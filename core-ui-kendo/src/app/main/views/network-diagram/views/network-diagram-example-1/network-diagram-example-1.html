<f-flow fDraggable (fLoaded)="onLoaded()" (fCreateConnection)="addConnection($event)">
  <f-canvas fZoom>
    <!-- Switches -->
    @for(switch of switches(); track switch.id) {
    <div class="switch" fGroup [fGroupId]="switch.id" fDragHandle [fGroupPosition]="switch.position">
      <!-- Header -->
      <div class="switch-header">
        <core-icon [icon]="switch.icon"></core-icon>
        {{switch.label}}
        <div class="spacer"></div>
        Ports Used: {{switch.portsInUse}} of {{switch.totalPorts}}
      </div>
      <!-- Ports -->
      @for(port of switch.ports; track port.id) {
      <button
        class="port"
        [class.connection-ok]="port.status === 'CONNECTION_OK'"
        [class.connection-error]="port.status === 'CONNECTION_ERROR'"
        [class.selected-port]="selectedPortId() === port.id"
        fNode
        [fNodeParentId]="switch.id"
        [fNodeId]="port.id"
        fNodeOutput
        [fOutputId]="port.id"
        fOutputConnectableSide="top"
        [fNodePosition]="port.position"
        (contextmenu)="selectedPortId.set(port.id)"
        (click)="selectedPortId.set(port.id)"
      >
        {{port.label}}
      </button>
      }
    </div>
    }

    <!-- Patch Panels -->
    @for(panel of patchPanels(); track panel.id) {
    <div class="patch-panel" fGroup [fGroupId]="panel.id" fDragHandle [fGroupPosition]="{ x: 0, y: 400 }">
      <!-- Header -->
      <div class="patch-panel-header">
        <core-icon [icon]="panel.icon"></core-icon>
        {{panel.label}}
        <div class="spacer"></div>
        Ports Used: {{panel.portsInUse}} of {{panel.totalPorts}}
      </div>
      <!-- Ports -->
      @for(port of panel.ports; track port.id) {
      <button
        class="port"
        [class.connection-ok]="port.status === 'CONNECTION_OK'"
        [class.connection-error]="port.status === 'CONNECTION_ERROR'"
        [class.selected-port]="selectedPortId() === port.id"
        fNode
        [fNodeParentId]="panel.id"
        [fNodeId]="port.id"
        fNodeInput
        [fInputId]="port.id"
        fInputConnectableSide="top"
        [fNodePosition]="port.position"
        (contextmenu)="selectedPortId.set(port.id)"
      >
        {{port.label}}
      </button>
      }
    </div>
    }

    <!-- Connections -->
    @for(connection of connections(); track connection.id; let index = $index) {
    <f-connection
      [fConnectionId]="connection.id"
      (contextmenu)="selectedConnectionId.set(connection.id)"
      (click)="selectedConnectionId.set(connection.id)"
      [class.selected-connection]="selectedConnectionId() === connection.id"
      [fOutputId]="connection.outputId"
      [fInputId]="connection.inputId"
      [fText]="connection.label"
      [fReassignableStart]="true"
      fBehavior="fixed"
      fType="segment"
      [fOffset]="index * 30 + 20"
    >
    </f-connection>
    }

    <!-- Connection for create -->
    <f-connection-for-create></f-connection-for-create>
  </f-canvas>
</f-flow>

<!-- Context Menu -->
@if(fCanvas()) {
<!-- Connections -->
<kendo-contextmenu
  [target]="fCanvas()!.hostElement"
  [items]="connectionMenuItems"
  [filter]="'f-connection'"
  (select)="onConnectionAction($event.item.text)"
></kendo-contextmenu>
<!-- Ports -->
<kendo-contextmenu
  [target]="fCanvas()!.hostElement"
  [items]="portMenuItems"
  [filter]="'.port'"
  (select)="onPortAction($event.item.text)"
></kendo-contextmenu>
}
