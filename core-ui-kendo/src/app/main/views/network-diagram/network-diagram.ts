import { Component, Signal, signal } from '@angular/core';
import { CoreNavigationTabs, CoreNavigationTabsModule } from '../../../core/components/core-navigation-tabs';

@Component({
  selector: 'app-network-diagram',
  templateUrl: './network-diagram.html',
  styleUrl: './network-diagram.scss',
  host: { class: 'view-component' },
  imports: [CoreNavigationTabsModule],
})
export class NetworkDiagramComponent {
  public navigationTabs: Signal<CoreNavigationTabs>;

  constructor() {
    this.navigationTabs = signal(
      new CoreNavigationTabs({
        tabs: [
          { label: 'Network Diagram Example 1', route: 'network-diagram-example-1' },
          { label: 'Network Diagram Example 2', route: 'network-diagram-example-2' },
          { label: 'API', route: 'network-diagram-api' },
        ],
        autoNavigate: true,
      }),
    );
  }
}
