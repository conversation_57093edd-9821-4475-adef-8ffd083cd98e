import { Component, Signal, signal } from '@angular/core';
import { CoreNavigationTabs } from '../../../../../core/components/core-navigation-tabs';
import { CoreNavigationTabsComponent } from '../../../../../core/components/core-navigation-tabs/core-navigation-tabs.component';

@Component({
  selector: 'app-backgrounds',
  templateUrl: './backgrounds.component.html',
  styleUrl: './backgrounds.component.scss',
  host: { class: 'view-component' },
  imports: [CoreNavigationTabsComponent],
})
export class BackgroundsComponent {
  public navigationTabs: Signal<CoreNavigationTabs>;

  constructor() {
    this.navigationTabs = signal(
      new CoreNavigationTabs({
        tabs: [{ label: 'Squares', route: 'squares' }],
        autoNavigate: true,
      }),
    );
  }
}
