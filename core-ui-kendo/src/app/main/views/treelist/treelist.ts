import { Component, Signal, signal } from '@angular/core';
import { CoreNavigationTabs, CoreNavigationTabsModule } from '../../../core/components/core-navigation-tabs';

@Component({
  selector: 'app-treelist',
  templateUrl: './treelist.html',
  styleUrl: './treelist.scss',
  host: { class: 'view-component' },
  imports: [CoreNavigationTabsModule],
})
export class Treelist {
  public navigationTabs: Signal<CoreNavigationTabs>;

  constructor() {
    this.navigationTabs = signal(
      new CoreNavigationTabs({
        tabs: [
          { label: 'Treelist Example', route: 'treelist-example' },
          { label: 'API', route: 'treelist-api' },
        ],
        autoNavigate: true,
      }),
    );
  }
}
