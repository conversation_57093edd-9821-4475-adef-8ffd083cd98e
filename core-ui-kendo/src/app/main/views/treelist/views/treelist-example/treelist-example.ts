import { Component, signal } from '@angular/core';
import { CoreCardModule } from '../../../../../core/components/core-card';
import { CoreTreelistItem, CoreTreelistModule } from '../../../../../core/components/core-treelist';
import { CheckedTreeListItem } from '../../../../../core/components/core-treelist/core-treelist.interfaces';
import { fileIcon, folderIcon } from '@progress/kendo-svg-icons';

@Component({
  selector: 'app-treelist-example',
  templateUrl: './treelist-example.html',
  styleUrl: './treelist-example.scss',
  host: { class: 'view-component' },
  imports: [CoreCardModule, CoreTreelistModule],
})
export class TreelistExample {
  public treelistItems = signal<CoreTreelistItem[]>(this.getTreelistItems());

  public onCheckedChange(e: CheckedTreeListItem[]): void {
    console.log(e);
  }

  // Helpers

  private getTreelistItems(): CoreTreelistItem[] {
    return [
      {
        id: 1,
        label: 'Root Item 1',
        icon: folderIcon,
        children: [
          {
            id: 11,
            label: 'Child 1 of Root 1',
            icon: fileIcon,
          },
          {
            id: 12,
            label: 'Child 2 of Root 1',
            icon: folderIcon,
            children: [
              {
                id: 121,
                label: 'Child 1 of Child 2 of Root 1',
                icon: fileIcon,
              },
              {
                id: 122,
                label: 'Child 2 of Child 2 of Root 1',
                icon: fileIcon,
              },
              {
                id: 123,
                label: 'Child 3 of Child 2 of Root 1',
                icon: fileIcon,
              },
            ],
          },
        ],
      },
      {
        id: 2,
        label: 'Root Item 2',
        icon: folderIcon,
      },
    ];
  }
}
