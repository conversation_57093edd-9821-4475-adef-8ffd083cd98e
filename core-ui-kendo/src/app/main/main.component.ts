// Angular
import { Component, effect, inject, model, OnInit, signal } from '@angular/core';
import { RouterModule } from '@angular/router';
// Core UI
import { CoreAppbarModule } from '../core/components/core-appbar';
import { CoreButtonModule } from '../core/components/core-button';
import { CoreNotificationModule, CoreNotificationService } from '../core/popups/core-notification';
// Components
import { HeaderComponent } from './components/header/header.component';
// Services
import { ThemeService } from '../shared/services/theme.service';
// Constants
import { NAVIGATION_ITEMS } from './main.constants';
import { CoreNavigationModule } from '../core/components/core-navigation/core-navigation.module';

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrl: './main.component.scss',
  providers: [ThemeService],
  imports: [
    RouterModule,
    CoreAppbarModule,
    CoreButtonModule,
    CoreNotificationModule,
    HeaderComponent,
    CoreNavigationModule,
  ],
})
export class MainComponent implements OnInit {
  public year: number = new Date().getFullYear();
  // public navigation: CoreNavigation;
  public theme = model<'light' | 'dark'>('light');
  public userName = signal<string | null>('User Name');
  public navigationItems = NAVIGATION_ITEMS;
  public navigationIsExpanded = signal(true);

  private _coreNotify = inject(CoreNotificationService);
  private _themeService = inject(ThemeService);

  constructor() {
    // Initialize navigation
    // this.navigation = new CoreNavigation(NAVIGATION_ITEMS, {
    //   panelWidth: 250,
    //   autoNavigate: true,
    //   containerMinWidth: 768,
    //   miniMode: true,
    //   miniWidth: 50,
    //   iconSize: 'xlarge',
    //   itemCssStyle: 'font-size: 1.3rem; padding: .75rem;',
    // });

    // Set theme on change
    effect(() => {
      this._themeService.setTheme(this.theme());
    });
  }

  public ngOnInit(): void {
    this.theme.set(this._themeService.getTheme());
  }

  // [ Public events ]

  public onToggleNavigationPanel(): void {
    this.navigationIsExpanded.set(!this.navigationIsExpanded());
  }

  public onLogout(): void {
    this._coreNotify.success('Logging out...');
  }

  public setTheme(theme: 'light' | 'dark'): void {
    this.theme.set(theme);
    this._themeService.setTheme(theme);
  }
}
