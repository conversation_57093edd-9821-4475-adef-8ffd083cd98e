import {
  buttonIcon,
  windowIcon,
  formElementIcon,
  commentIcon,
  tableIcon,
  positionTopIcon,
  moreHorizontalIcon,
  globeOutlineIcon,
  layoutIcon,
  textboxIcon,
  calendarIcon,
  infoCircleIcon,
  transparencyIcon,
  displayBlockIcon,
  thumbnailsDownIcon,
  minusIcon,
  freeTextIcon,
  alignRightIcon,
  dropdownIcon,
  moreVerticalIcon,
  connectorIcon,
} from '@progress/kendo-svg-icons';
import {
  CoreNavigationFolder,
  CoreNavigationItem,
} from '../core/components/core-navigation/core-navigation.interfaces';

export const NAVIGATION_ITEMS: (CoreNavigationItem | CoreNavigationFolder)[] = [
  {
    id: 'primitives',
    label: 'Primitives',
    icon: displayBlockIcon,
    children: [
      {
        id: 'button',
        label: 'Button',
        route: 'buttons',
        icon: buttonIcon,
      },
      {
        id: 'dropdown-button',
        label: 'Dropdown Button',
        route: 'dropdown-buttons',
        icon: dropdownIcon,
      },
      {
        id: 'card',
        label: 'Card',
        route: 'card',
        icon: windowIcon,
      },
      {
        id: 'controls',
        label: 'Controls',
        route: 'controls',
        icon: textboxIcon,
      },
      {
        id: 'icon',
        label: 'Icon',
        route: 'icons',
        icon: freeTextIcon,
      },
      {
        id: 'progress-bar',
        label: 'Progress Bar',
        route: 'progress-bar',
        icon: minusIcon,
      },
    ],
  },
  {
    id: 'complex',
    label: 'Complex',
    icon: thumbnailsDownIcon,
    children: [
      {
        id: 'toolbar',
        label: 'Toolbar',
        route: 'toolbar',
        icon: positionTopIcon,
      },
      {
        id: 'breadcrumbs',
        label: 'Breadcrumbs',
        route: 'breadcrumbs',
        icon: moreHorizontalIcon,
      },
      {
        id: 'form',
        label: 'Form',
        route: 'form',
        icon: formElementIcon,
      },
      {
        id: 'tree-view',
        label: 'Tree View',
        route: 'tree-view',
        icon: alignRightIcon,
      },
      {
        id: 'table',
        label: 'Table',
        route: 'tables',
        icon: tableIcon,
      },
      {
        id: 'popups',
        label: 'Popups',
        route: 'popups',
        icon: commentIcon,
      },
      {
        id: 'dashboard',
        label: 'Dashboard',
        icon: layoutIcon,
        route: 'dashboard',
      },
      {
        id: 'map',
        label: 'Map',
        route: 'map',
        icon: globeOutlineIcon,
      },
      {
        id: 'scheduler',
        label: 'Scheduler',
        route: 'scheduler',
        icon: calendarIcon,
      },
      {
        id: 'network-diagram',
        label: 'Network Diagram',
        route: 'network-diagram',
        icon: connectorIcon,
      },
      {
        id: 'pixel-editor',
        label: 'Pixel Editor',
        route: 'pixel-editor',
        icon: transparencyIcon,
      },
      {
        id: 'documentation',
        label: 'Documentation',
        route: 'documentation',
        icon: infoCircleIcon,
      },
    ],
  },
  {
    id: 'general',
    label: 'General',
    route: 'general',
    icon: moreVerticalIcon,
    alignBottom: true,
  },
];
