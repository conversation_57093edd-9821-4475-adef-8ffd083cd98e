import {
  buttonIcon,
  windowIcon,
  formElementIcon,
  commentIcon,
  tableIcon,
  positionTopIcon,
  moreHorizontalIcon,
  globeOutlineIcon,
  layoutIcon,
  textboxIcon,
  calendarIcon,
  infoCircleIcon,
  transparencyIcon,
  displayBlockIcon,
  thumbnailsDownIcon,
  minusIcon,
  freeTextIcon,
  alignRightIcon,
  dropdownIcon,
} from '@progress/kendo-svg-icons';
import {
  CoreNavigationFolder,
  CoreNavigationItem,
} from '../core/components/core-navigation/core-navigation.interfaces';

export const NAVIGATION_ITEMS: (CoreNavigationItem | CoreNavigationFolder)[] = [
  {
    id: 'primitives',
    label: 'Primitives',
    icon: displayBlockIcon,
    children: [
      {
        id: 'button',
        label: 'Button',
        route: 'buttons',
        icon: buttonIcon,
      },
      {
        id: 'dropdown-button',
        label: 'Dropdown Button',
        route: 'dropdown-buttons',
        icon: dropdownIcon,
      },
      {
        id: 'card',
        label: 'Card',
        route: 'card',
        icon: windowIcon,
      },
      {
        id: 'controls',
        label: 'Controls',
        route: 'controls',
        icon: textboxIcon,
      },
      {
        id: 'icon',
        label: 'Icon',
        route: 'icons',
        icon: freeTextIcon,
      },
      {
        id: 'progress-bar',
        label: 'Progress Bar',
        route: 'progress-bar',
        icon: minusIcon,
      },
    ],
  },
  {
    id: 'complex',
    label: 'Complex',
    icon: thumbnailsDownIcon,
    children: [
      {
        id: 'toolbar',
        label: 'Toolbar',
        route: 'toolbar',
        icon: positionTopIcon,
      },
      {
        id: 'form',
        label: 'Form',
        route: 'form',
        icon: formElementIcon,
      },
      {
        id: 'treelist',
        label: 'Treelist',
        route: 'treelist',
        icon: alignRightIcon
      },
      {
        id: 'table',
        label: 'Table',
        route: 'tables',
        icon: tableIcon,
      },
      {
        id: 'popups',
        label: 'Popups',
        route: 'popups',
        icon: commentIcon,
      },
      {
        id: 'dashboard',
        label: 'Dashboard',
        icon: layoutIcon,
        route: 'dashboard',
      },
      {
        id: 'map',
        label: 'Map',
        route: 'map',
        icon: globeOutlineIcon,
      },
      {
        id: 'scheduler',
        label: 'Scheduler',
        route: 'scheduler',
        icon: calendarIcon,
      },
      {
        id: 'pixel-editor',
        label: 'Pixel Editor',
        route: 'pixel-editor',
        icon: transparencyIcon,
      },
      {
        id: 'documentation',
        label: 'Documentation',
        route: 'documentation',
        icon: infoCircleIcon,
      },
    ],
  },
  {
    id: 'general',
    label: 'General',
    route: 'general',
    icon: moreHorizontalIcon,
    alignBottom: true,
  },
];
