<core-card class="card">
  <core-card-header>
    <h2>{{ title() }}</h2>
  </core-card-header>
  <core-card-body>
    <div class="card-content">
      <!-- Selector Section -->
      @if (selectorName()) {
        <section>
          <h2>Selector</h2>
          <code class="emphasis">{{ selectorName() }}</code>
        </section>
      }

      @if (serviceData().length) {
        <section>
          <app-api-table [title]="'Service'" [mode]="'service'" [data]="serviceData()" />
        </section>
      }

      <!-- Inputs Section -->
      @if (inputsData().length) {
        <section>
          <app-api-table [title]="'Inputs'" [mode]="'inputs'" [data]="inputsData()" />
        </section>
      }

      <!-- Events Section -->
      @if (eventsData().length) {
        <section>
          <app-api-table [title]="'Events'" [mode]="'events'" [data]="eventsData()" />
        </section>
      }
    </div>
  </core-card-body>
</core-card>
