:host {
  overflow: hidden;
}

.card {
  min-width: 350px;
  box-shadow: var(--kendo-elevation-2);
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  overflow: auto;
}

fieldset {
  border-radius: 0.25rem;
  padding: 0;
  border-color: var(--border-color);
}

.event-list {
  overflow: auto;
}

.event-row {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  word-break: break-word;
}

.event-row:last-child {
  border-bottom: none;
}

.event-index {
  min-width: 25px;
  text-align: right;
}

.event-type {
  font-weight: bold;
  min-width: 80px;
}

.event-value {
  flex: 1;
  padding-right: 4px;
}

.event-error {
  color: var(--kendo-color-error);
}

.event-warning {
  color: var(--kendo-color-warning);
}

.event-info {
  color: var(--accent-primary-color-alt);
}

.event-secondary {
  color: var(--kendo-color-secondary);
}

.event-primary {
  color: var(--kendo-color-primary);
}

.event-default {
  color: var(--text-color);
}
