/* eslint-disable @typescript-eslint/no-explicit-any */
import { Component, input } from '@angular/core';
import { CoreCardModule } from '../../../core/components/core-card';
import { EventLogItem } from './event-console.interfaces';

@Component({
  selector: 'app-event-console',
  templateUrl: './event-console.component.html',
  styleUrl: './event-console.component.scss',
  imports: [CoreCardModule],
})
export class EventConsoleComponent {
  // [ Inputs ]
  public events = input.required<EventLogItem[] | []>();
  public height = input<number>(400);

  public formatValue(val: any): string {
    if (typeof val === 'string') return val;
    return JSON.stringify(val, null, 2);
  }

  public getTypeClass(type?: string): string {
    switch (type) {
      case 'error':
        return 'event-error';
      case 'warning':
        return 'event-warning';
      case 'secondary':
        return 'event-secondary';
      case 'primary':
        return 'event-primary';
      case 'info':
        return 'event-info';
      case 'default':
        return 'event-default';
      default:
        return 'event-info';
    }
  }

  public trackByFn(index: number): number {
    return index;
  }
}
