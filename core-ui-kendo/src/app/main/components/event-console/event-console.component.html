<core-card class="card" [style.height.px]="height()">
  <core-card-header>
    <span>Event log</span>
  </core-card-header>
  <core-card-body>
    <div class="card-content">
      @if (events().length > 0) {
        <fieldset>
          <div class="event-list">
            @for (event of events(); track $index) {
              <div class="event-row">
                <span class="event-index">{{ events().length - $index }}.</span>
                <span class="event-type {{ getTypeClass(event.type) }}">[ {{ event.type ?? "info" }} ]</span>
                <span class="event-value">{{ formatValue(event.value) }}</span>
              </div>
            }
          </div>
        </fieldset>
      }
    </div>
  </core-card-body>
</core-card>
