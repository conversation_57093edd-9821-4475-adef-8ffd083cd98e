export interface CoreFilterGroup {
  logic: 'AND' | 'OR';
  filters: (CoreFilterGroup | CoreFilter)[];
}

export interface CoreFilter {
  field: string;
  operator: CoreFilterOperator;
  value: CoreFilterValue;
}

export type CoreFilterValue = string | number | boolean | Date | (string | number)[] | null;

export type CoreFilterOperator =
  | 'EQUALS'
  | 'NOT_EQUALS'
  | 'CONTAINS'
  | 'NOT_CONTAINS'
  | 'STARTS_WITH'
  | 'ENDS_WITH'
  | 'LESS_THAN'
  | 'LESS_THAN_OR_EQUAL'
  | 'GREATER_THAN'
  | 'GREATER_THAN_OR_EQUAL';
// | 'IS_NULL'
// | 'IS_NOT_NULL'

export type _CoreFilterOperator =
  | 'eq'
  | 'neq'
  | 'contains'
  | 'doesnotcontain'
  | 'startswith'
  | 'endswith'
  | 'lt'
  | 'lte'
  | 'gt'
  | 'gte';
// | 'isnull'
// | 'isnotnull'
