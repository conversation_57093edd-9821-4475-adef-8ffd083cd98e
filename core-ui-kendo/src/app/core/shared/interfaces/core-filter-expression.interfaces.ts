import { CoreSelectOption } from './core-select-option';

interface BaseFilterExpression {
  field: string;
  title?: string;
}

export interface CoreStringFilterExpression extends BaseFilterExpression {
  editor: 'string';
}

export interface CoreNumberFilterExpression extends BaseFilterExpression {
  editor: 'number';
  format?: string; // Default: 'n2'
}

export interface CoreDateFilterExpression extends BaseFilterExpression {
  editor: 'date';
  format?: string; // Default: 'MM/dd/yyyy'
}

export interface CoreDateTimeFilterExpression extends BaseFilterExpression {
  editor: 'datetime';
  format?: string; // Default: 'MM/dd/yyyy HH:mm'
}

export interface CoreBooleanFilterExpression extends BaseFilterExpression {
  editor: 'boolean';
  trueLabel?: string; // Default: 'Yes'
  falseLabel?: string; // Default: 'No'
}

export interface CoreEnumFilterExpression extends BaseFilterExpression {
  editor: 'select' | 'multiselect';
  values: CoreSelectOption<string | number>[];
}

// Discriminated union to let TypeScript know which properties are available based on the editor type
export type CoreFilterExpression =
  | CoreStringFilterExpression
  | CoreNumberFilterExpression
  | CoreDateFilterExpression
  | CoreDateTimeFilterExpression
  | CoreBooleanFilterExpression
  | CoreEnumFilterExpression;
