import { CompositeFilterDescriptor, FilterDescriptor } from '@progress/kendo-data-query';
import {
  CoreFilterGroup,
  _CoreFilterOperator,
  CoreFilter,
  CoreFilterOperator,
} from '../interfaces/core-filter.interfaces';

export class CoreKendoFilterTypeConverter {
  public static fromKendoFilterGroup(filterRoot?: CompositeFilterDescriptor): CoreFilterGroup | null {
    if (filterRoot == null || filterRoot.filters.length === 0) return null;

    // Convert the filter to the internal format
    const convertedFilters = filterRoot.filters
      .map((filter) => {
        // Check if it's a CompositeFilterDescriptor (has 'filters' property) or FilterDescriptor
        if ('filters' in filter && filter.filters) {
          // It's a CompositeFilterDescriptor - recursively convert it
          return this.fromKendoFilterGroup(filter);
        } else {
          // It's a FilterDescriptor - convert it directly to CoreFilter
          const f = filter as FilterDescriptor;
          return {
            field: f.field as string,
            operator: this._fromKendoFilterOperator(f.operator as _CoreFilterOperator),
            value: f.value as string | number | boolean | Date | null,
          } as CoreFilter;
        }
      })
      .filter((item): item is CoreFilterGroup | CoreFilter => item !== null);

    // If no valid filters after conversion, return null
    if (convertedFilters.length === 0) return null;

    const _filterRoot: CoreFilterGroup = {
      logic: this._fromKendoFilterLogic(filterRoot.logic),
      filters: convertedFilters,
    };

    return _filterRoot;
  }

  public static toKendoFilterGroup(
    filterGroup: CoreFilterGroup | CoreFilter | null,
  ): CompositeFilterDescriptor | FilterDescriptor | null {
    if (filterGroup == null) return null;

    if ('filters' in filterGroup) {
      // It's a CoreFilterGroup
      const convertedFilters = filterGroup.filters
        .map((item) => {
          if ('filters' in item) {
            // It's a nested CoreFilterGroup, recursively convert it
            return this.toKendoFilterGroup(item);
          } else {
            // It's a CoreFilter, convert to FilterDescriptor
            return {
              field: item.field,
              operator: this._toKendoFilterOperator(item.operator),
              value: item.value,
            } as FilterDescriptor;
          }
        })
        .filter((item): item is CompositeFilterDescriptor | FilterDescriptor => item !== null);

      // If no valid filters after conversion, return null
      if (convertedFilters.length === 0) return null;

      const _filterGroup: CompositeFilterDescriptor = {
        logic: this._toKendoFilterLogic(filterGroup.logic),
        filters: convertedFilters,
      };
      return _filterGroup;
    } else {
      // It's a CoreFilter
      const _filter: FilterDescriptor = {
        field: filterGroup.field,
        operator: this._toKendoFilterOperator(filterGroup.operator),
        value: filterGroup.value,
      };
      return _filter;
    }
  }

  // [ Internal methods ]

  private static _fromKendoFilterOperator(operator: _CoreFilterOperator): CoreFilterOperator {
    switch (operator) {
      case 'eq':
        return 'EQUALS';
      case 'neq':
        return 'NOT_EQUALS';
      case 'contains':
        return 'CONTAINS';
      case 'doesnotcontain':
        return 'NOT_CONTAINS';
      case 'startswith':
        return 'STARTS_WITH';
      case 'endswith':
        return 'ENDS_WITH';
      case 'lt':
        return 'LESS_THAN';
      case 'lte':
        return 'LESS_THAN_OR_EQUAL';
      case 'gt':
        return 'GREATER_THAN';
      case 'gte':
        return 'GREATER_THAN_OR_EQUAL';
      // case 'isnull':
      //   return 'IS_NULL';
      // case 'isnotnull':
      //   return 'IS_NOT_NULL';
    }
  }

  private static _toKendoFilterOperator(operator: CoreFilterOperator): _CoreFilterOperator {
    switch (operator) {
      case 'EQUALS':
        return 'eq';
      case 'NOT_EQUALS':
        return 'neq';
      case 'CONTAINS':
        return 'contains';
      case 'NOT_CONTAINS':
        return 'doesnotcontain';
      case 'STARTS_WITH':
        return 'startswith';
      case 'ENDS_WITH':
        return 'endswith';
      case 'LESS_THAN':
        return 'lt';
      case 'LESS_THAN_OR_EQUAL':
        return 'lte';
      case 'GREATER_THAN':
        return 'gt';
      case 'GREATER_THAN_OR_EQUAL':
        return 'gte';
      // case 'IS_NULL':
      //   return 'isnull';
      // case 'IS_NOT_NULL':
      //   return 'isnotnull';
    }
  }

  private static _fromKendoFilterLogic(logic: 'and' | 'or'): 'AND' | 'OR' {
    switch (logic) {
      case 'and':
        return 'AND';
      case 'or':
        return 'OR';
    }
  }

  private static _toKendoFilterLogic(logic: 'AND' | 'OR'): 'and' | 'or' {
    switch (logic) {
      case 'AND':
        return 'and';
      case 'OR':
        return 'or';
    }
  }
}
