import { CoreFilterGroup, CoreFilter, CoreFilterValue } from '../interfaces/core-filter.interfaces';
import { CoreSelectOption } from '../interfaces/core-select-option';

/**
 * Helper for processing filter values from UI components.
 *
 * Handles extraction of primitive values from complex objects like CoreSelectOption
 * and arrays thereof, making filter data suitable for API consumption.
 */
export class FilterProcessingHelper {
  /**
   * Processes filter groups to extract primitive values from complex objects.
   * Handles both single select options and multiselect arrays.
   *
   * This method ensures that filter values are properly processed from UI components
   * that may return CoreSelectOption objects or arrays of such objects.
   *
   * @param coreFilterGroup - The filter group to process
   * @returns Processed filter group with primitive values suitable for API calls
   */
  public static processFilters(coreFilterGroup: CoreFilterGroup | null): CoreFilterGroup | null {
    if (!coreFilterGroup) return null;

    return this.processFilterGroup(coreFilterGroup);
  }

  /**
   * Recursively processes a filter group, cleaning all nested filters and subgroups.
   *
   * @param filterGroup - The filter group to process
   * @returns Cleaned filter group
   */
  public static processFilterGroup(filterGroup: CoreFilterGroup): CoreFilterGroup {
    const processedFilters = filterGroup.filters
      .map((item) => {
        if (this.isFilterGroup(item)) {
          return this.processFilterGroup(item);
        }
        return this.processFilter(item);
      })
      .flat(); // Flatten in case processFilter returns a group that should be merged

    return {
      ...filterGroup,
      filters: processedFilters,
    };
  }

  /**
   * Processes an individual filter by extracting primitive values from select options.
   * Handles both single select options and multiselect arrays.
   * Converts multiselect filters (CONTAINS/NOT_CONTAINS with array values) into groups of EQUALS/NOT_EQUALS filters.
   *
   * @param filter - The filter to process
   * @returns Cleaned filter with primitive values or a filter group for multiselect
   */
  public static processFilter(filter: CoreFilter): CoreFilter | CoreFilterGroup {
    const cleanedValue = this.extractPrimitiveValue(filter.value);

    // Check if this is a multiselect filter that needs to be converted to a group
    if (this.isMultiselectFilter(filter, cleanedValue)) {
      return this.convertMultiselectToFilterGroup(filter, cleanedValue as (string | number)[]);
    }

    return {
      ...filter,
      value: cleanedValue,
    };
  }

  /**
   * Extracts primitive values from various input types:
   * - Single CoreSelectOption: extracts the value property
   * - Array of CoreSelectOptions: extracts values and always returns them as an array
   * - Primitive values: returns as-is
   *
   * For multiselect arrays, this method always returns an array (even with single values)
   * to clearly distinguish multiselect filters from single-value filters.
   *
   * @param value - The value to extract primitives from
   * @returns Primitive value or array of values suitable for API consumption
   */
  public static extractPrimitiveValue(value: unknown): CoreFilterValue {
    if (value === null || value === undefined) {
      return null;
    }

    // Handle array of select options (multiselect)
    if (Array.isArray(value) && value.length > 0) {
      const extractedValues = this.extractArrayValues(value);

      if (extractedValues.length === 0) {
        return null;
      }

      // Always return array for multiselect values to clearly indicate multiselect semantics
      return extractedValues;
    }

    // Handle single select option
    if (this.isSelectOption(value)) {
      return value.value as CoreFilterValue;
    }

    // Handle primitive values
    return this.convertToPrimitive(value);
  }

  /**
   * Extracts primitive values from an array, handling CoreSelectOption objects.
   *
   * @param array - Array of values to extract from
   * @returns Array of primitive values (only string and number for arrays)
   */
  private static extractArrayValues(array: unknown[]): (string | number)[] {
    return array
      .map((item) => {
        if (this.isSelectOption(item)) {
          return item.value;
        }
        const primitive = this.convertToPrimitive(item);
        return typeof primitive === 'string' || typeof primitive === 'number' ? primitive : null;
      })
      .filter((val): val is string | number => val !== null);
  }

  /**
   * Converts unknown value to CoreFilterValue type safely.
   *
   * @param value - The value to convert
   * @returns Converted primitive value or null if not convertible
   */
  private static convertToPrimitive(value: unknown): CoreFilterValue {
    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean' || value instanceof Date) {
      return value;
    }
    return null;
  }

  /**
   * Type guard to check if an object is a CoreSelectOption.
   *
   * @param obj - Object to check
   * @returns True if object is a CoreSelectOption
   */
  private static isSelectOption(obj: unknown): obj is CoreSelectOption<string | number> {
    if (typeof obj !== 'object' || obj === null) {
      return false;
    }

    const record = obj as Record<string, unknown>;
    return 'value' in record && 'label' in record && typeof record['label'] === 'string';
  }

  /**
   * Type guard to check if an item is a filter group.
   *
   * @param item - Item to check
   * @returns True if item is a filter group
   */
  private static isFilterGroup(item: CoreFilterGroup | CoreFilter): item is CoreFilterGroup {
    return 'logic' in item && 'filters' in item;
  }

  /**
   * Checks if a filter is a multiselect filter that needs to be converted to a group.
   * A multiselect filter has CONTAINS/NOT_CONTAINS operator and an array value.
   *
   * @param filter - The filter to check
   * @param value - The processed value
   * @returns True if this is a multiselect filter
   */
  private static isMultiselectFilter(filter: CoreFilter, value: CoreFilterValue): boolean {
    return (
      (filter.operator === 'CONTAINS' || filter.operator === 'NOT_CONTAINS') && Array.isArray(value) && value.length > 0
    );
  }

  /**
   * Converts a multiselect filter into a filter group with EQUALS/NOT_EQUALS filters.
   *
   * @param filter - The original multiselect filter
   * @param values - Array of values to create individual filters for
   * @returns Filter group with OR logic containing individual filters
   */
  private static convertMultiselectToFilterGroup(filter: CoreFilter, values: (string | number)[]): CoreFilterGroup {
    const operator = filter.operator === 'CONTAINS' ? 'EQUALS' : 'NOT_EQUALS';
    const logic = filter.operator === 'CONTAINS' ? 'OR' : 'AND';

    const individualFilters: CoreFilter[] = values.map((value) => ({
      field: filter.field,
      operator,
      value,
    }));

    return {
      logic,
      filters: individualFilters,
    };
  }
}
