<kendo-toolbar [overflow]="toolbar()._options().overflow" [size]="'medium'" [style]="toolbar()._options().style">
  @for (item of toolbar()._options().items; track index; let index = $index) {
    @if (item.visible) {
      @switch (item.kind) {
        @case ("button") {
          <kendo-toolbar-button
            [text]="$asButton(item)._options().label!"
            [showText]="$asButton(item)._options().showLabel"
            [svgIcon]="$asButton(item)._options().icon!"
            [fillMode]="$asButton(item)._options().type"
            [themeColor]="$asButton(item)._options().color"
            [disabled]="$asButton(item)._options().disabled"
            (click)="$asButton(item)._options().click()"
          />
        }
        @case ("dropdown-button") {
          <kendo-toolbar-dropdownbutton
            [text]="$asDropDownButton(item)._options().label"
            [showText]="$asButton(item)._options().showLabel"
            [svgIcon]="$asDropDownButton(item)._options().icon!"
            [disabled]="$asDropDownButton(item)._options().disabled"
            [fillMode]="$asDropDownButton(item)._options().type"
            [themeColor]="$asDropDownButton(item)._options().color"
            [data]="$asDropDownButton(item)._options().options"
          />
        }
        @case ("split-button") {
          <kendo-toolbar-splitbutton
            [text]="$asDropDownButton(item)._options().label"
            [showText]="$asButton(item)._options().showLabel"
            [svgIcon]="$asDropDownButton(item)._options().icon!"
            [data]="$asDropDownButton(item)._options().options"
            [disabled]="$asDropDownButton(item)._options().disabled"
            [fillMode]="$asDropDownButton(item)._options().type"
            [themeColor]="$asDropDownButton(item)._options().color"
            (buttonClick)="$asButton(item)._options().click()"
          />
        }
        @case ("custom") {
          <core-toolbar-custom [item]="$asCustom(item)" />
        }
        @case ("separator") {
          <kendo-toolbar-separator />
        }
        @case ("spacer") {
          <kendo-toolbar-spacer />
        }
      }
    }
  }
</kendo-toolbar>
