import { TemplateRef } from '@angular/core';

export interface CoreToolbarCustomOptions {
  id?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  templateRef: TemplateRef<any>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  popupTemplateRef?: TemplateRef<any>;

  visible?: boolean; // Default: true
}

export interface _CoreToolbarCustomOptions {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  templateRef: TemplateRef<any>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  popupTemplateRef: TemplateRef<any> | null;
}
