import { CoreButtonColor, CoreButtonType } from '../../core-button';
import { _CoreButtonColor, _CoreButtonType } from '../../core-button/core-button.types';
import { CoreIcon } from '../../core-icon';

export interface CoreToolbarDropDownButtonOptions {
  id?: string;
  label: string;
  icon?: CoreIcon;
  disabled?: boolean;
  type?: CoreButtonType; // Default: 'default'
  color?: CoreButtonColor; // Default: 'default'
  visible?: boolean; // Default: true
  options: CoreToolbarDropDownButtonItemOptions[];
}

export interface CoreToolbarDropDownButtonItemOptions {
  label: string;
  icon?: CoreIcon;
  click: () => void | Promise<void>;
}

export interface _CoreToolbarDropDownButtonOptions {
  label: string;
  icon: CoreIcon | null;
  disabled: boolean;
  type: _CoreButtonType;
  color: _CoreButtonColor;
  options: _CoreToolbarDropDownButtonItemOptions[];
}

export interface _CoreToolbarDropDownButtonItemOptions {
  text: string;
  svgIcon: CoreIcon | null;
  click: () => void | Promise<void>;
}
