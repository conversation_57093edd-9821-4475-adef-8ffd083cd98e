import { CoreButtonColor, CoreButtonType } from '../../core-button';
import { _CoreButtonColor, _CoreButtonType } from '../../core-button/core-button.types';
import { CoreIcon } from '../../core-icon';

export interface CoreToolbarSplitButtonOptions {
  id?: string;
  label: string;
  icon?: CoreIcon;
  disabled?: boolean;
  type?: CoreButtonType; // Default: 'default'
  color?: CoreButtonColor; // Default: 'default'
  visible?: boolean; // Default: true
  options: CoreToolbarSplitButtonItemOptions[];
  click: () => void | Promise<void>;
}

export interface CoreToolbarSplitButtonItemOptions {
  label: string;
  icon?: CoreIcon;
  click: () => void | Promise<void>;
}

export interface _CoreToolbarSplitButtonOptions {
  label: string;
  icon: CoreIcon | null;
  disabled: boolean;
  type: _CoreButtonType;
  color: _CoreButtonColor;
  options: _CoreToolbarSplitButtonItemOptions[];
  click: () => void | Promise<void>;
}

export interface _CoreToolbarSplitButtonItemOptions {
  text: string;
  svgIcon: CoreIcon | null;
  click: () => void | Promise<void>;
}
