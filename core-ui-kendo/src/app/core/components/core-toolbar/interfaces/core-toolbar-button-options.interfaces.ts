// Icons
import { CoreIcon } from '../../core-icon';
// Types
import { CoreButtonColor, CoreButtonType } from '../../core-button';
import { _CoreButtonColor, _CoreButtonType } from '../../core-button/core-button.types';
import { _CoreToolbarItemShowLabel, CoreToolbarItemShowLabel } from '../types/core-toolbar-item-show-label.type';

export interface CoreToolbarButtonOptions {
  id?: string;
  label?: string;
  showLabel?: CoreToolbarItemShowLabel; // Default: 'always'
  type?: CoreButtonType; // Default: 'default'
  color?: CoreButtonColor; // Default: 'default'
  icon?: CoreIcon;
  disabled?: boolean; // Default: false
  visible?: boolean; // Default: true
  click?: () => void | Promise<void>;
}

export interface _CoreToolbarButtonOptions {
  label: string | null;
  showLabel: _CoreToolbarItemShowLabel;
  type: _CoreButtonType;
  color: _CoreButtonColor;
  icon: CoreIcon | null;
  disabled: boolean;
  click: () => void | Promise<void>;
}
