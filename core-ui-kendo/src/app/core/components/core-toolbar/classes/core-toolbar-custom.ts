// Angular
import { signal, WritableSignal } from '@angular/core';
// Classes
import { CoreToolbarItem } from './core-toolbar-item';
// Interfaces
import {
  _CoreToolbarCustomOptions,
  CoreToolbarCustomOptions,
} from '../interfaces/core-toolbar-custom-options.interfaces';

export class CoreToolbarCustom extends CoreToolbarItem {
  public _options: WritableSignal<_CoreToolbarCustomOptions>;

  constructor(options: CoreToolbarCustomOptions) {
    super('custom', options.id, options.visible ?? true);
    this._options = signal({
      templateRef: options.templateRef,
      popupTemplateRef: options.popupTemplateRef ?? null,
    });
  }
}
