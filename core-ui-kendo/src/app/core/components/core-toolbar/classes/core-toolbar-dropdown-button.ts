// Angular
import { signal, WritableSignal } from '@angular/core';
// Classes
import { CoreToolbarItem } from './core-toolbar-item';
// Interfaces
import {
  _CoreToolbarDropDownButtonOptions,
  CoreToolbarDropDownButtonOptions,
} from '../interfaces/core-toolbar-dropdown-button-options.interfaces';
// Helpers
import { KendoTypeConverter } from '../../../shared/helpers/kendo-type-converter';

export class CoreToolbarDropDownButton extends CoreToolbarItem {
  public _options: WritableSignal<_CoreToolbarDropDownButtonOptions>;

  constructor(options: CoreToolbarDropDownButtonOptions) {
    super('dropdown-button', options.id, options.visible);
    this._options = signal({
      label: options.label,
      icon: options.icon ?? null,
      disabled: options.disabled ?? false,
      type: KendoTypeConverter.getKendoButtonType(options.type ?? 'default'),
      color: KendoTypeConverter.getKendoButtonColor(options.color ?? 'default'),
      options: options.options.map((o) => ({
        text: o.label,
        svgIcon: o.icon ?? null,
        click: o.click,
      })),
    });
  }
}
