// Angular
import { signal, WritableSignal } from '@angular/core';
// Classes
import { CoreToolbarItem } from './core-toolbar-item';
// Interfaces
import {
  _CoreToolbarButtonOptions,
  CoreToolbarButtonOptions,
} from '../interfaces/core-toolbar-button-options.interfaces';
// Helpers
import { KendoTypeConverter } from '../../../shared/helpers/kendo-type-converter';

export class CoreToolbarButton extends CoreToolbarItem {
  public _options: WritableSignal<_CoreToolbarButtonOptions>;

  constructor(options: CoreToolbarButtonOptions) {
    super('button', options.id, options.visible);
    this._options = signal({
      id: options.id ?? null,
      label: options.label ?? null,
      showLabel: KendoTypeConverter.getKendoToolbarShowLabel(options.showLabel ?? 'always'),
      type: KendoTypeConverter.getKendoButtonType(options.type ?? 'default'),
      color: KendoTypeConverter.getKendoButtonColor(options.color ?? 'default'),
      icon: options.icon ?? null,
      disabled: options.disabled ?? false,
      visible: options.visible ?? true,
      click: options.click ?? (() => void 0),
    });
  }
}
