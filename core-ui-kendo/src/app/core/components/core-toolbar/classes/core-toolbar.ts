// Angular
import { signal, WritableSignal } from '@angular/core';
// Classes
import { CoreToolbarItem } from './core-toolbar-item';
import { CoreToolbarButton } from './core-toolbar-button';
import { CoreToolbarDropDownButton } from './core-toolbar-dropdown-button';
import { CoreToolbarSplitButton } from './core-toolbar-split-button';
import { CoreToolbarCustomOptions } from '../interfaces/core-toolbar-custom-options.interfaces';
// Interfaces
import { _CoreToolbarOptions, CoreToolbarOptions } from '../interfaces/core-toolbar-options.interface';
import { CoreToolbarButtonOptions } from '../interfaces/core-toolbar-button-options.interfaces';
import { CoreToolbarDropDownButtonOptions } from '../interfaces/core-toolbar-dropdown-button-options.interfaces';
import { CoreToolbarSplitButtonOptions } from '../interfaces/core-toolbar-split-button-options.interfaces';
import { CoreToolbarCustom } from './core-toolbar-custom';

export class CoreToolbar {
  public _options: WritableSignal<_CoreToolbarOptions>;

  constructor(options: CoreToolbarOptions) {
    this._options = signal<_CoreToolbarOptions>({
      items: options.items,
      style: options.style ?? {},
      overflow: options.overflow ?? false,
    });
  }

  // [ Public events ]

  public setItems(items: CoreToolbarItem[]): void {
    this._options.set({ items, overflow: this._options().overflow });
  }

  public addItems(items: CoreToolbarItem[], index: number | null = null): void {
    this._options.update((state) => {
      const newItems = [...state.items];
      if (index !== null && index >= 0 && index < newItems.length) {
        newItems.splice(index, 0, ...items);
      } else {
        newItems.push(...items);
      }
      return { ...state, items: newItems };
    });
  }

  /**
   * Removes toolbar items by index or ID.
   *
   * @param itemsToRemove - Array of indexes (numbers) or IDs (strings) of items to remove
   */
  public removeItems(itemsToRemove: (number | string)[]): void {
    this._options.update((state) => ({
      ...state,
      items: state.items.filter((item, index) => {
        return !itemsToRemove.some((itemToRemove) => {
          if (typeof itemToRemove === 'number')
            return index === itemToRemove; // itemToRemove is a number -> compare with item index
          else return item.id === itemToRemove; // itemToRemove is a string -> compare with item ID
        });
      }),
    }));
  }

  /**
   * Retrieves a toolbar item by index or ID.
   *
   * @param item - The index (number) or ID (string) of the item to retrieve
   * @returns The toolbar item if found, null otherwise
   */
  public getItem<T extends CoreToolbarItem = CoreToolbarItem>(item: number | string): T | null {
    if (typeof item === 'number') {
      return (this._options().items[item] as T) ?? null;
    } else {
      return (this._options().items.find((i) => i.id === item) as T) ?? null;
    }
  }

  /**
   * Sets the visibility of a toolbar item.
   *
   * @param visible - Whether the item should be visible
   * @param item - The index (number) or ID (string) of the item to update
   */
  public setItemVisibility(visible: boolean, item: number | string): void {
    const itemToUpdate = typeof item === 'number' ? this.getItem(item) : this.getItem(item);
    if (itemToUpdate) {
      itemToUpdate.visible = visible;
    }
  }

  /**
   * Sets the visibility of multiple toolbar items.
   *
   * @param visible - Whether the items should be visible
   * @param items - Array of indexes (numbers) or IDs (strings) of items to update
   */
  public setItemsVisibility(visible: boolean, items: (number | string)[]): void {
    items.forEach((item) => {
      this.setItemVisibility(visible, item);
    });
  }

  // [ Extension methods ]
  public static Button = (options: CoreToolbarButtonOptions): CoreToolbarButton => new CoreToolbarButton(options);
  public static DropDownButton = (options: CoreToolbarDropDownButtonOptions): CoreToolbarDropDownButton =>
    new CoreToolbarDropDownButton(options);
  public static SplitButton = (options: CoreToolbarSplitButtonOptions): CoreToolbarSplitButton =>
    new CoreToolbarSplitButton(options);
  public static Custom = (options: CoreToolbarCustomOptions): CoreToolbarCustom => new CoreToolbarCustom(options);
  public static Separator = (): CoreToolbarItem => new CoreToolbarItem('separator');
  public static Spacer = (): CoreToolbarItem => new CoreToolbarItem('spacer');
}
