// Angular
import { Component, computed, input } from '@angular/core';
// Kendo UI
import { KENDO_PROGRESSBARS, LabelSettings } from '@progress/kendo-angular-progressbar';

@Component({
  selector: 'core-progress-bar',
  imports: [KENDO_PROGRESSBARS],
  templateUrl: './core-progress-bar.html',
  styleUrl: './core-progress-bar.scss',
})
export class CoreProgressBar {
  // [ Public API ]

  public value = input.required<number>();
  public min = input<number>(0);
  public max = input<number>(100);
  public labelVisible = input<boolean>(true);
  public labelFormat = input<'value' | 'percent' | ((value: number) => string)>('value');
  public labelPosition = input<'start' | 'end' | 'center'>('center');
  public indeterminate = input<boolean>(false);

  // [ Internal ]

  public _labelSettings = computed<LabelSettings>(() => this._getLabelSettings());

  private _getLabelSettings(): LabelSettings {
    return {
      visible: this.labelVisible(),
      format: this.labelFormat(),
      position: this.labelPosition(),
    };
  }
}
