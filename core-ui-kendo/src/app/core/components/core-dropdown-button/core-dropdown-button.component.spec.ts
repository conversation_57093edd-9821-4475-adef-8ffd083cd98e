// Angular
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { provideNoopAnimations } from '@angular/platform-browser/animations';
// Kendo UI
import { menuIcon, userIcon } from '@progress/kendo-svg-icons';
import { POPUP_CONTAINER } from '@progress/kendo-angular-popup';
// Core UI
import { CoreDropDownButtonComponent } from './core-dropdown-button.component';
import { CoreDropDownButtonModule } from './core-dropdown-button.module';
import { CoreIcon } from '../core-icon/core-icon.interfaces';
import { CoreDropDownButtonOption } from './core-dropdown-button.interfaces';
// Constants
const OPTIONS: CoreDropDownButtonOption[] = [
  {
    label: 'Create User',
    icon: userIcon,
    click: () => {
      return;
    },
  },
];

// [ Tests ]

describe('Core Dropdown Button Component', () => {
  let component: CoreDropDownButtonComponent;
  let fixture: ComponentFixture<CoreDropDownButtonComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CoreDropDownButtonModule],
      providers: [
        provideNoopAnimations(),
        {
          provide: POPUP_CONTAINER,
          useFactory: () => ({ nativeElement: document.body }),
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CoreDropDownButtonComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });

  it('should display the provided label', () => {
    fixture.componentRef.setInput('label', 'Save Changes');
    fixture.detectChanges();
    const dropdownButtonDebugEl = fixture.debugElement.query(By.css('.k-menu-button'));
    const buttonEl: HTMLButtonElement = dropdownButtonDebugEl.nativeElement as HTMLButtonElement;
    expect(buttonEl.textContent?.trim()).toBe('Save Changes');
  });

  it('should be disabled when the disabled input is true', () => {
    fixture.componentRef.setInput('disabled', true);
    fixture.detectChanges();
    const dropdownButtonDebugEl = fixture.debugElement.query(By.css('.k-menu-button'));
    const buttonEl: HTMLButtonElement = dropdownButtonDebugEl.nativeElement as HTMLButtonElement;
    expect(buttonEl.disabled).toBe(true);
  });

  it('should be disabled and show loader when busy is true', () => {
    fixture.componentRef.setInput('busy', true);
    fixture.detectChanges();
    const dropdownButtonDebugEl = fixture.debugElement.query(By.css('.k-menu-button'));
    const loaderElement = fixture.debugElement.query(By.css('kendo-loader'));
    const buttonEl: HTMLButtonElement = dropdownButtonDebugEl.nativeElement as HTMLButtonElement;
    expect(buttonEl.disabled).toBe(true);
    expect(loaderElement).toBeTruthy();
  });

  it('should render an SVG icon when a CoreIcon object is provided', () => {
    const mockIcon: CoreIcon = menuIcon;
    fixture.componentRef.setInput('icon', mockIcon);
    fixture.detectChanges();
    const dropdownButtonDebugEl = fixture.debugElement.query(By.css('.k-menu-button'));
    const svgElement = dropdownButtonDebugEl.query(By.css('kendo-svgicon'));
    expect(svgElement).toBeTruthy();
  });

  describe('Kendo class bindings', () => {
    it('should apply solid class for "default" type and "primary" color', () => {
      fixture.componentRef.setInput('type', 'default');
      fixture.componentRef.setInput('color', 'primary');
      fixture.detectChanges();
      const dropdownButtonDebugEl = fixture.debugElement.query(By.css('.k-menu-button'));
      expect(dropdownButtonDebugEl.classes['k-button-solid-primary']).toBe(true);
    });

    it('should apply outline class for "outline" type and "error" color', () => {
      fixture.componentRef.setInput('type', 'outline');
      fixture.componentRef.setInput('color', 'error');
      fixture.detectChanges();
      const dropdownButtonDebugEl = fixture.debugElement.query(By.css('.k-menu-button'));
      expect(dropdownButtonDebugEl.classes['k-button-outline-error']).toBe(true);
    });

    it('should apply flat class for "flat" type and "secondary" color', () => {
      fixture.componentRef.setInput('type', 'flat');
      fixture.componentRef.setInput('color', 'secondary');
      fixture.detectChanges();
      const dropdownButtonDebugEl = fixture.debugElement.query(By.css('.k-menu-button'));
      expect(dropdownButtonDebugEl.classes['k-button-flat-secondary']).toBe(true);
    });
  });

  describe('Dropdown Items', () => {
    it('should show dropdown items that match the options input', () => {
      fixture.componentRef.setInput('label', 'Test');
      fixture.componentRef.setInput('options', OPTIONS);
      fixture.detectChanges();

      const dropdownButtonDebugEl = fixture.debugElement.query(By.css('.k-menu-button'));
      dropdownButtonDebugEl.triggerEventHandler('click', null);
      fixture.detectChanges();

      const listItems = document.querySelectorAll('.k-menu-item');
      expect(listItems.length).toBe(OPTIONS.length);
      OPTIONS.forEach((option, index) => {
        const currentItem = listItems[index];
        const labelElement = currentItem.querySelector('.k-menu-link-text');
        expect(labelElement?.textContent?.trim()).toBe(option.label);
        const iconElement = currentItem.querySelector('kendo-svgicon');
        expect(iconElement).toBeTruthy();
      });
    });

    it('should call the click handler when an item is clicked', () => {
      const clickSpy = jasmine.createSpy('click handler');
      const testOptions: CoreDropDownButtonOption[] = [
        {
          label: 'Create User',
          icon: userIcon,
          click: clickSpy,
        },
      ];
      fixture.componentRef.setInput('options', testOptions);
      fixture.detectChanges();

      const dropdownButtonDebugEl = fixture.debugElement.query(By.css('.k-menu-button'));
      dropdownButtonDebugEl.triggerEventHandler('click', null);
      fixture.detectChanges();

      const firstItem = document.querySelector('.k-menu-item');
      expect(firstItem).toBeTruthy();

      (firstItem as HTMLElement).click();
      fixture.detectChanges();

      expect(clickSpy).toHaveBeenCalled();
      expect(clickSpy).toHaveBeenCalledTimes(1);
    });
  });

  describe('Accessibility & Focus Management', () => {
    it('should be focusable', () => {
      fixture.detectChanges();
      const buttonEl = fixture.debugElement.query(By.css('.k-menu-button')).nativeElement as HTMLElement;
      buttonEl.focus();
      fixture.detectChanges();
      expect(document.activeElement).toBe(buttonEl);
    });

    it('should make dropdown items focusable when open', () => {
      fixture.componentRef.setInput('options', OPTIONS);
      fixture.detectChanges();
      const buttonEl = fixture.debugElement.query(By.css('.k-menu-button')).nativeElement as HTMLElement;
      buttonEl.click();
      fixture.detectChanges();

      const firstItem = document.querySelector('.k-menu-item');
      expect(firstItem).toBeTruthy();
      (firstItem as HTMLElement).focus();
      fixture.detectChanges();
      expect(document.activeElement).toBe(firstItem);
    });

    it('should apply the aria-label attribute', () => {
      const testAriaLabel = 'Close navigation menu';
      fixture.componentRef.setInput('ariaLabel', testAriaLabel);
      fixture.detectChanges();
      const dropdownButtonDebugEl = fixture.debugElement.query(By.css('.k-menu-button'));
      const buttonEl: HTMLButtonElement = dropdownButtonDebugEl.nativeElement as HTMLButtonElement;
      expect(buttonEl.getAttribute('aria-label')).toBe(testAriaLabel);
    });

    it('should close the popup when blurred', async () => {
      fixture.componentRef.setInput('options', OPTIONS);
      fixture.detectChanges();

      const kendoComponent = component.dropdownButtonComponent()!;
      kendoComponent.openPopup();
      fixture.detectChanges();
      await fixture.whenStable();
      expect(document.querySelector('.k-popup')).toBeTruthy();

      kendoComponent.blur();
      fixture.detectChanges();
      await fixture.whenStable();
      expect(document.querySelector('.k-popup')).toBeFalsy();
    });
  });
});
