<kendo-listview [data]="items()">
  <!-- Checkbox (Select All) -->
  @if (checkbox() === "multiple") {
    <ng-template kendoListViewHeaderTemplate>
      <div class="listview-container">
        <kendo-checkbox #selectAll [checkedState]="checkedState()" (change)="_checkAll($event)" />
        <kendo-label [for]="selectAll" text="[ Select All ]" />
      </div>
    </ng-template>
  }
  <!-- Item -->
  <ng-template kendoListViewItemTemplate let-dataItem>
    <div class="listview-container">
      <!-- Checkbox -->
      @if (checkbox() !== "none") {
        <kendo-checkbox [checkedState]="_isChecked(dataItem)" (change)="_handleChecking(dataItem)" />
      }
      <!-- Custom Template -->
      @if (_templateRef()?.templateRef) {
        <ng-container
          [ngTemplateOutlet]="_templateRef()?.templateRef"
          [ngTemplateOutletContext]="{ $implicit: dataItem }"
        ></ng-container>
      } @else {
        <!-- Default Template -->
        @if (dataItem.icon) {
          <core-icon [icon]="dataItem.icon" />
        }
        <div class="fields-container">
          @for (field of dataItem.fields; track field.labelProperty) {
            <div class="field-row" [ngClass]="align()">
              <strong>{{ field.labelText }}:</strong>
              <span>{{ field.labelProperty }}</span>
            </div>
          }
        </div>
      }
    </div>
  </ng-template>
</kendo-listview>
