// Angular
import { Component, computed, contentChild, input, output, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
// Kendo UI
import { ListViewModule } from '@progress/kendo-angular-listview';
import { CheckBoxModule } from '@progress/kendo-angular-inputs';
import { LabelModule } from '@progress/kendo-angular-label';
// Interfaces
import { CoreListViewItem } from './core-list-view.interfaces';
// Core UI
import { CoreIconModule } from '../core-icon';
// Directives
import { CoreListViewTemplateDirective } from './directives/core-list-view-template.directive';

@Component({
  selector: 'core-list-view',
  templateUrl: './core-list-view.component.html',
  styleUrl: './core-list-view.component.scss',
  imports: [ListViewModule, CoreIconModule, CheckBoxModule, LabelModule, CommonModule],
})
export class CoreListViewComponent {
  // [ Public API ]
  public items = input.required<CoreListViewItem[]>();
  public checkbox = input<'none' | 'single' | 'multiple'>('single');
  public align = input<'vertical' | 'horizontal'>('vertical');
  // Outputs
  public checkedChange = output<(string | number)[]>();

  // [ Private ]
  private _checkedItemIds = signal<(string | number)[]>([]);

  // Content children
  public _templateRef = contentChild(CoreListViewTemplateDirective);
  // Computed
  public checkedState = computed(() => {
    const selected = this._checkedItemIds().length;
    const total = this.items().length;
    if (selected === total && total > 0) return true;
    if (selected > 0) return 'indeterminate';
    return false;
  });

  public _isChecked(item: CoreListViewItem): boolean {
    return this._checkedItemIds().includes(item.id);
  }

  // [ Events ]

  public _handleChecking(item: CoreListViewItem): void {
    let ids = [...this._checkedItemIds()];
    if (ids.includes(item.id)) {
      ids = ids.filter((id) => id !== item.id);
    } else {
      if (this.checkbox() === 'single') {
        ids = [item.id];
      } else {
        ids.push(item.id);
      }
    }
    this._checkedItemIds.set(ids);
    // Emit the change
    this.checkedChange.emit(this._checkedItemIds());
  }

  public _checkAll(event: Event): void {
    const checked = (event.target as HTMLInputElement).checked;
    if (checked) {
      const allIds = this.items().map((item) => item.id);
      this._checkedItemIds.set(allIds);
    } else {
      this._checkedItemIds.set([]);
    }
    // Emit the change
    this.checkedChange.emit(this._checkedItemIds());
  }
}
