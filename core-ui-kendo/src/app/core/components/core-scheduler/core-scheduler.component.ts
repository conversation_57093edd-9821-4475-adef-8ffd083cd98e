// Angular
import { Component, computed, effect, inject, input, OnInit, output, Signal, signal, viewChild } from '@angular/core';
// Interfaces
import { CoreSchedulerDurationValue, CoreSchedulerRepeatValue, CoreSchedulerValue } from './core-scheduler.interfaces';
// Types
import { CoreSchedulerValidityStatus } from './core-scheduler.types';
// Components
import { SchedulerStartSectionComponent } from './components/scheduler-start-section/scheduler-start-section.component';
import { SchedulerRepeatSectionComponent } from './components/scheduler-repeat-section/scheduler-repeat-section.component';
import { SchedulerDurationSectionComponent } from './components/scheduler-duration-section/scheduler-duration-section.component';
import { SchedulerEndSectionComponent } from './components/scheduler-end-section/scheduler-end-section.component';
import { CoreSchedulerRepeatMode } from './core-scheduler.enums';
// Services
import { CoreSchedulerStateService } from './core-scheduler.state.service';

@Component({
  selector: 'core-scheduler',
  templateUrl: './core-scheduler.component.html',
  styleUrl: './core-scheduler.component.scss',
  providers: [CoreSchedulerStateService],
  imports: [
    SchedulerStartSectionComponent,
    SchedulerRepeatSectionComponent,
    SchedulerDurationSectionComponent,
    SchedulerEndSectionComponent,
  ],
})
export class CoreSchedulerComponent implements OnInit {
  // Public

  public value = input<CoreSchedulerValue>();
  public timeMode = input<'12hrs' | '24hrs'>('12hrs');
  public showSummary = input<boolean>(true);
  public valueChanges = output<void>();
  public status: Signal<CoreSchedulerValidityStatus>;

  public async getValue(): Promise<CoreSchedulerValue> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          startDateTime: this._getStartDateTimeValue(),
          repeat: this._getRepeatValue(),
          duration: this._getDurationValue(),
          endDate: this._getEndDateValue(),
        });
      });
    });
  }

  // Internal
  public _startSummary = signal('');
  public _repeatSummary = signal('');
  public _durationSummary = signal('');
  public _endSummary = signal('');

  private _state = inject(CoreSchedulerStateService);
  private _startSectioRef = viewChild(SchedulerStartSectionComponent);
  private _repeatSectionRef = viewChild(SchedulerRepeatSectionComponent);
  private _durationSectionRef = viewChild(SchedulerDurationSectionComponent);
  private _endSectionRef = viewChild(SchedulerEndSectionComponent);

  constructor() {
    this.status = computed(() => {
      if (
        this._state.startSectionStatus() === 'VALID' &&
        this._state.repeatSectionStatus() === 'VALID' &&
        this._state.durationSectionStatus() === 'VALID' &&
        this._state.endSectionStatus() === 'VALID'
      ) {
        return 'VALID';
      } else {
        return 'INVALID';
      }
    });

    // On repeat mode changes, check if mode is ONCE and set duration mode options accordingly
    effect(() => {
      switch (this._state.repeatSectionMode()) {
        case CoreSchedulerRepeatMode.ONCE:
          this._durationSectionRef()!.showInfiniteOption();
          break;
        default:
          this._durationSectionRef()!.hideInfiniteOption();
      }
    });
  }

  public ngOnInit(): void {
    this._updateSummary();
  }

  // Events

  public _valueChanges(): void {
    this._updateSummary();
    this.valueChanges.emit();
  }

  // Private

  private _getStartDateTimeValue(): Date {
    return this._startSectioRef()!.getValue();
  }

  private _getRepeatValue(): CoreSchedulerRepeatValue {
    return this._repeatSectionRef()!.getValue();
  }

  private _getDurationValue(): CoreSchedulerDurationValue {
    return this._durationSectionRef()!.getValue();
  }

  private _getEndDateValue(): Date | null {
    return this._endSectionRef()!.getValue();
  }

  private _updateSummary(): void {
    setTimeout(() => {
      this._startSummary.set(this._startSectioRef()!.getDescription() || '[error]');
      this._repeatSummary.set(this._repeatSectionRef()!.getDescription() || '[error]');
      this._durationSummary.set(this._durationSectionRef()!.getDescription() || '[error]');
      this._endSummary.set(this._endSectionRef()!.getDescription() || '[error]');
    });
  }
}
