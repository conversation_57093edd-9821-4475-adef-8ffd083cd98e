// Angular
import { Component, effect, inject, input, OnDestroy, OnInit, output, signal } from '@angular/core';
import { Subscription } from 'rxjs';
// Core UI
import {
  CoreInputDate,
  CoreInputDateComponent,
  CoreInputRadioSelect,
  CoreInputRadioSelectComponent,
  CoreInputValidators,
} from '../../../core-inputs';
import { CoreSchedulerValidityStatus } from '../../core-scheduler.types';
// Services
import { CoreSchedulerStateService } from '../../core-scheduler.state.service';
import { DateTime } from 'luxon';
// Types
type Mode = 'date' | 'never';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'scheduler-end-section',
  templateUrl: './scheduler-end-section.component.html',
  imports: [CoreInputRadioSelectComponent, CoreInputDateComponent],
})
export class SchedulerEndSectionComponent implements OnInit, OnDestroy {
  // Public

  public value = input<Date | null>(null);
  public min = input<Date | null>();
  public timeFormat = input<'hh:mm a' | 'HH:mm'>('hh:mm a');
  public valueChanges = output<void>();

  public getValue(): Date | null {
    return this._getValue();
  }

  public getDescription(): string {
    if (this._modeControl?.value) {
      return this._getDescription();
    } else {
      return 'Ends on: Description not available.';
    }
  }

  // Internal
  public _modeControl: CoreInputRadioSelect | null = null;
  public _dateControl: CoreInputDate | null = null;
  public _mode = signal<Mode>('never');

  private _subscriptions$ = new Subscription();
  private _schedulerState = inject(CoreSchedulerStateService);
  private _status = signal<CoreSchedulerValidityStatus>('INVALID');

  constructor() {
    // On status changes, update respective status in scheduler state service
    effect(() => {
      this._schedulerState.endSectionStatus.set(this._status());
    });
  }

  public ngOnInit(): void {
    // Declare variables that depend on Input availability
    this._modeControl = new CoreInputRadioSelect({
      label: 'Mode',
      options: [
        { value: 'never', label: 'Never' },
        { value: 'date', label: 'Date' },
      ],
      value: this.value() ? 'date' : 'never',
    });
    this._mode.set(this._modeControl.value as Mode);
    this._dateControl = new CoreInputDate({
      label: 'Date',
      value: this.value() ?? new Date(),
      min: this.min() ?? new Date(),
      validators: [CoreInputValidators.required('Required field')],
    });
    // Set intial status
    this._updateStatus();
    // Update mode and status on modeControl value changes
    this._subscriptions$.add(
      this._modeControl.valueChanges.subscribe((mode) => {
        this._mode.set(mode as Mode);
        this._updateStatus();
      }),
    );
    // Update status on any value changes
    this._subscriptions$.add(
      this._dateControl.valueChanges.subscribe(() => {
        this._updateStatus();
        this.valueChanges.emit();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  // Private

  private _updateStatus(): void {
    switch (this._mode()) {
      case 'never':
        this._status.set('VALID');
        break;
      case 'date':
        this._status.set(this._dateControl!.valid ? 'VALID' : 'INVALID');
        break;
    }
  }

  private _getValue(): Date | null {
    switch (this._mode()) {
      case 'never':
        return null;
      case 'date':
        return this._dateControl!.value as Date;
    }
  }

  private _getDescription(): string {
    if (this._modeControl!.value === 'date') {
      const readableDate = DateTime.fromJSDate(this._dateControl!.value as Date).toFormat('MM/dd/yyyy');
      return `Ends on ${readableDate}`;
    } else {
      return 'Never ends.';
    }
  }
}
