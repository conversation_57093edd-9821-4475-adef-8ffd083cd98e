// Angular
import { Component, inject, input, OnD<PERSON>roy, OnInit, output } from '@angular/core';
import { Subscription } from 'rxjs';
// Core UI
import { CoreInputDateTime, CoreInputDateTimeComponent, CoreInputValidators } from '../../../core-inputs';
// Services
import { CoreSchedulerStateService } from '../../core-scheduler.state.service';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'scheduler-start-section',
  templateUrl: './scheduler-start-section.component.html',
  styleUrl: './scheduler-start-section.component.scss',
  imports: [CoreInputDateTimeComponent],
})
export class SchedulerStartSectionComponent implements OnInit, OnDestroy {
  // Public
  public value = input<Date | null>();
  public timeFormat = input<'hh:mm a' | 'HH:mm'>('hh:mm a');
  public valueChanges = output<void>();

  public getValue(): Date {
    return this._dateTimeControl!.value as Date;
  }

  public getDescription(): string {
    if (this._dateTimeControl?.value) {
      return `Starts on ${this._getDateDescription()}`;
    } else {
      return 'Start description not available';
    }
  }

  // Internal
  public _dateTimeControl: CoreInputDateTime | null = null;
  private _subscriptions$ = new Subscription();
  private _schedulerState = inject(CoreSchedulerStateService);

  public ngOnInit(): void {
    // Declare variables that depend on Input availability
    this._dateTimeControl = new CoreInputDateTime({
      label: 'Start Date and Time',
      showRequiredIndicator: true,
      min: this.value() ?? new Date(),
      value: this.value() ?? new Date(),
      format: `MM/dd/yyyy ${this.timeFormat()}`,
      validators: [CoreInputValidators.required('Required field')],
    });
    // Set initial status
    this._schedulerState.startSectionStatus.set(this._dateTimeControl.valid ? 'VALID' : 'INVALID');
    // Subscribe to value changes
    this._subscriptions$.add(
      this._dateTimeControl.valueChanges.subscribe(() => {
        this._schedulerState.startSectionStatus.set(this._dateTimeControl?.valid ? 'VALID' : 'INVALID');
        this.valueChanges.emit();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  private _getDateDescription(): string {
    const date: Date = this._dateTimeControl!.value as Date;
    const day = date.getDate();
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    const hour = date.getHours();
    const minute = date.getMinutes();
    if (this.timeFormat() === 'hh:mm a') {
      const timeSuffix = hour < 12 ? 'AM' : 'PM';
      const validHour = hour === 0 ? 12 : hour <= 12 ? hour : hour - 12;
      return `${this._twoDigits(month)}/${this._twoDigits(day)}/${year} at ${this._twoDigits(validHour)}:${this._twoDigits(minute)} ${timeSuffix}`;
    } else {
      return `${this._twoDigits(month)}/${this._twoDigits(day)}/${year} at ${this._twoDigits(hour)}:${this._twoDigits(minute)}`;
    }
  }

  private _twoDigits(num: number): string {
    return num.toString().padStart(2, '0');
  }
}
