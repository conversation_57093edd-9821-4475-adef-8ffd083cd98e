// Angular
import { Component, inject, input, OnD<PERSON>roy, OnInit, output } from '@angular/core';
import { merge, Subscription } from 'rxjs';
// Core UI
import { MonthlyRepeatDaysOfTheWeek } from '../../../../../../core-scheduler.interfaces';
import {
  CoreInputMultiselect,
  CoreInputMultiselectComponent,
  CoreInputMultiselectOption,
  CoreInputValidators,
} from '../../../../../../../core-inputs';
// Services
import { MonthlyRepeatStateService } from '../../monthly-repeat.state.service';

@Component({
  selector: 'app-monthly-repeat-day-of-week-mode',
  templateUrl: './monthly-repeat-day-of-week-mode.component.html',
  imports: [CoreInputMultiselectComponent],
})
export class MonthlyRepeatDayOfWeekModeComponent implements OnInit, OnDestroy {
  // Public

  public value = input<MonthlyRepeatDaysOfTheWeek | null>();
  public valueChanges = output<void>();

  public getValue(): MonthlyRepeatDaysOfTheWeek {
    return this._getValue();
  }

  public getDescription(): string {
    if ((this._weeksControl?.value as number[]).length > 0 && (this._daysControl?.value as number[]).length > 0) {
      return `on the ${this._getWeeksDescription()} ${this._getDaysDescription()}`;
    } else {
      return '[weeks/days-not-selected]';
    }
  }

  // Internal

  public _weeksControl: CoreInputMultiselect | null = null;
  public _daysControl: CoreInputMultiselect | null = null;

  private _monthlyRepeatState = inject(MonthlyRepeatStateService);
  private _subscriptions$ = new Subscription();

  public ngOnInit(): void {
    // Declare variables that depend on Input availability
    this._weeksControl = new CoreInputMultiselect({
      label: 'On the',
      checkboxes: true,
      showRequiredIndicator: true,
      options: this._getWeeksOptions(),
      validators: [CoreInputValidators.required('Required field')],
      value: this.value()?.weeks ?? [],
    });
    this._daysControl = new CoreInputMultiselect({
      label: 'Days',
      checkboxes: true,
      showRequiredIndicator: true,
      options: this._getDaysOptions(),
      validators: [CoreInputValidators.required('Required field')],
      value: this.value()?.weekDays ?? [],
    });
    // Set initial status
    this._updateStatus();
    // Subscribe to value changes and update status
    this._subscriptions$.add(
      merge(this._weeksControl.valueChanges, this._daysControl.valueChanges).subscribe(() => {
        this._updateStatus();
        this.valueChanges.emit();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  // Private

  private _getValue(): MonthlyRepeatDaysOfTheWeek {
    return {
      weeks: this._weeksControl!.value as number[],
      weekDays: this._daysControl!.value as number[],
    };
  }

  private _updateStatus(): void {
    const valueIsValid =
      (this._weeksControl!.value as number[]).length > 0 && (this._daysControl!.value as number[]).length > 0;
    this._monthlyRepeatState.monthlyRepeatDaysOfWeekModeStatus.set(valueIsValid ? 'VALID' : 'INVALID');
  }

  private _getWeeksOptions(): CoreInputMultiselectOption[] {
    return [
      { value: 1, label: '1st' },
      { value: 2, label: '2nd' },
      { value: 3, label: '3rd' },
      { value: 4, label: '4th' },
      { value: 5, label: '5th' },
      { value: -1, label: 'Last' },
    ];
  }

  private _getDaysOptions(): CoreInputMultiselectOption[] {
    return [
      { value: 1, label: 'Sunday' },
      { value: 2, label: 'Monday' },
      { value: 3, label: 'Tuesday' },
      { value: 4, label: 'Wednesday' },
      { value: 5, label: 'Thursday' },
      { value: 6, label: 'Friday' },
      { value: 7, label: 'Saturday' },
    ];
  }

  private _getWeeksDescription(): string {
    const weeksLabels = this._getValue().weeks.map(
      (weekValue) => this._getWeeksOptions().find((o) => o.value === weekValue)!.label,
    );
    return weeksLabels.join(', ');
  }

  private _getDaysDescription(): string {
    const daysLabels = this._getValue().weekDays.map(
      (dayValue) => this._getDaysOptions().find((o) => o.value === dayValue)!.label,
    );
    return daysLabels.join(', ');
  }
}
