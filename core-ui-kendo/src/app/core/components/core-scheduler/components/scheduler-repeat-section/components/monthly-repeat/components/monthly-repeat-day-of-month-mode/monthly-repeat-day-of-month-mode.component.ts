// Angular
import { Component, inject, input, OnD<PERSON>roy, OnInit, output } from '@angular/core';
import { Subscription } from 'rxjs';
// Core UI
import {
  CoreInputMultiselect,
  CoreInputMultiselectComponent,
  CoreInputMultiselectOption,
  CoreInputValidators,
} from '../../../../../../../core-inputs';
import { MonthlyRepeatDaysOfTheMonth } from '../../../../../../core-scheduler.interfaces';
// Services
import { MonthlyRepeatStateService } from '../../monthly-repeat.state.service';

@Component({
  selector: 'app-monthly-repeat-day-of-month-mode',
  templateUrl: './monthly-repeat-day-of-month-mode.component.html',
  imports: [CoreInputMultiselectComponent],
})
export class MonthlyRepeatDayOfMonthModeComponent implements OnInit, OnDestroy {
  // Public

  public value = input<MonthlyRepeatDaysOfTheMonth | null>();
  public valueChanges = output<void>();

  public getValue(): MonthlyRepeatDaysOfTheMonth {
    return this._getValue();
  }

  public getDescription(): string {
    if ((this._monthDaysControl?.value as number[]).length > 0) {
      return `on the ${this._getDescription()}`;
    } else {
      return '[days-not-selected]';
    }
  }

  // Internal

  public _monthDaysControl: CoreInputMultiselect | null = null;

  private _monthlyRepeatState = inject(MonthlyRepeatStateService);
  private _subscriptions$ = new Subscription();

  public ngOnInit(): void {
    // Declare variables that depend on Input availability
    this._monthDaysControl = new CoreInputMultiselect({
      label: 'On the',
      checkboxes: true,
      showRequiredIndicator: true,
      options: this._getMonthDaysOptions(),
      validators: [CoreInputValidators.required('Required field')],
      value: this.value()?.monthDays ?? [],
    });
    // Set initial status
    this._updateStatus();
    // Update status when values change
    this._subscriptions$.add(
      this._monthDaysControl.valueChanges.subscribe(() => {
        this._updateStatus();
        this.valueChanges.emit();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  // Private

  private _getValue(): MonthlyRepeatDaysOfTheMonth {
    return {
      monthDays: this._monthDaysControl!.value as number[],
    };
  }

  private _updateStatus(): void {
    const valueIsValid = (this._monthDaysControl!.value as number[]).length > 0;
    this._monthlyRepeatState.monthlyRepeatDaysOfMonthModeStatus.set(valueIsValid ? 'VALID' : 'INVALID');
  }

  private _getMonthDaysOptions(): CoreInputMultiselectOption[] {
    return [
      { value: 1, label: '1st' },
      { value: 2, label: '2nd' },
      { value: 3, label: '3rd' },
      { value: 4, label: '4th' },
      { value: 5, label: '5th' },
      { value: 6, label: '6th' },
      { value: 7, label: '7th' },
      { value: 8, label: '8th' },
      { value: 9, label: '9th' },
      { value: 10, label: '10th' },
      { value: 11, label: '11th' },
      { value: 12, label: '12th' },
      { value: 13, label: '13th' },
      { value: 14, label: '14th' },
      { value: 15, label: '15th' },
      { value: 16, label: '16th' },
      { value: 17, label: '17th' },
      { value: 18, label: '18th' },
      { value: 19, label: '19th' },
      { value: 20, label: '20th' },
      { value: 21, label: '21st' },
      { value: 22, label: '22nd' },
      { value: 23, label: '23rd' },
      { value: 24, label: '24th' },
      { value: 25, label: '25th' },
      { value: 26, label: '26th' },
      { value: 27, label: '27th' },
      { value: 28, label: '28th' },
      { value: 29, label: '29th' },
      { value: 30, label: '30th' },
      { value: 31, label: '31st' },
      { value: -1, label: 'Last' },
    ];
  }

  private _getDescription(): string {
    const selectedMonthDaysLabels = this._getValue().monthDays.map(
      (monthDayVaule) => this._getMonthDaysOptions().find((o) => o.value === monthDayVaule)!.label,
    );
    return selectedMonthDaysLabels.join(', ');
  }
}
