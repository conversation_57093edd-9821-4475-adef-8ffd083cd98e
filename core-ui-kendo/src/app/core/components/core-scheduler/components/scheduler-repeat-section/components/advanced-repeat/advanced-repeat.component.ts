// Angular
import { Component, inject, input, OnD<PERSON>roy, OnInit, output } from '@angular/core';
import { Subscription } from 'rxjs';
// Core UI
import { CoreInputText, CoreInputTextComponent, CoreInputValidators } from '../../../../../core-inputs';
import { CoreSchedulerRepeatAdvanced } from '../../../../core-scheduler.interfaces';
// Service
import { SchedulerRepeatSectionStateService } from '../../scheduler-repeat-section.state.service';

@Component({
  selector: 'app-advanced-repeat',
  templateUrl: './advanced-repeat.component.html',
  imports: [CoreInputTextComponent],
})
export class AdvancedRepeatComponent implements OnInit, OnDestroy {
  // Public
  public value = input<CoreSchedulerRepeatAdvanced | null>();
  public valueChanges = output<void>();

  public getValue(): CoreSchedulerRepeatAdvanced {
    return this._getValue();
  }

  public getDescription(): string {
    return '[repeats-advanced-description-not-available]';
  }

  // Internal

  public _cronControl: CoreInputText | null = null;

  private _repeatState = inject(SchedulerRepeatSectionStateService);
  private _subscriptions$ = new Subscription();

  public ngOnInit(): void {
    // Declare variables that depend on Input availability
    this._cronControl = new CoreInputText({
      label: 'Cron',
      showRequiredIndicator: true,
      validators: [
        CoreInputValidators.required('Required field'),
        CoreInputValidators.isValidCronExp('Not a valid cron expression'),
      ],
      value: this.value()?.cron ?? '',
    });
    // Set initial staus
    this._repeatState.advancedRepeatSectionStatus.set(this._cronControl.valid ? 'VALID' : 'INVALID');
    // Subscribe to values changes and update status
    this._subscriptions$.add(
      this._cronControl.valueChanges.subscribe(() => {
        this._repeatState.advancedRepeatSectionStatus.set(this._cronControl!.valid ? 'VALID' : 'INVALID');
        this.valueChanges.emit();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  // Private

  private _getValue(): CoreSchedulerRepeatAdvanced {
    return {
      cron: this._cronControl!.value as string,
    };
  }
}
