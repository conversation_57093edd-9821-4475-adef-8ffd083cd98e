// Angular
import { Component, inject, input, OnD<PERSON>roy, OnInit, output } from '@angular/core';
import { merge, Subscription } from 'rxjs';
// Core UI
import {
  CoreInputMultiselect,
  CoreInputValidators,
  CoreInputMultiselectOption,
  CoreInputMultiselectComponent,
} from '../../../../../../../core-inputs';
import { YearlyRepeatDaysOfTheWeek } from '../../../../../../core-scheduler.interfaces';
// Services
import { YearlyRepeatStateService } from '../../yearly-repeat.state.service';

@Component({
  selector: 'app-yearly-repeat-day-of-week-mode',
  imports: [CoreInputMultiselectComponent],
  templateUrl: './yearly-repeat-day-of-week-mode.component.html',
  styleUrl: './yearly-repeat-day-of-week-mode.component.scss',
})
export class YearlyRepeatDayOfWeekModeComponent implements OnInit, OnD<PERSON>roy {
  // Public

  public value = input<YearlyRepeatDaysOfTheWeek | null>();
  public valueChanges = output<void>();

  public getValue(): YearlyRepeatDaysOfTheWeek {
    return this._getValue();
  }

  public getDescription(): string {
    if ((this._weeksControl?.value as number[]).length > 0 && (this._daysControl?.value as number[]).length > 0) {
      return `on the ${this._getWeeksDescription()} ${this._getDaysDescription()}`;
    } else {
      return '[weeks/days-not-selected]';
    }
  }

  // Internal

  public _weeksControl: CoreInputMultiselect | null = null;
  public _daysControl: CoreInputMultiselect | null = null;

  private _yearlyRepeatState = inject(YearlyRepeatStateService);
  private _subscriptions$ = new Subscription();

  public ngOnInit(): void {
    // Declare variables that depend on Input availability
    this._weeksControl = new CoreInputMultiselect({
      label: 'On the',
      checkboxes: true,
      showRequiredIndicator: true,
      options: this._getWeeksOptions(),
      validators: [CoreInputValidators.required('Required field')],
      value: this.value()?.weeks ?? [],
    });
    this._daysControl = new CoreInputMultiselect({
      label: 'Days',
      checkboxes: true,
      showRequiredIndicator: true,
      options: this._getDaysOptions(),
      validators: [CoreInputValidators.required('Required field')],
      value: this.value()?.weekDays ?? [],
    });
    // On any value changes, update status
    this._subscriptions$.add(
      merge(this._weeksControl.valueChanges, this._daysControl.valueChanges).subscribe(() => {
        this._updateStatus();
        this.valueChanges.emit();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  private _updateStatus(): void {
    const valueIsValid =
      (this._weeksControl!.value as number[]).length > 0 && (this._daysControl!.value as number[]).length > 0;
    this._yearlyRepeatState.monthlyRepeatDaysOfWeekModeStatus.set(valueIsValid ? 'VALID' : 'INVALID');
  }

  private _getValue(): YearlyRepeatDaysOfTheWeek {
    return {
      weeks: this._weeksControl!.value as number[],
      weekDays: this._daysControl!.value as number[],
    };
  }

  private _getWeeksOptions(): CoreInputMultiselectOption[] {
    return [
      { value: 1, label: '1st' },
      { value: 2, label: '2nd' },
      { value: 3, label: '3rd' },
      { value: 4, label: '4th' },
      { value: 5, label: '5th' },
      { value: -1, label: 'Last' },
    ];
  }

  private _getDaysOptions(): CoreInputMultiselectOption[] {
    return [
      { value: 1, label: 'Sunday' },
      { value: 2, label: 'Monday' },
      { value: 3, label: 'Tuesday' },
      { value: 4, label: 'Wednesday' },
      { value: 5, label: 'Thursday' },
      { value: 6, label: 'Friday' },
      { value: 7, label: 'Saturday' },
    ];
  }

  private _getWeeksDescription(): string {
    const weeksLabels = this._getValue().weeks.map(
      (weekValue) => this._getWeeksOptions().find((o) => o.value === weekValue)!.label,
    );
    return weeksLabels.join(', ');
  }

  private _getDaysDescription(): string {
    const daysLabels = this._getValue().weekDays.map(
      (dayValue) => this._getDaysOptions().find((o) => o.value === dayValue)!.label,
    );
    return daysLabels.join(', ');
  }
}
