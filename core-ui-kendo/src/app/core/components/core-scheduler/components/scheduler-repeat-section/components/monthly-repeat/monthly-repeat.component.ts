// Angular
import {
  Component,
  computed,
  effect,
  inject,
  input,
  OnDestroy,
  OnInit,
  output,
  Signal,
  signal,
  viewChild,
} from '@angular/core';
import { Subscription } from 'rxjs';
// Components
import { MonthlyRepeatDayOfMonthModeComponent } from './components/monthly-repeat-day-of-month-mode/monthly-repeat-day-of-month-mode.component';
import { MonthlyRepeatDayOfWeekModeComponent } from './components/monthly-repeat-day-of-week-mode/monthly-repeat-day-of-week-mode.component';
// Core UI
import { CoreSchedulerValidityStatus } from '../../../../core-scheduler.types';
import {
  CoreSchedulerRepeatMonthly,
  MonthlyRepeatDaysOfTheMonth,
  MonthlyRepeatDaysOfTheWeek,
} from '../../../../core-scheduler.interfaces';
import { CoreSchedulerRepeatMonthlyMode } from '../../../../core-scheduler.enums';
import {
  CoreInputDropDown,
  CoreInputTime,
  CoreInputTimeComponent,
  CoreInputValidators,
} from '../../../../../core-inputs';
import { CoreInputDropDownComponent } from '../../../../../core-inputs/core-input-drop-down/core-input-drop-down.component';
// Services
import { SchedulerRepeatSectionStateService } from '../../scheduler-repeat-section.state.service';
import { MonthlyRepeatStateService } from './monthly-repeat.state.service';
import { CoreInputDropDownOption } from '../../../../../core-inputs/core-input-drop-down/core-input-drop-down.interfaces';

@Component({
  selector: 'app-monthly-repeat',
  templateUrl: './monthly-repeat.component.html',
  providers: [MonthlyRepeatStateService],
  imports: [
    MonthlyRepeatDayOfMonthModeComponent,
    MonthlyRepeatDayOfWeekModeComponent,
    CoreInputDropDownComponent,
    CoreInputTimeComponent,
    CoreInputTimeComponent,
    CoreInputDropDownComponent,
  ],
})
export class MonthlyRepeatComponent implements OnInit, OnDestroy {
  // Public

  public value = input<CoreSchedulerRepeatMonthly | null>();
  public timeFormat = input<'hh:mm a' | 'HH:mm'>('hh:mm a');
  public valueChanges = output<void>();

  public getValue(): CoreSchedulerRepeatMonthly {
    return this._getValue();
  }

  public getDescription(): string {
    if (this._mode() && this._timeControl?.value) {
      return `Repeats every month ${this._getMonthlyRepeatDescription()} at ${this._getTimeDescription()}`;
    } else {
      return '[repeats-monthly-mode/time-not-selected]';
    }
  }

  // Internal

  public _mode = signal<CoreSchedulerRepeatMonthlyMode>(CoreSchedulerRepeatMonthlyMode.DAYS_OF_THE_MONTH);
  public _modeControl: CoreInputDropDown | null = null;
  public _timeControl: CoreInputTime | null = null;

  private _sectionState = inject(SchedulerRepeatSectionStateService);
  private _monthlyRepeatState = inject(MonthlyRepeatStateService);
  private _status: Signal<CoreSchedulerValidityStatus>;
  private _daysOfMonthRef = viewChild(MonthlyRepeatDayOfMonthModeComponent);
  private _daysOfWeekRef = viewChild(MonthlyRepeatDayOfWeekModeComponent);
  private _subscriptions$ = new Subscription();

  constructor() {
    this._status = computed(() => {
      let modeStatus: CoreSchedulerValidityStatus = 'INVALID';
      switch (this._mode()) {
        case CoreSchedulerRepeatMonthlyMode.DAYS_OF_THE_MONTH:
          modeStatus = this._monthlyRepeatState.monthlyRepeatDaysOfMonthModeStatus();
          break;
        case CoreSchedulerRepeatMonthlyMode.DAYS_OF_THE_WEEK:
          modeStatus = this._monthlyRepeatState.monthlyRepeatDaysOfWeekModeStatus();
          break;
      }
      if (this._monthlyRepeatState.timeIsValid() && modeStatus === 'VALID') {
        return 'VALID';
      } else {
        return 'INVALID';
      }
    });
    // Update parent state status
    effect(() => {
      this._sectionState.monthlyRepeatSectionStatus.set(this._status());
    });
  }

  public ngOnInit(): void {
    // Declare variables that depend on Input availability
    this._modeControl = new CoreInputDropDown({
      label: 'Recurrence',
      showRequiredIndicator: true,
      options: this._getModeOptions(),
      validators: [CoreInputValidators.required('Required field')],
      value: this.value()?.mode ?? CoreSchedulerRepeatMonthlyMode.DAYS_OF_THE_MONTH,
    });
    this._mode.set(this._modeControl.value as CoreSchedulerRepeatMonthlyMode);
    this._timeControl = new CoreInputTime({
      label: 'At',
      format: this.timeFormat(),
      validators: [CoreInputValidators.required('Required field')],
      value: this.value() ? this._getHrsAndMinsAsDate(this.value()!.hour, this.value()!.minute) : new Date(),
    });
    // Set initial validations
    this._monthlyRepeatState.timeIsValid.set(this._timeControl.valid);
    // Subscribe to mode changes
    this._subscriptions$.add(
      this._modeControl.valueChanges.subscribe((mode) => {
        this._mode.set(mode as CoreSchedulerRepeatMonthlyMode);
        this.valueChanges.emit();
      }),
    );
    // Subscribe to time value changes to time validity
    this._subscriptions$.add(
      this._timeControl.valueChanges.subscribe((value) => {
        this._monthlyRepeatState.timeIsValid.set(!!value);
        this.valueChanges.emit();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  // Events

  public _valueChanges(): void {
    this.valueChanges.emit();
  }

  // Helpers

  public _daysOfMonthValue(): MonthlyRepeatDaysOfTheMonth | undefined {
    return this.value()?.value as MonthlyRepeatDaysOfTheMonth | undefined;
  }

  public _daysOfWeekValue(): MonthlyRepeatDaysOfTheWeek | undefined {
    return this.value()?.value as MonthlyRepeatDaysOfTheWeek | undefined;
  }

  // Private

  private _getValue(): CoreSchedulerRepeatMonthly {
    let value: MonthlyRepeatDaysOfTheMonth | MonthlyRepeatDaysOfTheWeek;
    switch (this._mode()) {
      case CoreSchedulerRepeatMonthlyMode.DAYS_OF_THE_MONTH:
        value = this._daysOfMonthRef()!.getValue();
        break;
      case CoreSchedulerRepeatMonthlyMode.DAYS_OF_THE_WEEK:
        value = this._daysOfWeekRef()!.getValue();
        break;
    }
    return {
      minute: (this._timeControl!.value as Date).getMinutes(),
      hour: (this._timeControl!.value as Date).getHours(),
      mode: this._mode(),
      value,
    };
  }

  private _getModeOptions(): CoreInputDropDownOption[] {
    return [
      { value: CoreSchedulerRepeatMonthlyMode.DAYS_OF_THE_MONTH, label: 'Days of the month' },
      { value: CoreSchedulerRepeatMonthlyMode.DAYS_OF_THE_WEEK, label: 'Days of the week' },
    ];
  }

  private _getHrsAndMinsAsDate(hours: number, minutes: number): Date {
    const time = new Date();
    time.setMinutes(minutes);
    time.setHours(hours);
    return time;
  }

  private _getMonthlyRepeatDescription(): string {
    switch (this._mode()) {
      case CoreSchedulerRepeatMonthlyMode.DAYS_OF_THE_MONTH:
        return this._daysOfMonthRef()!.getDescription();
      case CoreSchedulerRepeatMonthlyMode.DAYS_OF_THE_WEEK:
        return this._daysOfWeekRef()!.getDescription();
    }
  }

  private _getTimeDescription(): string {
    const date: Date = this._timeControl!.value as Date;
    const hour = date.getHours();
    const minute = date.getMinutes();
    if (this.timeFormat() === 'hh:mm a') {
      const timeSuffix = hour < 12 ? 'AM' : 'PM';
      const validHour = hour === 0 ? 12 : hour <= 12 ? hour : hour - 12;
      return `${this._twoDigits(validHour)}:${this._twoDigits(minute)} ${timeSuffix}`;
    } else {
      return `${this._twoDigits(hour)}:${this._twoDigits(minute)}`;
    }
  }

  private _twoDigits(num: number): string {
    return num.toString().padStart(2, '0');
  }
}
