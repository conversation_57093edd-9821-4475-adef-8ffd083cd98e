// Angular
import { Component, inject, input, On<PERSON><PERSON>roy, OnInit, output } from '@angular/core';
import { merge, Subscription } from 'rxjs';
// Core UI
import {
  CoreInputMultiselect,
  CoreInputMultiselectComponent,
  CoreInputMultiselectOption,
  CoreInputTime,
  CoreInputTimeComponent,
  CoreInputValidators,
} from '../../../../../core-inputs';
import { CoreSchedulerRepeatWeekly } from '../../../../core-scheduler.interfaces';
// Services
import { SchedulerRepeatSectionStateService } from '../../scheduler-repeat-section.state.service';

@Component({
  selector: 'app-weekly-repeat',
  templateUrl: './weekly-repeat.component.html',
  imports: [CoreInputTimeComponent, CoreInputMultiselectComponent],
})
export class WeeklyRepeatComponent implements OnInit, OnDestroy {
  // Public

  public value = input<CoreSchedulerRepeatWeekly | null>();
  public timeFormat = input<'hh:mm a' | 'HH:mm'>('hh:mm a');
  public valueChanges = output<void>();

  public getValue(): CoreSchedulerRepeatWeekly {
    return this._getValue();
  }

  public getDescription(): string {
    if (this._daysControl?.value && this._timeControl?.value) {
      return `Repeats every week on ${this._getDaysDescription()} at ${this._getTimeDescription()}`;
    } else {
      return '[repeats-daily-days/time-not-selected]';
    }
  }

  // Internal

  public _daysControl: CoreInputMultiselect | null = null;
  public _timeControl: CoreInputTime | null = null;

  private _sectionState = inject(SchedulerRepeatSectionStateService);
  private _subscriptions$ = new Subscription();

  public ngOnInit(): void {
    // Declare variables that depend on Input availability
    this._daysControl = new CoreInputMultiselect({
      label: 'Days',
      checkboxes: true,
      showRequiredIndicator: true,
      options: this._getDaysOptions(),
      validators: [CoreInputValidators.required('Required field')],
      value: this.value() ? this.value()!.weekDays : [],
    });
    this._timeControl = new CoreInputTime({
      label: 'At',
      format: this.timeFormat(),
      validators: [CoreInputValidators.required('Required field')],
      value: this.value() ? this._getHrsAndMinsAsDate(this.value()!.hour, this.value()!.minute) : new Date(),
    });
    // Set initial status
    this._updateStatus();
    // Subscribe to value changes and update status
    this._subscriptions$.add(
      merge(this._daysControl.valueChanges, this._timeControl.valueChanges).subscribe(() => {
        this._updateStatus();
        this.valueChanges.emit();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  private _updateStatus(): void {
    const daysAreValid = (this._daysControl?.value as number[]).length > 0;
    const timeIsValid = this._timeControl?.value != null;
    if (daysAreValid && timeIsValid) {
      this._sectionState.weeklyRepeatSectionStatus.set('VALID');
    } else {
      this._sectionState.weeklyRepeatSectionStatus.set('INVALID');
    }
  }

  private _getValue(): CoreSchedulerRepeatWeekly {
    const time: Date = this._timeControl!.value as Date;
    const minute = time.getMinutes();
    const hour = time.getHours();
    const weekDays = this._daysControl!.value as number[];
    return {
      minute,
      hour,
      weekDays,
    };
  }

  private _getHrsAndMinsAsDate(hours: number, minutes: number): Date {
    const time = new Date();
    time.setMinutes(minutes);
    time.setHours(hours);
    return time;
  }

  private _getDaysOptions(): CoreInputMultiselectOption[] {
    return [
      { value: 1, label: 'Sunday' },
      { value: 2, label: 'Monday' },
      { value: 3, label: 'Tuesday' },
      { value: 4, label: 'Wednesday' },
      { value: 5, label: 'Thursday' },
      { value: 6, label: 'Friday' },
      { value: 7, label: 'Saturday' },
    ];
  }

  private _getDaysDescription(): string {
    const selectedDaysLabels = this._getValue().weekDays.map(
      (dayValue) => this._getDaysOptions().find((o) => o.value === dayValue)!.label,
    );
    if (selectedDaysLabels.length > 0) {
      return selectedDaysLabels.join(', ');
    } else {
      return '[days-not-selected]';
    }
  }

  private _getTimeDescription(): string {
    const date: Date = this._timeControl!.value as Date;
    const hour = date.getHours();
    const minute = date.getMinutes();
    if (this.timeFormat() === 'hh:mm a') {
      const timeSuffix = hour < 12 ? 'AM' : 'PM';
      const validHour = hour === 0 ? 12 : hour <= 12 ? hour : hour - 12;
      return `${this._twoDigits(validHour)}:${this._twoDigits(minute)} ${timeSuffix}`;
    } else {
      return `${this._twoDigits(hour)}:${this._twoDigits(minute)}`;
    }
  }

  private _twoDigits(num: number): string {
    return num.toString().padStart(2, '0');
  }
}
