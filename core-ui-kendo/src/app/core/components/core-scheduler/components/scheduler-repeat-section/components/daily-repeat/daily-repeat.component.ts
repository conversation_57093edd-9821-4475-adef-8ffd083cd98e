// Angular
import { Component, inject, input, OnD<PERSON>roy, OnInit, output } from '@angular/core';
import { Subscription } from 'rxjs';
// Core UI
import { CoreInputTime, CoreInputTimeComponent, CoreInputValidators } from '../../../../../core-inputs';
import { CoreSchedulerRepeatDaily } from '../../../../core-scheduler.interfaces';
// Services
import { SchedulerRepeatSectionStateService } from '../../scheduler-repeat-section.state.service';

@Component({
  selector: 'app-daily-repeat',
  templateUrl: './daily-repeat.component.html',
  imports: [CoreInputTimeComponent],
})
export class DailyRepeatComponent implements OnInit, OnDestroy {
  // Public
  public value = input<CoreSchedulerRepeatDaily | null>();
  public timeFormat = input<'hh:mm a' | 'HH:mm'>('hh:mm a');
  public valueChanges = output<void>();

  public getValue(): CoreSchedulerRepeatDaily {
    return this._getValue();
  }

  public getDescription(): string {
    if (this._timeControl?.value) {
      return `Repeats every day at ${this._getTimeDescription()}`;
    } else {
      return '[repeats-daily-time-not-selected]';
    }
  }

  // Internal
  public _timeControl: CoreInputTime | null = null;

  private _sectionState = inject(SchedulerRepeatSectionStateService);
  private _subscriptions$ = new Subscription();

  public ngOnInit(): void {
    // Declare variables that depend on Input availability
    this._timeControl = new CoreInputTime({
      label: 'At',
      format: this.timeFormat(),
      validators: [CoreInputValidators.required('Required field')],
      value: this.value() ? this._getValueAsDate(this.value()!) : new Date(),
    });
    // Set initial status
    this._updateStatus();
    // Subscribe to value changes and update status
    this._subscriptions$.add(
      this._timeControl.valueChanges.subscribe(() => {
        this._updateStatus();
        this.valueChanges.emit();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  private _updateStatus(): void {
    const timeIsValid = !!this._timeControl?.valid;
    this._sectionState.dailyRepeatSectionStatus.set(timeIsValid ? 'VALID' : 'INVALID');
  }

  private _getValueAsDate(value: CoreSchedulerRepeatDaily): Date {
    const time = new Date();
    time.setMinutes(value.minute);
    time.setHours(value.hour);
    return time;
  }

  private _getValue(): CoreSchedulerRepeatDaily {
    const time: Date = this._timeControl!.value as Date;
    const minute = time.getMinutes();
    const hour = time.getHours();
    return { minute, hour };
  }

  private _getTimeDescription(): string {
    const date: Date = this._timeControl!.value as Date;
    const hour = date.getHours();
    const minute = date.getMinutes();
    if (this.timeFormat() === 'hh:mm a') {
      const timeSuffix = hour < 12 ? 'AM' : 'PM';
      const validHour = hour === 0 ? 12 : hour <= 12 ? hour : hour - 12;
      return `${this._twoDigits(validHour)}:${this._twoDigits(minute)} ${timeSuffix}`;
    } else {
      return `${this._twoDigits(hour)}:${this._twoDigits(minute)}`;
    }
  }

  private _twoDigits(num: number): string {
    return num.toString().padStart(2, '0');
  }
}
