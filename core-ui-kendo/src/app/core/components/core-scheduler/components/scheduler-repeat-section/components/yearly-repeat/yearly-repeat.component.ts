// Angular
import {
  Component,
  computed,
  effect,
  inject,
  input,
  OnD<PERSON>roy,
  OnInit,
  output,
  Signal,
  signal,
  viewChild,
} from '@angular/core';
import { Subscription } from 'rxjs';
// Core UI
import {
  CoreSchedulerRepeatYearly,
  YearlyRepeatDaysOfTheMonth,
  YearlyRepeatDaysOfTheWeek,
} from '../../../../core-scheduler.interfaces';
import { CoreSchedulerRepeatYearlyMode } from '../../../../core-scheduler.enums';
import { CoreSchedulerValidityStatus } from '../../../../core-scheduler.types';
import {
  CoreInputDropDown,
  CoreInputDropDownComponent,
  CoreInputMultiselect,
  CoreInputMultiselectComponent,
  CoreInputMultiselectOption,
  CoreInputTime,
  CoreInputTimeComponent,
  CoreInputValidators,
} from '../../../../../core-inputs';
import { YearlyRepeatDayOfMonthModeComponent } from './components/yearly-repeat-day-of-month-mode/yearly-repeat-day-of-month-mode.component';
import { YearlyRepeatDayOfWeekModeComponent } from './components/yearly-repeat-day-of-week-mode/yearly-repeat-day-of-week-mode.component';
// Services
import { YearlyRepeatStateService } from './yearly-repeat.state.service';
import { SchedulerRepeatSectionStateService } from '../../scheduler-repeat-section.state.service';

@Component({
  selector: 'app-yearly-repeat',
  templateUrl: './yearly-repeat.component.html',
  providers: [YearlyRepeatStateService],
  imports: [
    CoreInputDropDownComponent,
    CoreInputMultiselectComponent,
    CoreInputTimeComponent,
    YearlyRepeatDayOfMonthModeComponent,
    YearlyRepeatDayOfWeekModeComponent,
  ],
})
export class YearlyRepeatComponent implements OnInit, OnDestroy {
  // Public

  public value = input<CoreSchedulerRepeatYearly | null>();
  public timeFormat = input<'hh:mm a' | 'HH:mm'>('hh:mm a');
  public valueChanges = output<void>();

  public getValue(): CoreSchedulerRepeatYearly {
    return this._getValue();
  }

  public getDescription(): string {
    if (this._mode() && (this._monthsControl?.value as number[]).length > 0 && this._timeControl?.value) {
      return this._getYearlyRepeatDescription();
    } else {
      return '[repeats-yearly-mode/months/time-not-selected]';
    }
  }

  // Internal

  public _modeControl: CoreInputDropDown | null = null;
  public _monthsControl: CoreInputMultiselect | null = null;
  public _timeControl: CoreInputTime | null = null;
  public _mode = signal<CoreSchedulerRepeatYearlyMode>(CoreSchedulerRepeatYearlyMode.DAYS_OF_THE_MONTH);

  private _yearlyRepeatState = inject(YearlyRepeatStateService);
  private _sectionState = inject(SchedulerRepeatSectionStateService);
  private _status: Signal<CoreSchedulerValidityStatus>;
  private _daysOfMonthRef = viewChild(YearlyRepeatDayOfMonthModeComponent);
  private _daysOfWeekRef = viewChild(YearlyRepeatDayOfWeekModeComponent);
  private _subscriptions$ = new Subscription();

  constructor() {
    this._status = computed(() => {
      let modeStatus: CoreSchedulerValidityStatus = 'INVALID';
      switch (this._mode()) {
        case CoreSchedulerRepeatYearlyMode.DAYS_OF_THE_MONTH:
          modeStatus = this._yearlyRepeatState.monthlyRepeatDaysOfMonthModeStatus();
          break;
        case CoreSchedulerRepeatYearlyMode.DAYS_OF_THE_WEEK:
          modeStatus = this._yearlyRepeatState.monthlyRepeatDaysOfWeekModeStatus();
          break;
      }
      if (modeStatus === 'VALID' && this._yearlyRepeatState.timeIsValid() && this._yearlyRepeatState.monthsAreValid()) {
        return 'VALID';
      } else {
        return 'INVALID';
      }
    });

    effect(() => {
      // Update parent status state when internal status changes
      this._sectionState.yearlyRepeatSectionStatus.set(this._status());
    });
  }

  public ngOnInit(): void {
    // Declare variables that depend on Input availability
    this._modeControl = new CoreInputDropDown({
      label: 'Recurrence',
      showRequiredIndicator: true,
      options: this._getModeOptions(),
      validators: [CoreInputValidators.required('Required field')],
      value: this.value()?.mode ?? CoreSchedulerRepeatYearlyMode.DAYS_OF_THE_MONTH,
    });
    this._mode.set(this._modeControl.value as CoreSchedulerRepeatYearlyMode);
    this._monthsControl = new CoreInputMultiselect({
      label: 'Months',
      checkboxes: true,
      showRequiredIndicator: true,
      options: this._getMonthsOptions(),
      validators: [CoreInputValidators.required('Required field')],
      value: this.value()?.months ?? [],
    });
    this._timeControl = new CoreInputTime({
      label: 'At',
      format: this.timeFormat(),
      validators: [CoreInputValidators.required('Required field')],
      value: this.value() ? this._getHrsAndMinsAsDate(this.value()!.hour, this.value()!.minute) : new Date(),
    });
    // Set initial validations
    this._yearlyRepeatState.timeIsValid.set(this._timeControl.valid);
    this._yearlyRepeatState.monthsAreValid.set(this._monthsControl.valid);
    // Subscribe to mode changes
    this._subscriptions$.add(
      this._modeControl.valueChanges.subscribe((mode) => {
        this._mode.set(mode as CoreSchedulerRepeatYearlyMode);
        this.valueChanges.emit();
      }),
    );
    // Subscribe to value changes and update respective status in state service
    this._subscriptions$.add(
      this._timeControl.valueChanges.subscribe(() => {
        this._yearlyRepeatState.timeIsValid.set(!!this._timeControl?.valid);
        this.valueChanges.emit();
      }),
    );
    // Subscribe to value changes and update respective status in state service
    this._subscriptions$.add(
      this._monthsControl.valueChanges.subscribe(() => {
        this._yearlyRepeatState.monthsAreValid.set(!!this._monthsControl?.valid);
        this.valueChanges.emit();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  // Events

  public _valueChanges(): void {
    this.valueChanges.emit();
  }

  // Helpers

  public _daysOfMonthValue(): YearlyRepeatDaysOfTheMonth | undefined {
    return this.value()?.value as YearlyRepeatDaysOfTheMonth | undefined;
  }

  public _daysOfWeekValue(): YearlyRepeatDaysOfTheWeek | undefined {
    return this.value()?.value as YearlyRepeatDaysOfTheWeek | undefined;
  }

  // Private

  private _getValue(): CoreSchedulerRepeatYearly {
    let value: YearlyRepeatDaysOfTheMonth | YearlyRepeatDaysOfTheWeek;
    switch (this._mode()) {
      case CoreSchedulerRepeatYearlyMode.DAYS_OF_THE_MONTH:
        value = this._daysOfMonthRef()!.getValue();
        break;
      case CoreSchedulerRepeatYearlyMode.DAYS_OF_THE_WEEK:
        value = this._daysOfWeekRef()!.getValue();
        break;
    }
    return {
      minute: (this._timeControl!.value as Date).getMinutes(),
      hour: (this._timeControl!.value as Date).getHours(),
      mode: this._mode(),
      months: this._monthsControl!.value as number[],
      value,
    };
  }

  private _getModeOptions(): CoreInputMultiselectOption[] {
    return [
      { value: CoreSchedulerRepeatYearlyMode.DAYS_OF_THE_MONTH, label: 'Days of the month' },
      { value: CoreSchedulerRepeatYearlyMode.DAYS_OF_THE_WEEK, label: 'Days of the week' },
    ];
  }

  private _getMonthsOptions(): CoreInputMultiselectOption[] {
    return [
      { value: 1, label: 'January' },
      { value: 2, label: 'February' },
      { value: 3, label: 'March' },
      { value: 4, label: 'April' },
      { value: 5, label: 'May' },
      { value: 6, label: 'June' },
      { value: 7, label: 'July' },
      { value: 8, label: 'August' },
      { value: 9, label: 'September' },
      { value: 10, label: 'October' },
      { value: 11, label: 'November' },
      { value: 12, label: 'December' },
    ];
  }

  private _getHrsAndMinsAsDate(hours: number, minutes: number): Date {
    const time = new Date();
    time.setMinutes(minutes);
    time.setHours(hours);
    return time;
  }

  private _getYearlyRepeatDescription(): string {
    const monthsDescription = (this._monthsControl!.value as string[])
      .map((monthValue: string) => this._getMonthsOptions().find((o) => o.value === monthValue)!.label)
      .join(', ');
    let modeValueDescription: string;
    switch (this._mode()) {
      case CoreSchedulerRepeatYearlyMode.DAYS_OF_THE_MONTH:
        modeValueDescription = this._daysOfMonthRef()!.getDescription();
        break;
      case CoreSchedulerRepeatYearlyMode.DAYS_OF_THE_WEEK:
        modeValueDescription = this._daysOfWeekRef()!.getDescription();
        break;
    }
    const timeDescription = this._getTimeDescription();
    return `Repeats in ${monthsDescription} on ${modeValueDescription} at ${timeDescription}`;
  }

  private _getTimeDescription(): string {
    const date: Date = this._timeControl!.value as Date;
    const hour = date.getHours();
    const minute = date.getMinutes();
    if (this.timeFormat() === 'hh:mm a') {
      const timeSuffix = hour < 12 ? 'AM' : 'PM';
      const validHour = hour === 0 ? 12 : hour <= 12 ? hour : hour - 12;
      return `${this._twoDigits(validHour)}:${this._twoDigits(minute)} ${timeSuffix}`;
    } else {
      return `${this._twoDigits(hour)}:${this._twoDigits(minute)}`;
    }
  }

  private _twoDigits(num: number): string {
    return num.toString().padStart(2, '0');
  }
}
