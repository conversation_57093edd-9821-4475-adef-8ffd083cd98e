// Angular
import {
  Component,
  computed,
  effect,
  inject,
  input,
  OnD<PERSON>roy,
  OnInit,
  output,
  Signal,
  signal,
  viewChild,
} from '@angular/core';
import { Subscription } from 'rxjs';
// Core UI
import { CoreInputRadioSelect, CoreInputRadioSelectComponent, CoreInputRadioSelectOption } from '../../../core-inputs';
import { CoreSchedulerValidityStatus } from '../../core-scheduler.types';
import { DailyRepeatComponent } from './components/daily-repeat/daily-repeat.component';
import { WeeklyRepeatComponent } from './components/weekly-repeat/weekly-repeat.component';
import { MonthlyRepeatComponent } from './components/monthly-repeat/monthly-repeat.component';
import { YearlyRepeatComponent } from './components/yearly-repeat/yearly-repeat.component';
import { AdvancedRepeatComponent } from './components/advanced-repeat/advanced-repeat.component';
import {
  CoreSchedulerRepeatAdvanced,
  CoreSchedulerRepeatDaily,
  CoreSchedulerRepeatMonthly,
  CoreSchedulerRepeatValue,
  CoreSchedulerRepeatWeekly,
  CoreSchedulerRepeatYearly,
} from '../../core-scheduler.interfaces';
import { CoreSchedulerRepeatMode } from '../../core-scheduler.enums';
// Services
import { CoreSchedulerStateService } from '../../core-scheduler.state.service';
import { SchedulerRepeatSectionStateService } from './scheduler-repeat-section.state.service';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'scheduler-repeat-section',
  templateUrl: './scheduler-repeat-section.component.html',
  styleUrl: './scheduler-repeat-section.component.scss',
  providers: [SchedulerRepeatSectionStateService],
  imports: [
    CoreInputRadioSelectComponent,
    DailyRepeatComponent,
    WeeklyRepeatComponent,
    MonthlyRepeatComponent,
    YearlyRepeatComponent,
    AdvancedRepeatComponent,
  ],
})
export class SchedulerRepeatSectionComponent implements OnInit, OnDestroy {
  // Public

  public value = input<CoreSchedulerRepeatValue>();
  public timeFormat = input<'hh:mm a' | 'HH:mm'>('hh:mm a');
  public valueChanges = output<void>();

  public getValue(): CoreSchedulerRepeatValue {
    return this._getValue();
  }

  public getDescription(): string {
    if (this._mode()) {
      return `${this._getRepeatDescription()}`;
    } else {
      return 'Repeat description not available';
    }
  }

  // Internal

  public _modeControl: CoreInputRadioSelect | null = null;
  public _value = signal<CoreSchedulerRepeatValue | null>(null);
  private _status: Signal<CoreSchedulerValidityStatus>;
  private _mode = signal<CoreSchedulerRepeatMode>(CoreSchedulerRepeatMode.ONCE);
  private _dailySectionRef = viewChild(DailyRepeatComponent);
  private _weeklySectionRef = viewChild(WeeklyRepeatComponent);
  private _monthlySectionRef = viewChild(MonthlyRepeatComponent);
  private _yearlySectionRef = viewChild(YearlyRepeatComponent);
  private _advancedSectionRef = viewChild(AdvancedRepeatComponent);
  private _sectionState = inject(SchedulerRepeatSectionStateService);
  private _schedulerState = inject(CoreSchedulerStateService);

  private _subscriptions$ = new Subscription();

  constructor() {
    this._status = computed(() => {
      switch (this._mode()) {
        case CoreSchedulerRepeatMode.ONCE:
          return 'VALID';
        case CoreSchedulerRepeatMode.DAILY:
          return this._sectionState.dailyRepeatSectionStatus();
        case CoreSchedulerRepeatMode.WEEKLY:
          return this._sectionState.weeklyRepeatSectionStatus();
        case CoreSchedulerRepeatMode.MONTHLY:
          return this._sectionState.monthlyRepeatSectionStatus();
        case CoreSchedulerRepeatMode.YEARLY:
          return this._sectionState.yearlyRepeatSectionStatus();
        case CoreSchedulerRepeatMode.ADVANCED:
          return this._sectionState.advancedRepeatSectionStatus();
        default:
          return 'INVALID';
      }
    });
    // Update repeat section status in scheduler state
    effect(() => {
      this._schedulerState.repeatSectionStatus.set(this._status());
    });
    // Update repeat section mode value in scheduler state
    effect(() => {
      this._schedulerState.repeatSectionMode.set(this._mode());
    });
  }

  public ngOnInit(): void {
    // Declare variables that depend on Input availability
    this._value.set(this.value() ?? null);
    this._modeControl = new CoreInputRadioSelect({
      label: 'Mode',
      options: this._getModeOptions(),
      value: this.value()?.mode ?? CoreSchedulerRepeatMode.ONCE,
    });
    this._mode.set(this._modeControl.value as CoreSchedulerRepeatMode);
    // Subscribe to mode control and set mode
    this._subscriptions$.add(
      this._modeControl.valueChanges.subscribe((mode) => {
        this._mode.set(mode as CoreSchedulerRepeatMode);
        // Reset values to eliminate potential conflicts between different mode values being passed incorrectly
        this._value.set(null);
        this.valueChanges.emit();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  // Events

  public _valueChanges(): void {
    this.valueChanges.emit();
  }

  // Helpers

  public _dailySectionValue(): CoreSchedulerRepeatDaily | null {
    return this._value()?.value as CoreSchedulerRepeatDaily | null;
  }

  public _weeklySectionValue(): CoreSchedulerRepeatWeekly | null {
    return this._value()?.value as CoreSchedulerRepeatWeekly | null;
  }

  public _monthlySectionValue(): CoreSchedulerRepeatMonthly | null {
    return this._value()?.value as CoreSchedulerRepeatMonthly | null;
  }

  public _yearlySectionValue(): CoreSchedulerRepeatYearly | null {
    return this._value()?.value as CoreSchedulerRepeatYearly | null;
  }

  public _advancedSectionValue(): CoreSchedulerRepeatAdvanced | null {
    return this._value()?.value as CoreSchedulerRepeatAdvanced | null;
  }

  private _getValue(): CoreSchedulerRepeatValue {
    let value:
      | CoreSchedulerRepeatDaily
      | CoreSchedulerRepeatWeekly
      | CoreSchedulerRepeatMonthly
      | CoreSchedulerRepeatYearly
      | CoreSchedulerRepeatAdvanced
      | null = null;
    switch (this._mode()) {
      case CoreSchedulerRepeatMode.DAILY:
        value = this._dailySectionRef()!.getValue();
        break;
      case CoreSchedulerRepeatMode.WEEKLY:
        value = this._weeklySectionRef()!.getValue();
        break;
      case CoreSchedulerRepeatMode.MONTHLY:
        value = this._monthlySectionRef()!.getValue();
        break;
      case CoreSchedulerRepeatMode.YEARLY:
        value = this._yearlySectionRef()!.getValue();
        break;
      case CoreSchedulerRepeatMode.ADVANCED:
        value = this._advancedSectionRef()!.getValue();
        break;
    }
    return {
      mode: this._mode(),
      value,
    };
  }

  private _getModeOptions(): CoreInputRadioSelectOption[] {
    return [
      { value: CoreSchedulerRepeatMode.ONCE, label: 'Once' },
      { value: CoreSchedulerRepeatMode.DAILY, label: 'Daily' },
      { value: CoreSchedulerRepeatMode.WEEKLY, label: 'Weekly' },
      { value: CoreSchedulerRepeatMode.MONTHLY, label: 'Monthly' },
      { value: CoreSchedulerRepeatMode.YEARLY, label: 'Yearly' },
      { value: CoreSchedulerRepeatMode.ADVANCED, label: 'Advanced' },
    ];
  }

  private _getRepeatDescription(): string {
    switch (this._mode()) {
      case CoreSchedulerRepeatMode.ONCE:
        return 'Executes once and does not repeat';
      case CoreSchedulerRepeatMode.DAILY:
        return this._dailySectionRef()?.getDescription() ?? '[repeat-desc-error]';
      case CoreSchedulerRepeatMode.WEEKLY:
        return this._weeklySectionRef()?.getDescription() ?? '[repeat-desc-error]';
      case CoreSchedulerRepeatMode.MONTHLY:
        return this._monthlySectionRef()?.getDescription() ?? '[repeat-desc-error]';
      case CoreSchedulerRepeatMode.YEARLY:
        return this._yearlySectionRef()?.getDescription() ?? '[repeat-desc-error]';
      case CoreSchedulerRepeatMode.ADVANCED:
        return this._advancedSectionRef()?.getDescription() ?? '[repeat-desc-error]';
    }
  }
}
