// Angular
import { Component, inject, input, On<PERSON><PERSON>roy, OnInit, output } from '@angular/core';
import { Subscription } from 'rxjs';
// Core UI
import { CoreSchedulerDurationEndTime } from '../../../../core-scheduler.interfaces';
import { CoreInputTime, CoreInputTimeComponent, CoreInputValidators } from '../../../../../core-inputs';
// Services
import { SchedulerDurationSectionStateService } from '../../scheduler-duration-section.state.service';

@Component({
  selector: 'app-duration-end-time',
  templateUrl: './duration-end-time.component.html',
  styleUrl: './duration-end-time.component.scss',
  imports: [CoreInputTimeComponent],
})
export class DurationEndTimeComponent implements OnInit, OnDestroy {
  // Public

  public value = input<CoreSchedulerDurationEndTime>();
  public timeFormat = input<'hh:mm a' | 'HH:mm'>('hh:mm a');
  public valueChanges = output<void>();

  public getValue(): CoreSchedulerDurationEndTime {
    return {
      date: this._timeControl!.value as Date,
    };
  }

  public getDescription(): string {
    if (this._timeControl?.value) {
      return `Runs until ${this._getTimeDescription()} after the scheduled time`;
    } else {
      return '[end-time-not-selected]';
    }
  }

  // Internal
  public _timeControl: CoreInputTime | null = null;
  private _subscriptions$ = new Subscription();
  private _durationSectionState = inject(SchedulerDurationSectionStateService);

  public ngOnInit(): void {
    // Declare variables that depend on Input availability
    this._timeControl = new CoreInputTime({
      label: 'At',
      format: this.timeFormat(),
      validators: [CoreInputValidators.required('Required field')],
      value: this.value()?.date ?? new Date(),
    });
    // Set initial section state status
    this._durationSectionState.endTimeSectionStatus.set(this._timeControl.valid ? 'VALID' : 'INVALID');
    // On any value changes, update section state status
    this._subscriptions$.add(
      this._timeControl.valueChanges.subscribe(() => {
        this._durationSectionState.endTimeSectionStatus.set(this._timeControl!.valid ? 'VALID' : 'INVALID');
        this.valueChanges.emit();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  private _getTimeDescription(): string {
    const date = this._timeControl!.value as Date;
    const hour = date.getHours();
    const minute = date.getMinutes();
    if (this.timeFormat() === 'hh:mm a') {
      const timeSuffix = hour < 12 ? 'AM' : 'PM';
      const validHour = hour === 0 ? 12 : hour <= 12 ? hour : hour - 12;
      return `${this._twoDigits(validHour)}:${this._twoDigits(minute)} ${timeSuffix}`;
    } else {
      return `${this._twoDigits(hour)}:${this._twoDigits(minute)}`;
    }
  }

  private _twoDigits(num: number): string {
    return num.toString().padStart(2, '0');
  }
}
