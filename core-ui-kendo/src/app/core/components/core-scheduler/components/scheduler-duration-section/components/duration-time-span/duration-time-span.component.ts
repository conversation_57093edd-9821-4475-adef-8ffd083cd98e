// Angular
import { Component, inject, input, On<PERSON><PERSON>roy, OnInit, output } from '@angular/core';
import { merge, Subscription } from 'rxjs';
// Core UI
import { CoreSchedulerDurationTimeSpan } from '../../../../core-scheduler.interfaces';
import { CoreInputDropDown, CoreInputDropDownComponent, CoreInputValidators } from '../../../../../core-inputs';
import { CoreInputDropDownOption } from '../../../../../core-inputs/core-input-drop-down/core-input-drop-down.interfaces';
// Services
import { SchedulerDurationSectionStateService } from '../../scheduler-duration-section.state.service';

@Component({
  selector: 'app-duration-time-span',
  templateUrl: './duration-time-span.component.html',
  styleUrl: './duration-time-span.component.scss',
  imports: [CoreInputDropDownComponent],
})
export class DurationTimeSpanComponent implements OnInit, OnD<PERSON>roy {
  // Public

  public value = input<CoreSchedulerDurationTimeSpan>();
  public valueChanges = output<void>();

  public getValue(): CoreSchedulerDurationTimeSpan {
    return {
      days: this._daysControl!.value as number,
      hours: this._hoursControl!.value as number,
      minutes: this._minutesControl!.value as number,
    };
  }

  public getDescription(): string {
    if (this._daysControl?.value || this._hoursControl?.value || this._minutesControl?.value) {
      return `Runs for ${this._getDescription()}`;
    } else {
      return '[days/time/minutes-not-selected]';
    }
  }

  // Internal
  public _daysControl: CoreInputDropDown | null = null;
  public _hoursControl: CoreInputDropDown | null = null;
  public _minutesControl: CoreInputDropDown | null = null;

  private _subscriptions$ = new Subscription();
  private _durationSectionState = inject(SchedulerDurationSectionStateService);

  // Private

  public ngOnInit(): void {
    // Declare variables that depend on Input availability
    this._daysControl = new CoreInputDropDown({
      label: 'Days',
      showRequiredIndicator: true,
      options: this._getDaysOptions(),
      validators: [CoreInputValidators.required('Required field')],
      value: this.value()?.days ?? 0,
    });
    this._hoursControl = new CoreInputDropDown({
      label: 'Hours',
      showRequiredIndicator: true,
      options: this._getHoursOptions(),
      validators: [CoreInputValidators.required('Required field')],
      value: this.value()?.hours ?? 0,
    });
    this._minutesControl = new CoreInputDropDown({
      label: 'Minutes',
      showRequiredIndicator: true,
      options: this._getMinutesOptions(),
      validators: [CoreInputValidators.required('Required field')],
      value: this.value()?.minutes ?? 0,
    });
    // Set initial status
    this._durationSectionState.timeSpanSectionStatus.set(this._valueIsValid() ? 'VALID' : 'INVALID');
    // Subscribe to any value changes and update section status in parent state
    this._subscriptions$.add(
      merge(
        this._daysControl.valueChanges,
        this._hoursControl.valueChanges,
        this._minutesControl.valueChanges,
      ).subscribe(() => {
        this._durationSectionState.timeSpanSectionStatus.set(this._valueIsValid() ? 'VALID' : 'INVALID');
        this.valueChanges.emit();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  private _getDaysOptions(): CoreInputDropDownOption[] {
    return this._numbersAsOptions(0, 366);
  }

  private _getHoursOptions(): CoreInputDropDownOption[] {
    return this._numbersAsOptions(0, 23);
  }

  private _getMinutesOptions(): CoreInputDropDownOption[] {
    return this._numbersAsOptions(0, 59);
  }

  private _valueIsValid(): boolean {
    if (this._daysControl!.value > 0) return true;
    if (this._hoursControl!.value > 0) return true;
    if (this._minutesControl!.value > 0) return true;
    return false;
  }

  // Utilities

  private _numbersAsOptions(min: number, max: number): CoreInputDropDownOption[] {
    // Edge case protection
    if (min >= max) return [];
    // Logic
    const options: CoreInputDropDownOption[] = [];
    let current = min;
    while (current <= max) {
      options.push({ value: current, label: `${current}` });
      current++;
    }
    return options;
  }

  private _getDescription(): string {
    return `${this._daysControl?.value ?? 0} days, ${this._hoursControl?.value ?? 0} hours and ${this._minutesControl?.value ?? 0} minutes`;
  }
}
