// Angular
import {
  Component,
  computed,
  effect,
  inject,
  input,
  OnD<PERSON>roy,
  OnInit,
  output,
  Signal,
  signal,
  viewChild,
} from '@angular/core';
import { Subscription } from 'rxjs';
// Core UI
import {
  CoreSchedulerDurationEndTime,
  CoreSchedulerDurationTimeSpan,
  CoreSchedulerDurationValue,
} from '../../core-scheduler.interfaces';
import { CoreSchedulerValidityStatus } from '../../core-scheduler.types';
import { CoreSchedulerDurationMode } from '../../core-scheduler.enums';
import { CoreInputRadioSelect, CoreInputRadioSelectComponent, CoreInputRadioSelectOption } from '../../../core-inputs';
import { DurationEndTimeComponent } from './components/duration-end-time/duration-end-time.component';
import { DurationTimeSpanComponent } from './components/duration-time-span/duration-time-span.component';
// Services
import { SchedulerDurationSectionStateService } from './scheduler-duration-section.state.service';
import { CoreSchedulerStateService } from '../../core-scheduler.state.service';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'scheduler-duration-section',
  templateUrl: './scheduler-duration-section.component.html',
  providers: [SchedulerDurationSectionStateService],
  imports: [CoreInputRadioSelectComponent, DurationEndTimeComponent, DurationTimeSpanComponent],
})
export class SchedulerDurationSectionComponent implements OnInit, OnDestroy {
  // Public

  public value = input<CoreSchedulerDurationValue>();
  public timeFormat = input<'hh:mm a' | 'HH:mm'>('hh:mm a');
  public valueChanges = output<void>();

  public getValue(): CoreSchedulerDurationValue {
    return this._getValue();
  }

  public getDescription(): string {
    if (this._modeControl?.value) {
      return this._getDurationDescription();
    } else {
      return 'Duration description not available';
    }
  }

  public showInfiniteOption(): void {
    setTimeout(() => {
      this._modeControl?.setOptions([
        ...this._defaultModeOptions(),
        { value: CoreSchedulerDurationMode.INFINITE, label: 'Infinite' },
      ]);
    });
  }

  public hideInfiniteOption(): void {
    setTimeout(() => {
      this._modeControl?.setOptions(this._defaultModeOptions());
      // Reset value if Infinite was selected previously
      if (this._modeControl?.value === CoreSchedulerDurationMode.INFINITE) {
        this._modeControl.setValue(CoreSchedulerDurationMode.END_TIME);
        this.valueChanges.emit();
      }
    });
  }

  // Internal

  public _modeControl: CoreInputRadioSelect | null = null;
  public _mode = signal<CoreSchedulerDurationMode>(CoreSchedulerDurationMode.END_TIME);

  private _subscriptions$ = new Subscription();
  private _endTimeSectionRef = viewChild(DurationEndTimeComponent);
  private _timeSpanSectionRef = viewChild(DurationTimeSpanComponent);
  private _status: Signal<CoreSchedulerValidityStatus>;
  private _durationState = inject(SchedulerDurationSectionStateService);
  private _schedulerState = inject(CoreSchedulerStateService);

  constructor() {
    this._status = computed(() => {
      switch (this._mode()) {
        case CoreSchedulerDurationMode.ALL_DAY:
        case CoreSchedulerDurationMode.INFINITE:
          return 'VALID';
        case CoreSchedulerDurationMode.END_TIME:
          return this._durationState.endTimeSectionStatus();
        case CoreSchedulerDurationMode.TIME_SPAN:
          return this._durationState.timeSpanSectionStatus();
      }
    });
    // Update duration section status in scheduler state
    effect(() => {
      this._schedulerState.durationSectionStatus.set(this._status());
    });
  }

  public ngOnInit(): void {
    // Declare variables that depend on Input availability
    this._modeControl = new CoreInputRadioSelect({
      label: 'Mode',
      options: this._defaultModeOptions(),
      value: this.value()?.mode ?? CoreSchedulerDurationMode.END_TIME,
    });
    this._mode.set(this._modeControl.value as CoreSchedulerDurationMode);
    // Update mode value on modeControl value changes
    this._subscriptions$.add(
      this._modeControl.valueChanges.subscribe((mode) => {
        this._mode.set(mode as CoreSchedulerDurationMode);
        this.valueChanges.emit();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  // Events

  public _valueChanges(): void {
    this.valueChanges.emit();
  }

  // Helpers

  public _endTimeDurationValue(): CoreSchedulerDurationEndTime | undefined {
    return this.value()?.value as CoreSchedulerDurationEndTime | undefined;
  }

  public _timeSpanDurationValue(): CoreSchedulerDurationTimeSpan | undefined {
    return this.value()?.value as CoreSchedulerDurationTimeSpan | undefined;
  }

  // Private

  private _getValue(): CoreSchedulerDurationValue {
    let value: CoreSchedulerDurationEndTime | CoreSchedulerDurationTimeSpan | null;
    switch (this._mode()) {
      case CoreSchedulerDurationMode.ALL_DAY:
      case CoreSchedulerDurationMode.INFINITE:
        value = null;
        break;
      case CoreSchedulerDurationMode.END_TIME:
        value = this._endTimeSectionRef()!.getValue();
        break;
      case CoreSchedulerDurationMode.TIME_SPAN:
        value = this._timeSpanSectionRef()!.getValue();
        break;
    }
    return {
      mode: this._mode(),
      value,
    };
  }

  private _defaultModeOptions(): CoreInputRadioSelectOption[] {
    return [
      { value: CoreSchedulerDurationMode.END_TIME, label: 'End Time' },
      { value: CoreSchedulerDurationMode.TIME_SPAN, label: 'Time Span' },
      { value: CoreSchedulerDurationMode.ALL_DAY, label: 'All Day' },
    ];
  }

  private _getDurationDescription(): string {
    switch (this._mode()) {
      case CoreSchedulerDurationMode.ALL_DAY:
        return 'Runs all day';
      case CoreSchedulerDurationMode.INFINITE:
        return 'Runs infinitely';
      case CoreSchedulerDurationMode.END_TIME:
        return this._endTimeSectionRef()!.getDescription();
      case CoreSchedulerDurationMode.TIME_SPAN:
        return this._timeSpanSectionRef()!.getDescription();
    }
  }
}
