// Angular
import { Component, input, OnInit, Signal, signal, viewChild, ViewEncapsulation } from '@angular/core';
// Gridstack
import { GridstackComponent, GridstackModule, NgGridStackOptions } from 'gridstack/dist/angular';
// Classes
import { CoreDashboard } from './classes/core-dashboard';
import { CoreDashboardWidget } from './classes/core-dashboard-widget';
// Components
import { CoreDashboardWidgetTextComponent } from './components/core-dashboard-widget-text/core-dashboard-widget-text.component';
import { CoreDashboardWidgetText } from './components/core-dashboard-widget-text/core-dashboard-widget-text';
import { CoreDashboardWidgetHeaderComponent } from './components/core-dashboard-widget-header/core-dashboard-widget-header.component';
import { CoreDashboardWidgetCustomComponent } from './components/core-dashboard-widget-custom/core-dashboard-widget-custom.component';
import { CoreDashboardWidgetCustom } from './components/core-dashboard-widget-custom/core-dashboard-widget-custom';
import { CoreDashboardWidgetTableComponent } from './components/core-dashboard-widget-table/core-dashboard-widget-table.component';
import { CoreDashboardWidgetTable } from './components/core-dashboard-widget-table/core-dashboard-widget-table';
import { CoreDashboardWidgetChart } from './components/core-dashboard-widget-chart/core-dashboard-widget-chart';
import { CoreDashboardWidgetChartComponent } from './components/core-dashboard-widget-chart/core-dashboard-widget-chart.component';
import { CoreTableRow } from '../core-table';

@Component({
  selector: 'core-dashboard',
  templateUrl: './core-dashboard.component.html',
  styleUrl: './core-dashboard.component.scss',
  encapsulation: ViewEncapsulation.None, // IMPORTANT! Disable view encapsulation to allow custom styles to be applied to child components, use with a wrapper tag
  imports: [
    GridstackModule,
    CoreDashboardWidgetTextComponent,
    CoreDashboardWidgetHeaderComponent,
    CoreDashboardWidgetCustomComponent,
    CoreDashboardWidgetTableComponent,
    CoreDashboardWidgetChartComponent,
  ],
})
export class CoreDashboardComponent implements OnInit {
  // [ Inputs ]
  public dashboard = input.required<CoreDashboard>();

  // [ View children ]
  public gridstack = viewChild.required(GridstackComponent);

  // [ Public ]
  public _options!: Signal<NgGridStackOptions>;

  public ngOnInit(): void {
    this._options = signal({
      margin: 5,
      minRow: 1,
      handle: '.widget-header',
      columnOpts: {
        breakpoints: this.dashboard().breakpoints,
        layout: 'compact',
        breakpointForWindow: false,
      },
    });
  }

  // [ Internal ]

  public $asWidgetText(input: CoreDashboardWidget): CoreDashboardWidgetText {
    return input as CoreDashboardWidgetText;
  }

  public $asWidgetCustom(input: CoreDashboardWidget): CoreDashboardWidgetCustom {
    return input as CoreDashboardWidgetCustom;
  }

  public $asWidgetTable<TRow extends CoreTableRow>(input: CoreDashboardWidget): CoreDashboardWidgetTable<TRow> {
    return input as CoreDashboardWidgetTable<TRow>;
  }

  public $asWidgetChart(input: CoreDashboardWidget): CoreDashboardWidgetChart {
    return input as CoreDashboardWidgetChart;
  }
}
