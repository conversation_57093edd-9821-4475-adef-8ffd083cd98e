// Angular
import { AfterViewInit, Component, effect, ElementRef, input, viewChild } from '@angular/core';
// Classes
import { CoreDashboardWidgetChart } from './core-dashboard-widget-chart';
// Chart.js
import { Chart, ChartItem, registerables } from 'chart.js';
Chart.register(...registerables);

@Component({
  selector: 'core-dashboard-widget-chart',
  templateUrl: './core-dashboard-widget-chart.component.html',
  styleUrl: './core-dashboard-widget-chart.component.scss',
  imports: [],
})
export class CoreDashboardWidgetChartComponent implements AfterViewInit {
  // [ Inputs ]
  public widget = input.required<CoreDashboardWidgetChart>();

  // [ View children ]
  public chartCanvas = viewChild.required('chartCanvas', { read: ElementRef });

  // [ Internal ]
  private chart: Chart | null = null;

  constructor() {
    effect(() => {
      const newData = this.widget().data();
      if (this.chart) {
        this.chart.data = newData;
        this.chart.update();
      }
    });
  }

  public ngAfterViewInit(): void {
    this.chart = new Chart(this.chartCanvas().nativeElement as ChartItem, {
      type: this.widget().chartType,
      data: this.widget().data(),
      options: this.widget().options,
    });
  }
}
