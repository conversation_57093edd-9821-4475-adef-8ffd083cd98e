// Angular
import { Component, input } from '@angular/core';
import { SwitchModule } from '@progress/kendo-angular-inputs';
// Core UI
import { CoreTableModule, CoreTableRow } from '../../../core-table';
// Classes
import { CoreDashboardWidgetTable } from './core-dashboard-widget-table';

@Component({
  selector: 'core-dashboard-widget-table',
  templateUrl: './core-dashboard-widget-table.component.html',
  styleUrl: './core-dashboard-widget-table.component.scss',
  imports: [CoreTableModule, SwitchModule],
})
export class CoreDashboardWidgetTableComponent<TRow extends CoreTableRow> {
  public widget = input.required<CoreDashboardWidgetTable<TRow>>();
}
