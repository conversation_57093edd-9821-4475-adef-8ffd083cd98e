import { signal, Signal } from '@angular/core';
import { CoreTable, CoreTableRow } from '../../../core-table';
import { CoreDashboardWidget } from '../../classes/core-dashboard-widget';
import { CoreDashboardWidgetTableOptions } from './core-dashboard-widget-table-options';

export class CoreDashboardWidgetTable<TRow extends CoreTableRow> extends CoreDashboardWidget {
  public table: Signal<CoreTable<TRow>>;

  constructor(options: CoreDashboardWidgetTableOptions<TRow>) {
    super(options);
    this.table = signal(options.table);
  }
}
