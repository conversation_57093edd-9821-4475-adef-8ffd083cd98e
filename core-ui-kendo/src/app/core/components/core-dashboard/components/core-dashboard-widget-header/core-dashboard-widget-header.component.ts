// Angular
import { Component, input } from '@angular/core';
// Kendo UI
import { ButtonModule } from '@progress/kendo-angular-buttons';
import { SVGIconModule } from '@progress/kendo-angular-icons';
import { TooltipDirective } from '@progress/kendo-angular-tooltip';
// Icons
import { xmark } from '../../../core-icon';
import { gearIcon } from '@progress/kendo-svg-icons';
// Classes
import { CoreDashboardWidget } from '../../classes/core-dashboard-widget';

@Component({
  selector: 'core-dashboard-widget-header',
  templateUrl: './core-dashboard-widget-header.component.html',
  styleUrl: './core-dashboard-widget-header.component.scss',
  imports: [ButtonModule, SVGIconModule, TooltipDirective],
})
export class CoreDashboardWidgetHeaderComponent {
  // Inputs
  public widget = input.required<CoreDashboardWidget>();
  // Icons
  public xmark = xmark;
  public gearIcon = gearIcon;
}
