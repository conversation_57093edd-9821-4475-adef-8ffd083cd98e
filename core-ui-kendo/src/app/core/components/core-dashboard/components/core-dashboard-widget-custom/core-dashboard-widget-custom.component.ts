// Angular
import { Component, input, OnInit, Type, viewChild, ViewContainerRef } from '@angular/core';
// Core UI
import { CoreDashboardWidgetCustom } from './core-dashboard-widget-custom';
// Components
// import { ExampleComponent } from '../../../../../main/views/dashboard/views/example/example.component';

@Component({
  selector: 'core-dashboard-widget-custom',
  templateUrl: './core-dashboard-widget-custom.component.html',
  styleUrl: './core-dashboard-widget-custom.component.scss',
})
export class CoreDashboardWidgetCustomComponent<T> implements OnInit {
  // [ Inputs ]
  public widget = input.required<CoreDashboardWidgetCustom>();

  // [ View children ]
  public customContainer = viewChild.required('customContainer', { read: ViewContainerRef });

  // Add your components here
  private _components: Record<string, Type<T>> = {
    // 'app-example': ExampleComponent,
  };

  public ngOnInit(): void {
    const componentSelector = this._components[this.widget().selector];

    if (!componentSelector) {
      return;
    }

    this.customContainer().createComponent(componentSelector);
  }
}
