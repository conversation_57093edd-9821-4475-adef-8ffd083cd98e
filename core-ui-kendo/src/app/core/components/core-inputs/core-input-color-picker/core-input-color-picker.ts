// Angular
import { signal } from '@angular/core';
// Classes
import { CoreInput } from '../shared/classes/core-input';
// Interfaces
import { _CoreInputColorPickerOptions, CoreInputColorPickerOptions } from './core-input-color-picker.interfaces';

export class CoreInpuColorPicker extends CoreInput<string | null> {
  declare public options: _CoreInputColorPickerOptions;

  constructor(options: CoreInputColorPickerOptions) {
    // Call the parent constructor
    super(options.value ?? null, options);

    // Append implementation specific options with default values
    this.options = {
      ...this.options,
      colors: signal(options.colors ?? []),
      type: signal(options.type ?? ['palette', 'gradient']),
      icon: signal(options.icon ?? null),
    };
  }
}
