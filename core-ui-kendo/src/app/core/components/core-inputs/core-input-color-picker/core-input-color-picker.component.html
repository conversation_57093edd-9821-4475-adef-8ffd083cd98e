<div [ngClass]="_labelWrapperClass()">
  <!-- Label (optional)-->
  <div class="label">
    @if (control().options.label()) {
      <kendo-label
        [ngStyle]="{ 'min-width': control().options.labelMinWidth() }"
        [labelCssStyle]="{ 'font-size': '0.9rem' }"
        [for]="input"
        [text]="control().options.label()!"
        [optional]="control().options.optional()"
      />
      @if (this.control().options.showRequiredIndicator() && this.control().options.required()) {
        <span class="required-indicator">*</span>
      }
    }
    <!-- Tooltip Icon-->
    @if (control().options.info()) {
      <div kendoTooltip position="bottom" [titleTemplate]="titleTemplate" [tooltipWidth]="200">
        <kendo-svg-icon [icon]="infoCircleIcon" [title]="getInfoText()" />
      </div>
      <ng-template #titleTemplate> {{ getInfoTitle() }} </ng-template>
    }
  </div>
  <!-- Input -->
  <div class="input-wrapper">
    <kendo-colorpicker
      #input
      [formControl]="control()"
      [initialFocused]="initialFocused()"
      [coreFocusControl]="_inputRef()!"
      [readonly]="control().options.readonly()"
      [attr.required]="control().options.required()"
      [views]="control().options.type()"
      [svgIcon]="control().options.icon()!"
      [gradientSettings]="{ opacity: false }"
      format="hex"
      [paletteSettings]="
        control().options.colors()!.length > 0 ? { palette: control().options.colors()! } : { palette: 'office' }
      "
    />
    <!-- Hint or Error -->
    @if (!control().options.hideMessage()) {
      <div class="input-message">
        <!-- Errors (optional) -->
        @if (control().touched && control().errors) {
          <kendo-formerror>{{ control().errors | errorMessage }}</kendo-formerror>
          <!-- Hint (optional) -->
        } @else if (control().options.hint()) {
          <kendo-formhint>{{ control().options.hint() }}</kendo-formhint>
        }
      </div>
    }
  </div>
</div>
