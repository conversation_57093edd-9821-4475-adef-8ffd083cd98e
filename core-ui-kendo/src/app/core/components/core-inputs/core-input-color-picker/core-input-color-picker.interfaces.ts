// Angular
import { Signal } from '@angular/core';
// Interfaces
import { CoreInputOptions, _CoreInputOptions } from '../shared/interfaces/core-input-options.interface';
import { CoreIcon } from '../../core-icon';
// Types
import { CoreInputColorPickerType } from './core-input-color-picker.types';

export interface CoreInputColorPickerOptions extends CoreInputOptions<string> {
  type?: CoreInputColorPickerType; // Default: ['palette', 'gradient'];
  colors?: string[] | null;
  icon?: CoreIcon;
}

export interface _CoreInputColorPickerOptions extends _CoreInputOptions {
  type: Signal<CoreInputColorPickerType>;
  colors: Signal<string[] | null>;
  icon: Signal<CoreIcon | null>;
}
