// Angular
import { Component, computed, input, On<PERSON><PERSON>roy, OnInit, signal, viewChild, ViewEncapsulation } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { CommonModule } from '@angular/common';
// Kendo UI
import { FormFieldModule, TextBoxComponent, TextBoxModule } from '@progress/kendo-angular-inputs';
import { LabelModule } from '@progress/kendo-angular-label';
import { SVGIconModule } from '@progress/kendo-angular-icons';
import { TooltipsModule } from '@progress/kendo-angular-tooltip';
import { infoCircleIcon } from '@progress/kendo-svg-icons';
// Classes
import { CoreInputCron } from './core-input-cron';
// Pipes
import { ErrorMessagePipe } from '../shared/pipes/error-message.pipe';
// Directives
import { CoreFocusControlDirective } from '../../../directives/core-focus-control.directive';
// Core UI
import { CoreButtonModule } from '../../core-button';
import { CoreInputValidators } from '../shared/classes/core-input-validators';
import { CoreInputDropDown } from '../core-input-drop-down/core-input-drop-down';
import { CoreInputDropDownComponent } from '../core-input-drop-down/core-input-drop-down.component';
// Cronstrue
import cronstrue from 'cronstrue';
// Components
import { DailyModeComponent } from './components/daily-mode/daily-mode.component';
import { WeeklyModeComponent } from './components/weekly-mode/weekly-mode.component';
import { AdvancedModeComponent } from './components/advanced-mode/advanced-mode.component';
import { MonthlyModeComponent } from './components/monthly-mode/monthly-mode.component';
import { YearlyModeComponent } from './components/yearly-mode/yearly-mode.component';
// Enums
import { CronMode } from './core-input-cron.enums';

@Component({
  selector: 'core-input-cron',
  templateUrl: './core-input-cron.component.html',
  styleUrls: ['./core-input-cron.component.scss', '../shared/styles/core-input.styles.scss'],
  encapsulation: ViewEncapsulation.None, // IMPORTANT! Disable view encapsulation to allow custom styles to be applied to child components, use with a wrapper tag
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormFieldModule,
    LabelModule,
    TextBoxModule,
    ErrorMessagePipe,
    CoreFocusControlDirective,
    SVGIconModule,
    TooltipsModule,
    CoreButtonModule,
    CoreInputDropDownComponent,
    FormsModule,
    DailyModeComponent,
    WeeklyModeComponent,
    AdvancedModeComponent,
    MonthlyModeComponent,
    YearlyModeComponent,
  ],
})
export class CoreInputCronComponent implements OnInit, OnDestroy {
  // Public
  public cronValue = signal<string>('');
  public selectedMode = signal<string>('');
  public monthlyMode = signal<'DaysOfMonth' | 'DaysOfWeek'>('DaysOfMonth');
  public yearlyMode = signal<'DaysOfMonth' | 'DaysOfWeek'>('DaysOfMonth');
  public isUpdateCron = signal<boolean>(true);
  public modeControl!: CoreInputDropDown;
  public isDisabled = signal<boolean>(false);
  // [ Inputs ]
  public control = input.required<CoreInputCron>();
  public initialFocused = input<boolean>(false);
  // Icon
  public infoCircleIcon = infoCircleIcon;

  // [ View children ]
  public _inputRef = viewChild<TextBoxComponent>('input');

  // [ Computed ]

  public _labelWrapperClass = computed(() => {
    return this.control().options.labelPosition() === 'top'
      ? 'core-input-text-label-on-top'
      : 'core-input-text-label-on-left';
  });

  // [ Internal ]

  private _subscription$ = new Subscription();

  public ngOnInit(): void {
    this.loadModeControl();
    this.updateDisabledState();

    if (this.control().value) {
      this.cronValue.set(this.control().value as string);
      this.selectedMode.set(this.getOptimalModeBasedOnCron());
      this.modeControl.setValue(this.selectedMode());
    } else {
      this.selectedMode.set(CronMode.DAILY);
    }

    this._subscription$.add(
      this.control().valueChanges.subscribe((value: string | null) => {
        if (this.control().disabled !== this.isDisabled()) {
          this.updateDisabledState();
        }
        this.control().onValueChange(value) as void;
        this.cronValue.set(value ?? '');
        if (value) {
          this.isUpdateCron.set(false);
          this.selectedMode.set(this.getOptimalModeBasedOnCron());
          this.modeControl.setValue(this.selectedMode());
          this.isUpdateCron.set(true);
        }
      }),
    );
  }

  public loadModeControl(): void {
    this.modeControl = new CoreInputDropDown({
      label: 'Mode',
      showRequiredIndicator: true,
      value: this.control().value ? this.getOptimalModeBasedOnCron() : CronMode.DAILY,
      options: this.control().getModeOptions(),
      onValueChange: (value) => {
        this.selectedMode.set(value as string);
        if (this.isUpdateCron()) {
          this.cronValue.set('');
        }
      },
      validators: [CoreInputValidators.required('Mode is required field')],
    });
  }

  public cronTextHint = computed(() => {
    const cron = this.cronValue();
    if (!cron) return '';

    try {
      const response = cronstrue.toString(cron, { use24HourTimeFormat: true });
      return this.selectedMode() === 'Daily'
        ? `Every day ${response.charAt(0).toLowerCase()}${response.slice(1)}`
        : response;
    } catch {
      return 'Invalid cron expression';
    }
  });

  // [ Events ]

  public getInfoText(): string {
    const info = this.control().options.info();
    return typeof info === 'string' ? info : (info?.text ?? '');
  }

  public getInfoTitle(): string {
    const info = this.control().options.info();
    return typeof info === 'string' ? '' : (info?.title ?? '');
  }

  public onValueChanges(cron: string): void {
    this.cronValue.set(cron);
  }

  public changeMode(value: boolean): void {
    if (value) {
      this.isUpdateCron.set(false);
      this.selectedMode.set(CronMode.ADVANCED);
      this.modeControl.setValue(CronMode.ADVANCED);
      this.isUpdateCron.set(true);
    }
  }

  public onStatusChanges(value: { isValid: boolean }): void {
    if (!value.isValid) {
      this.control().setErrors({ invalid: true });
    } else {
      this.control().setErrors(null);
    }
  }

  private getOptimalModeBasedOnCron(): CronMode {
    const cronSegments = this.cronValue().split(' ');

    if (cronSegments.length < 5) {
      return CronMode.ADVANCED;
    }

    if (this.isCronYearlyFormat(cronSegments)) {
      const dayOfMonth = cronSegments[2];
      if (dayOfMonth.includes(',') || dayOfMonth.includes('-') || dayOfMonth.includes('/')) {
        return CronMode.ADVANCED;
      }
      this.yearlyMode.set(this.getYearlyMode(cronSegments));
      return CronMode.YEARLY;
    }
    if (this.isCronWeeklyFormat(cronSegments)) return CronMode.WEEKLY;

    if (this.isCronMonthlyFormat(cronSegments)) {
      // Check if the "day of month" segment contains multiple values (e.g., "1,15").
      const dayOfMonth = cronSegments[2];
      if (dayOfMonth.includes(',') || dayOfMonth.includes('-') || dayOfMonth.includes('/')) {
        return CronMode.ADVANCED;
      }
      this.monthlyMode.set(this.getMonthlyMode(cronSegments));
      return CronMode.MONTHLY;
    }
    if (this.isCronWeeklyFormat(cronSegments)) return CronMode.WEEKLY;
    if (this.isCronDailyFormat(cronSegments)) return CronMode.DAILY;
    if (this.isValidCron()) return CronMode.ADVANCED;

    // Default return to satisfy the return type
    return CronMode.ADVANCED;
  }

  // [ Private ]

  private isCronDailyFormat(cronSegments: string[]): boolean {
    return (
      this.hasTimeSpecified(cronSegments) &&
      !cronSegments[0].includes(',') &&
      !cronSegments[1].includes(',') &&
      !this.hasDaysSpecified(cronSegments) &&
      !this.hasMonthsSpecified(cronSegments) &&
      !this.hasMonthOptionSpecified(cronSegments)
    );
  }

  private isCronWeeklyFormat(cronSegments: string[]): boolean {
    return (
      this.hasTimeSpecified(cronSegments) &&
      this.hasDaysSpecified(cronSegments) &&
      !this.hasMonthsSpecified(cronSegments) &&
      !this.hasMonthOptionSpecified(cronSegments) &&
      !this.hasHashSymbol(cronSegments[4])
    );
  }

  private isCronMonthlyFormat(cronSegments: string[]): boolean {
    return (
      this.hasTimeSpecified(cronSegments) &&
      !this.hasMonthsSpecified(cronSegments) &&
      (this.hasDaysSpecified(cronSegments) || this.hasMonthOptionSpecified(cronSegments))
    );
  }

  private isCronYearlyFormat(cronSegments: string[]): boolean {
    return (
      this.hasTimeSpecified(cronSegments) &&
      this.hasMonthsSpecified(cronSegments) &&
      (this.hasMonthOptionSpecified(cronSegments) || this.hasHashSymbol(cronSegments[4]))
    );
  }

  private hasTimeSpecified(cronSegments: string[]): boolean {
    return !this.isEmptySegment(cronSegments[0]) && !this.isEmptySegment(cronSegments[1]);
  }

  private hasDaysSpecified(cronSegments: string[]): boolean {
    return !this.isEmptySegment(cronSegments[4]);
  }

  private hasMonthOptionSpecified(cronSegments: string[]): boolean {
    return !this.isEmptySegment(cronSegments[2]);
  }

  private hasMonthsSpecified(cronSegments: string[]): boolean {
    return !this.isEmptySegment(cronSegments[3]);
  }

  private isEmptySegment(seg: string): boolean {
    return seg === '*';
  }

  private hasHashSymbol(segment: string | undefined): boolean {
    return segment ? segment.includes('#') : false;
  }

  private getMonthlyMode(cronSegments: string[]): 'DaysOfMonth' | 'DaysOfWeek' {
    const dayOfMonth = cronSegments[2];
    const dayOfWeek = cronSegments[4];

    if (dayOfWeek !== '*' && this.hasHashSymbol(dayOfWeek)) {
      return 'DaysOfWeek';
    }

    if (dayOfMonth !== '*') {
      return 'DaysOfMonth';
    }

    if (dayOfWeek !== '*') {
      return 'DaysOfWeek';
    }

    return 'DaysOfMonth';
  }

  private getYearlyMode(cronSegments: string[]): 'DaysOfMonth' | 'DaysOfWeek' {
    const dayOfMonth = cronSegments[2];
    const dayOfWeek = cronSegments[4];

    if (dayOfWeek !== '*' && this.hasHashSymbol(dayOfWeek)) {
      return 'DaysOfWeek';
    }

    if (dayOfMonth !== '*') {
      return 'DaysOfMonth';
    }
    return 'DaysOfMonth';
  }

  private isValidCron(): boolean {
    const regex = new RegExp(
      /^((((\d+,)+\d+|(\d+(\/|-|#)\d+)|\d+L?|\*(\/\d+)?|L(-\d+)?|\?|[A-Z]{3}(-[A-Z]{3})?) ?){5,7})$/,
      'g',
    );
    return regex.test(this.cronValue());
  }

  private updateDisabledState(): void {
    if (this.control().disabled) {
      this.modeControl.disable();
      this.isDisabled.set(true);
    } else {
      this.modeControl.enable();
      this.isDisabled.set(false);
    }
  }

  public ngOnDestroy(): void {
    this._subscription$.unsubscribe();
  }
}
