/* eslint-disable @angular-eslint/no-output-on-prefix */
// Angular
import { Component, effect, input, model, OnInit, output, signal } from '@angular/core';
// Luxon
import { DateTime } from 'luxon';
// Core UI
import { CoreInputValidators } from '../../../shared/classes/core-input-validators';
import { CoreInputTime } from '../../../core-input-time/core-input-time';
import { CoreInputTimeComponent } from '../../../core-input-time/core-input-time.component';

@Component({
  selector: 'app-daily-mode',
  templateUrl: './daily-mode.component.html',
  imports: [CoreInputTimeComponent],
})
export class DailyModeComponent implements OnInit {
  // [ Inputs ]
  public cron = model.required<string>();
  public isDisabled = input<boolean>(false);
  // [ Outputs ]
  public onValueChanges = output<string>();
  public onStatusChanges = output<{ isValid: boolean }>();
  // Signals
  public _isValid = signal<boolean>(false);

  constructor() {
    effect(() => {
      const disabled = this.isDisabled();
      if (disabled) {
        this.timeControl.disable();
      } else {
        this.timeControl.enable();
      }
    });
  }

  public ngOnInit(): void {
    if (this.cron().length) {
      this.setFormValuesFromCron();
    }
    this.updateValidity();
  }

  public timeControl = new CoreInputTime({
    label: 'Time',
    showRequiredIndicator: true,
    onValueChange: () => this.onTimeValueChanges(),
    validators: [CoreInputValidators.required('Time is required')],
  });

  public onTimeValueChanges(): void {
    this.updateValidity();
    this.updateCronBasedOnFormValues();
  }

  public getCronValue(): string {
    const selectedTime = DateTime.fromJSDate(this.timeControl.value as Date);
    const min: number = selectedTime.minute;
    const hour: number = selectedTime.hour;
    return `${min} ${hour} * * *`;
  }

  private updateCronBasedOnFormValues(): void {
    this.cron.set(this.getCronValue());
    this.onValueChanges.emit(this.cron());
  }

  private setFormValuesFromCron(): void {
    try {
      const cronSegments = this.cron().split(' ');
      const min = +cronSegments[0];
      const hour = +cronSegments[1];
      this.timeControl.setValue(DateTime.now().set({ minute: min, hour: hour }).toJSDate());
    } catch {
      this.timeControl.setValue(new Date());
    }

    this.updateCronBasedOnFormValues();
  }

  private updateValidity(): void {
    const timeIsValid = DateTime.fromJSDate(this.timeControl.value as Date).isValid;
    const previousStatus = this._isValid();
    this._isValid.set(timeIsValid);
    if (previousStatus !== this._isValid()) {
      this.onStatusChanges.emit({ isValid: this._isValid() });
    }
  }
}
