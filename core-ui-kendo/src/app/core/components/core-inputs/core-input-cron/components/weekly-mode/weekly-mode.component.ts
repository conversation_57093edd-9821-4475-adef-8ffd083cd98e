/* eslint-disable @angular-eslint/no-output-on-prefix */
// Angular
import { Component, effect, input, model, OnInit, output, signal } from '@angular/core';
// Enums
import { DayOfWeek } from './weekly-mode.enums';
// Core UI
import { CoreInputValidators } from '../../../shared/classes/core-input-validators';
import { CoreInputMultiselect } from '../../../core-input-multiselect/core-input-multiselect';
import { CoreInputMultiselectOption } from '../../../core-input-multiselect/core-input-multiselect.interfaces';
import { CoreInputMultiselectComponent } from '../../../core-input-multiselect/core-input-multiselect.component';
import { CoreInputTimeComponent } from '../../../core-input-time/core-input-time.component';
import { CoreInputTime } from '../../../core-input-time/core-input-time';
// Luxon
import { DateTime } from 'luxon';

@Component({
  selector: 'app-weekly-mode',
  templateUrl: './weekly-mode.component.html',
  imports: [CoreInputMultiselectComponent, CoreInputTimeComponent],
})
export class WeeklyModeComponent implements OnInit {
  // [ Inputs ]
  public cron = model.required<string>();
  public isDisabled = input<boolean>(false);
  // [ Outputs ]
  public onValueChanges = output<string>();
  public onStatusChanges = output<{ isValid: boolean }>();
  public changeMode = output<boolean>();
  // Signals
  public _isValid = signal<boolean>(false);

  // React to changes in the disabled signal
  constructor() {
    effect(() => {
      const disabled = this.isDisabled();
      if (disabled) {
        this.timeControl.disable();
        this.dayOfWeekControl.disable();
      } else {
        this.timeControl.enable();
        this.dayOfWeekControl.enable();
      }
    });
  }

  public ngOnInit(): void {
    if (this.cron().length) {
      this.setFormValuesFromCron(true);
    }
    this.updateValidity();
  }

  public dayOfWeekControl = new CoreInputMultiselect({
    label: 'Days of the Week',
    checkboxes: true,
    showRequiredIndicator: true,
    options: this.getDaysOptions(),
    onValueChange: () => this.onValueChange(),
    validators: [CoreInputValidators.required('Day of the Week is required')],
  });

  public timeControl = new CoreInputTime({
    label: 'Time',
    showRequiredIndicator: true,
    onValueChange: () => this.onValueChange(),
    validators: [CoreInputValidators.required('Time is required')],
  });

  public onValueChange(): void {
    this.updateValidity();
    this.updateCronBasedOnFormValues();
  }

  public getCronValue(): string {
    const selectedTime = DateTime.fromJSDate(this.timeControl.value as Date);
    const min: number = selectedTime.minute;
    const hour: number = selectedTime.hour;
    const days: number[] = this.dayOfWeekControl.value as number[];
    return `${min} ${hour} * * ${days.join(',') || '*'}`;
  }

  private setFormValuesFromCron(changeMode = false): void {
    try {
      const cronSegments = this.cron().split(' ');
      const min: number = +cronSegments[0];
      const hour: number = +cronSegments[1];
      const days: number[] = cronSegments[4].split(',').map((d) => +d);
      this.timeControl.setValue(DateTime.now().set({ minute: min, hour: hour }).toJSDate());
      // Validate days of the week (0-6)
      if (days.some((day) => isNaN(day) || day < 0 || day > 6)) {
        throw new Error('Invalid day of the week values');
      }
      this.dayOfWeekControl.setValue(days);
      this.updateCronBasedOnFormValues();
    } catch {
      if (changeMode) {
        this.changeMode.emit(true);
      }
    }
  }

  private updateCronBasedOnFormValues(): void {
    this.cron.set(this.getCronValue());
    this.onValueChanges.emit(this.cron());
  }

  private updateValidity(): void {
    const timeIsValid = DateTime.fromJSDate(this.timeControl.value as Date).isValid;
    const daysAreValid = ((this.dayOfWeekControl.value as number[]) || []).length > 0;
    const previousStatus = this._isValid();
    this._isValid.set(daysAreValid && timeIsValid);
    if (previousStatus !== this._isValid()) {
      this.onStatusChanges.emit({ isValid: this._isValid() });
    }
  }

  private getDaysOptions(): CoreInputMultiselectOption[] {
    return [
      { value: DayOfWeek.MONDAY, label: 'Monday' },
      { value: DayOfWeek.TUESDAY, label: 'Tuesday' },
      { value: DayOfWeek.WEDNESDAY, label: 'Wednesday' },
      { value: DayOfWeek.THURSDAY, label: 'Thursday' },
      { value: DayOfWeek.FRIDAY, label: 'Friday' },
      { value: DayOfWeek.SATURDAY, label: 'Saturday' },
      { value: DayOfWeek.SUNDAY, label: 'Sunday' },
    ];
  }
}
