/* eslint-disable @angular-eslint/no-output-on-prefix */
// Angular
import { Component, effect, input, model, OnInit, output, signal } from '@angular/core';
// Core UI
import { CoreInputDropDown } from '../../../../../core-input-drop-down/core-input-drop-down';
import { CoreInputValidators } from '../../../../../shared/classes/core-input-validators';
import { CoreInputDropDownOption } from '../../../../../core-input-drop-down/core-input-drop-down.interfaces';
import { CoreInputTime } from '../../../../../core-input-time/core-input-time';
import { CoreInputDropDownComponent } from '../../../../../core-input-drop-down/core-input-drop-down.component';
import { CoreInputTimeComponent } from '../../../../../core-input-time/core-input-time.component';
// Luxon
import { DateTime } from 'luxon';

@Component({
  selector: 'app-monthly-day-of-week-mode',
  templateUrl: './monthly-day-of-week-mode.component.html',
  imports: [CoreInputDropDownComponent, CoreInputTimeComponent],
})
export class MonthlyDayOfWeekModeComponent implements OnInit {
  public cron = model.required<string>();
  public isDisabled = input<boolean>(false);
  // [ Outputs ]
  public onValueChanges = output<string>();
  public onStatusChanges = output<{ isValid: boolean }>();
  public changeMode = output<boolean>();
  // Signals
  public _isValid = signal<boolean>(false);

  constructor() {
    // React to changes in the disabled input
    effect(() => {
      const disabled = this.isDisabled();
      if (disabled) {
        this._weekControl.disable();
        this._dayControl.disable();
        this.timeControl.disable();
      } else {
        this._weekControl.enable();
        this._dayControl.enable();
        this.timeControl.enable();
      }
    });
  }

  public ngOnInit(): void {
    if (this.cron().length) {
      this.setFormValuesFromCron(true);
    }
    this.updateValidity();
  }

  public _weekControl = new CoreInputDropDown({
    label: 'On the',
    showRequiredIndicator: true,
    options: this._getWeeksOptions(),
    onValueChange: () => this.onValueChange(),
    validators: [CoreInputValidators.required('Required field')],
  });

  public _dayControl = new CoreInputDropDown({
    label: 'Day',
    showRequiredIndicator: true,
    options: this._getDaysOptions(),
    onValueChange: () => this.onValueChange(),
    validators: [CoreInputValidators.required('Required field')],
  });

  public timeControl = new CoreInputTime({
    label: 'Time',
    showRequiredIndicator: true,
    onValueChange: () => this.onValueChange(),
    validators: [CoreInputValidators.required('Time is required')],
  });

  public onValueChange(): void {
    this.updateValidity();
    this.updateCronBasedOnFormValues();
  }

  public getCronValue(): string {
    const selectedTime = DateTime.fromJSDate(this.timeControl.value as Date);
    const min: number = selectedTime.minute;
    const hour: number = selectedTime.hour;
    const week = this._weekControl.value as number | string;
    const day = this._dayControl.value as number;
    return `${min} ${hour} * * ${day}#${week}`;
  }

  private setFormValuesFromCron(changeMode = false): void {
    try {
      const cronSegments = this.cron().split(' ');
      const min: number = +cronSegments[0];
      const hour: number = +cronSegments[1];
      const daySegment = cronSegments[4].split('#');

      // Validate day and week segments
      if (daySegment.length !== 2) {
        throw new Error('Invalid day or week format');
      }
      const day: number = +daySegment[0];
      const week: string | number = daySegment[1];

      // Validate day of the week (0-6)
      if (isNaN(day) || day < 0 || day > 6) {
        throw new Error('Invalid day of the week value');
      }

      if (cronSegments[2] === '?') {
        throw new Error('Invalid day of the month value');
      }
      // Validate week (1-5 or 'L')
      if (!isNaN(+week) && (+week < 1 || +week > 5) && week !== 'L') {
        throw new Error('Invalid week value');
      }
      // Set values to controls
      this.timeControl.setValue(DateTime.now().set({ minute: min, hour: hour }).toJSDate());
      this._dayControl.setValue(day);
      if (week === 'L') {
        this._weekControl.setValue('L');
      } else {
        this._weekControl.setValue(+week);
      }
      this.updateCronBasedOnFormValues();
    } catch {
      // Handle errors and optionally emit changeMode
      if (changeMode) {
        this.changeMode.emit(true);
      }
    }
  }

  private updateCronBasedOnFormValues(): void {
    this.cron.set(this.getCronValue());
    this.onValueChanges.emit(this.cron());
  }

  private updateValidity(): void {
    const weekOptionIsValid = this._weekControl.value != null;
    const dayOptionIsValid = this._dayControl.value != null;
    const timeIsValid = DateTime.fromJSDate(this.timeControl.value as Date).isValid;
    const previousStatus = this._isValid();
    this._isValid.set(weekOptionIsValid && timeIsValid && dayOptionIsValid);

    if (previousStatus !== this._isValid()) {
      this.onStatusChanges.emit({ isValid: this._isValid() });
    }
  }

  private _getWeeksOptions(): CoreInputDropDownOption[] {
    return [
      { value: 1, label: '1st' },
      { value: 2, label: '2nd' },
      { value: 3, label: '3rd' },
      { value: 4, label: '4th' },
      { value: 5, label: '5th' },
      { value: 'L', label: 'Last' },
    ];
  }

  private _getDaysOptions(): CoreInputDropDownOption[] {
    return [
      { value: 0, label: 'Sunday' },
      { value: 1, label: 'Monday' },
      { value: 2, label: 'Tuesday' },
      { value: 3, label: 'Wednesday' },
      { value: 4, label: 'Thursday' },
      { value: 5, label: 'Friday' },
      { value: 6, label: 'Saturday' },
    ];
  }
}
