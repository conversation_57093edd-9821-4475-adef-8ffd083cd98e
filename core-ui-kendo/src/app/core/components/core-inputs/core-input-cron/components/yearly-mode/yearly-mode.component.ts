// Angular
import { Component, effect, model, OnInit, output, signal } from '@angular/core';
// Core UI
import { CoreInputDropDownComponent } from '../../../core-input-drop-down/core-input-drop-down.component';
import { CoreInputDropDown } from '../../../core-input-drop-down/core-input-drop-down';
import { CoreInputValidators } from '../../../shared/classes/core-input-validators';
// Components
import { YearlyDayOfMonthModeComponent } from './components/yearly-day-of-month-mode/yearly-day-of-month-mode.component';
import { YearlyDayOfWeekModeComponent } from './components/yearly-day-of-week-mode/yearly-day-of-week-mode.component';

@Component({
  selector: 'app-yearly-mode',
  templateUrl: './yearly-mode.component.html',
  imports: [CoreInputDropDownComponent, YearlyDayOfMonthModeComponent, YearlyDayOfWeekModeComponent],
})
export class YearlyModeComponent implements OnInit {
  // [ Inputs ]
  public cron = model.required<string>();
  public yearlyMode = model.required<string | null>();
  public isDisabled = model<boolean>(false);
  // [ Outputs ]
  public valueChanges = output<string>();
  public statusChanges = output<{ isValid: boolean }>();
  public changeMode = output<boolean>();
  // Signals
  public _modeControl = signal<CoreInputDropDown | null>(null);

  constructor() {
    // React to changes in the disabled input
    effect(() => {
      const disabled = this.isDisabled();
      if (disabled) {
        this._modeControl()?.disable();
      } else {
        this._modeControl()?.enable();
      }
    });
  }

  public ngOnInit(): void {
    this._modeControl.set(
      new CoreInputDropDown({
        label: 'Recurrence',
        showRequiredIndicator: true,

        options: [
          { label: 'Day of the month', value: 'DaysOfMonth' },
          { label: 'Day of the week', value: 'DaysOfWeek' },
        ],
        validators: [CoreInputValidators.required('Required field')],
        value: this.yearlyMode() ?? 'DaysOfMonth',
        onValueChange: (value) => {
          this.yearlyMode.set(value as string);
          this.cron.set('');
          this.valueChanges.emit('');
        },
      }),
    );
  }
}
