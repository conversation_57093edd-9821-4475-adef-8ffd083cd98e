/* eslint-disable @angular-eslint/no-output-on-prefix */
// Angular
import { Component, input, OnInit, output, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
// Kendo UI
import { TextBoxModule } from '@progress/kendo-angular-inputs';

@Component({
  selector: 'app-advanced-mode',
  templateUrl: './advanced-mode.component.html',
  imports: [TextBoxModule, FormsModule],
})
export class AdvancedModeComponent implements OnInit {
  // [ Inputs ]
  public cron = input.required<string>();
  public isDisabled = input<boolean>(false);
  // [ Outputs ]
  public onValueChanges = output<string>();
  public onStatusChanges = output<{ isValid: boolean }>();
  // Signals
  public cronVAlue = signal<string>('');
  public _isValid = signal<boolean>(false);

  public ngOnInit(): void {
    this.cronVAlue.set(this.cron());
    this.updateValidity();
  }

  public onValueChange(): void {
    this.onValueChanges.emit(this.cronVAlue());
    this.updateValidity();
  }

  private updateValidity(): void {
    const regex = new RegExp(
      /^((((\d+,)+\d+|(\d+(\/|-|#)\d+)|\d+L?|\*(\/\d+)?|L(-\d+)?|\?|[A-Z]{3}(-[A-Z]{3})?) ?){5,7})$/,
      'g',
    );
    const previousStatus = this._isValid();
    this._isValid.set(regex.test(this.cron()));
    if (previousStatus !== this._isValid()) {
      this.onStatusChanges.emit({ isValid: this._isValid() });
    }
  }
}
