// Angular
import { Component, effect, input, model, OnInit, output, signal } from '@angular/core';
// Core UI
import { CoreInputDropDownComponent } from '../../../../../core-input-drop-down/core-input-drop-down.component';
import { CoreInputTimeComponent } from '../../../../../core-input-time/core-input-time.component';
import { CoreInputTime } from '../../../../../core-input-time/core-input-time';
import { CoreInputDropDown } from '../../../../../core-input-drop-down/core-input-drop-down';
import { CoreInputValidators } from '../../../../../shared/classes/core-input-validators';
import { CoreInputDropDownOption } from '../../../../../core-input-drop-down/core-input-drop-down.interfaces';
// Luxon
import { DateTime } from 'luxon';

@Component({
  selector: 'app-yearly-day-of-week-mode',
  templateUrl: './yearly-day-of-week-mode.component.html',
  imports: [CoreInputDropDownComponent, CoreInputTimeComponent],
})
export class YearlyDayOfWeekModeComponent implements OnInit {
  public cron = model.required<string>();
  public disabled = input<boolean>();
  // [ Outputs ]
  public valueChanges = output<string>();
  public statusChanges = output<{ isValid: boolean }>();
  public changeMode = output<boolean>();
  // Signals
  public _isValid = signal<boolean>(false);

  constructor() {
    // React to changes in the disabled input
    effect(() => {
      const disabled = this.disabled();
      if (disabled) {
        this._weekControl.disable();
        this.weekDayControl.disable();
        this.monthControl.disable();
        this.timeControl.disable();
      } else {
        this._weekControl.enable();
        this.weekDayControl.enable();
        this.monthControl.enable();
        this.timeControl.enable();
      }
    });
  }

  public ngOnInit(): void {
    if (this.cron().length) {
      this.setFormValuesFromCron(true);
    }
    this.updateValidity();
  }

  public _weekControl = new CoreInputDropDown({
    label: 'On the',
    showRequiredIndicator: true,
    options: this._getWeeksOptions(),
    onValueChange: () => this.onValueChange(),
    validators: [CoreInputValidators.required('Required field')],
  });

  public weekDayControl = new CoreInputDropDown({
    label: 'Day',
    showRequiredIndicator: true,
    options: this._getDaysOptions(),
    onValueChange: () => this.onValueChange(),
    validators: [CoreInputValidators.required('Required field')],
  });

  public monthControl = new CoreInputDropDown({
    label: 'Month',
    showRequiredIndicator: true,
    options: this._getMonthsOptions(),
    onValueChange: () => this.onValueChange(),
    validators: [CoreInputValidators.required('Required field')],
  });

  public timeControl = new CoreInputTime({
    label: 'Time',
    showRequiredIndicator: true,
    onValueChange: () => this.onValueChange(),
    validators: [CoreInputValidators.required('Time is required')],
  });

  public onValueChange(): void {
    this.updateValidity();
    this.updateCronBasedOnFormValues();
  }

  public getCronValue(): string {
    const selectedTime = DateTime.fromJSDate(this.timeControl.value as Date);
    const min: number = selectedTime.minute;
    const hour: number = selectedTime.hour;
    const dayOfWeek = this.weekDayControl.value as number | string;
    const week = this._weekControl.value as number | string;
    const month = this.monthControl.value as number;

    return `${min} ${hour} * ${month || '*'} ${dayOfWeek}#${week}`;
  }

  private setFormValuesFromCron(changeMode = false): void {
    try {
      const cronSegments = this.cron().split(' ');
      const min: number = +cronSegments[0];
      const hour: number = +cronSegments[1];
      const month: string = cronSegments[3];
      const [dayOfWeek, week] = cronSegments[4].split('#');
      const monthValue = Number(month);
      const dayOfWeekValue = Number(dayOfWeek);

      if (isNaN(monthValue) || monthValue < 1 || monthValue > 12) {
        throw new Error('Invalid month value');
      }
      if (isNaN(dayOfWeekValue) || dayOfWeekValue < 0 || dayOfWeekValue > 6) {
        throw new Error('Invalid day of the week value');
      }
      if (week !== 'L' && (isNaN(Number(week)) || +week < 1 || +week > 5)) {
        throw new Error('Invalid week value');
      }
      this.timeControl.setValue(DateTime.now().set({ minute: min, hour: hour }).toJSDate());
      this.monthControl.setValue(monthValue);
      this.weekDayControl.setValue(dayOfWeekValue);
      this._weekControl.setValue(week === 'L' ? 'L' : Number(week));
      this.updateCronBasedOnFormValues();
    } catch {
      // Handle errors and optionally emit changeMode
      if (changeMode) {
        this.changeMode.emit(true);
      }
    }
  }

  private updateCronBasedOnFormValues(): void {
    this.cron.set(this.getCronValue());
    this.valueChanges.emit(this.cron());
  }

  private updateValidity(): void {
    // Check if all controls have valid values
    const isMonthDayValid = this.weekDayControl.value != null;
    const isMonthValid = this.monthControl.value != null;
    const isTimeValid = DateTime.fromJSDate(this.timeControl.value as Date).isValid;
    const isWeekValid = this._weekControl.value != null;

    // Determine overall validity
    const isValid = isMonthDayValid && isMonthValid && isTimeValid && isWeekValid;

    // Update validity status only if it has changed
    if (this._isValid() !== isValid) {
      this._isValid.set(isValid);
      this.statusChanges.emit({ isValid });
    }
  }

  private _getMonthsOptions(): CoreInputDropDownOption[] {
    return [
      { value: 1, label: 'January' },
      { value: 2, label: 'February' },
      { value: 3, label: 'March' },
      { value: 4, label: 'April' },
      { value: 5, label: 'May' },
      { value: 6, label: 'June' },
      { value: 7, label: 'July' },
      { value: 8, label: 'August' },
      { value: 9, label: 'September' },
      { value: 10, label: 'October' },
      { value: 11, label: 'November' },
      { value: 12, label: 'December' },
    ];
  }

  private _getDaysOptions(): CoreInputDropDownOption[] {
    return [
      { value: 0, label: 'Sunday' },
      { value: 1, label: 'Monday' },
      { value: 2, label: 'Tuesday' },
      { value: 3, label: 'Wednesday' },
      { value: 4, label: 'Thursday' },
      { value: 5, label: 'Friday' },
      { value: 6, label: 'Saturday' },
    ];
  }

  private _getWeeksOptions(): CoreInputDropDownOption[] {
    return [
      { value: 1, label: '1st' },
      { value: 2, label: '2nd' },
      { value: 3, label: '3rd' },
      { value: 4, label: '4th' },
      { value: 5, label: '5th' },
      { value: 'L', label: 'Last' },
    ];
  }
}
