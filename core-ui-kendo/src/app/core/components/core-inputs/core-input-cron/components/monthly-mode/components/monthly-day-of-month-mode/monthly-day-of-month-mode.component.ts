/* eslint-disable @angular-eslint/no-output-on-prefix */
// Angular
import { Component, effect, input, model, OnInit, output, signal } from '@angular/core';
// Core UI
import { CoreInputValidators } from '../../../../../shared/classes/core-input-validators';
import { CoreInputDropDownComponent } from '../../../../../core-input-drop-down/core-input-drop-down.component';
import { CoreInputDropDown } from '../../../../../core-input-drop-down/core-input-drop-down';
import { CoreInputTimeComponent } from '../../../../../core-input-time/core-input-time.component';
import { CoreInputTime } from '../../../../../core-input-time/core-input-time';
import { CoreInputDropDownOption } from '../../../../../core-input-drop-down/core-input-drop-down.interfaces';
// Luxon
import { DateTime } from 'luxon';

@Component({
  selector: 'app-monthly-day-of-month-mode',
  templateUrl: './monthly-day-of-month-mode.component.html',
  imports: [CoreInputDropDownComponent, CoreInputTimeComponent],
})
export class MonthlyDayOfMonthModeComponent implements OnInit {
  public cron = model.required<string>();
  public isDisabled = input<boolean>(false);
  // [ Outputs ]
  public onValueChanges = output<string>();
  public onStatusChanges = output<{ isValid: boolean }>();
  public changeMode = output<boolean>();
  // Signals
  public _isValid = signal<boolean>(false);

  constructor() {
    // React to changes in the disabled input
    effect(() => {
      const disabled = this.isDisabled();
      if (disabled) {
        this.monthDaysControl.disable();
        this.timeControl.disable();
      } else {
        this.monthDaysControl.enable();
        this.timeControl.enable();
      }
    });
  }

  public ngOnInit(): void {
    if (this.cron().length) {
      this.setFormValuesFromCron(true);
    }
    this.updateValidity();
  }

  public monthDaysControl = new CoreInputDropDown({
    label: 'On the',
    showRequiredIndicator: true,
    options: this._getMonthDaysOptions(),
    onValueChange: () => this.onValueChange(),
    validators: [CoreInputValidators.required('Required field')],
  });

  public timeControl = new CoreInputTime({
    label: 'Time',
    showRequiredIndicator: true,
    onValueChange: () => this.onValueChange(),
    validators: [CoreInputValidators.required('Time is required')],
  });

  public onValueChange(): void {
    this.updateCronBasedOnFormValues();
    this.updateValidity();
  }

  public getCronValue(): string {
    const selectedTime = DateTime.fromJSDate(this.timeControl.value as Date);
    const min: number = selectedTime.minute;
    const hour: number = selectedTime.hour;
    const monthDay = this.monthDaysControl.value as number | string;
    return `${min} ${hour} ${monthDay || '*'} * *`;
  }

  private setFormValuesFromCron(changeMode = false): void {
    try {
      const cronSegments = this.cron().split(' ');
      const min: number = +cronSegments[0];
      const hour: number = +cronSegments[1];
      const monthDay: string = cronSegments[2];
      this.timeControl.setValue(DateTime.now().set({ minute: min, hour: hour }).toJSDate());
      if (monthDay === 'L') {
        this.monthDaysControl.setValue('L');
      } else if (!isNaN(Number(monthDay))) {
        this.monthDaysControl.setValue(Number(monthDay));
      } else {
        throw new Error(`Invalid day of month: ${monthDay}`);
      }
      this.updateCronBasedOnFormValues();
    } catch {
      if (changeMode) {
        this.changeMode.emit(true);
      }
    }
  }

  private updateCronBasedOnFormValues(): void {
    this.cron.set(this.getCronValue());
    this.onValueChanges.emit(this.cron());
  }

  private updateValidity(): void {
    const monthOptionIsValid = this.monthDaysControl.value != null;
    const timeIsValid = DateTime.fromJSDate(this.timeControl.value as Date).isValid;
    const previousStatus = this._isValid();
    this._isValid.set(monthOptionIsValid && timeIsValid);
    if (previousStatus !== this._isValid()) {
      this.onStatusChanges.emit({ isValid: this._isValid() });
    }
  }

  private _getMonthDaysOptions(): CoreInputDropDownOption[] {
    return [
      { value: 1, label: '1st' },
      { value: 2, label: '2nd' },
      { value: 3, label: '3rd' },
      { value: 4, label: '4th' },
      { value: 5, label: '5th' },
      { value: 6, label: '6th' },
      { value: 7, label: '7th' },
      { value: 8, label: '8th' },
      { value: 9, label: '9th' },
      { value: 10, label: '10th' },
      { value: 11, label: '11th' },
      { value: 12, label: '12th' },
      { value: 13, label: '13th' },
      { value: 14, label: '14th' },
      { value: 15, label: '15th' },
      { value: 16, label: '16th' },
      { value: 17, label: '17th' },
      { value: 18, label: '18th' },
      { value: 19, label: '19th' },
      { value: 20, label: '20th' },
      { value: 21, label: '21st' },
      { value: 22, label: '22nd' },
      { value: 23, label: '23rd' },
      { value: 24, label: '24th' },
      { value: 25, label: '25th' },
      { value: 26, label: '26th' },
      { value: 27, label: '27th' },
      { value: 28, label: '28th' },
      { value: 29, label: '29th' },
      { value: 30, label: '30th' },
      { value: 31, label: '31st' },
      { value: 'L', label: 'Last' },
    ];
  }
}
