// Angular
import { Component, effect, model, OnInit, output, signal } from '@angular/core';
// Core UI
import { CoreInputDropDown } from '../../../../../core-input-drop-down/core-input-drop-down';
import { CoreInputDropDownOption } from '../../../../../core-input-drop-down/core-input-drop-down.interfaces';
import { CoreInputTime } from '../../../../../core-input-time/core-input-time';
import { CoreInputValidators } from '../../../../../shared/classes/core-input-validators';
import { CoreInputDropDownComponent } from '../../../../../core-input-drop-down/core-input-drop-down.component';
import { CoreInputTimeComponent } from '../../../../../core-input-time/core-input-time.component';
// Luxon
import { DateTime } from 'luxon';

@Component({
  selector: 'app-yearly-day-of-month-mode',
  templateUrl: './yearly-day-of-month-mode.component.html',
  imports: [CoreInputDropDownComponent, CoreInputTimeComponent],
})
export class YearlyDayOfMonthModeComponent implements OnInit {
  public cron = model.required<string>();
  public disabled = model.required<boolean>();
  // [ Outputs ]
  public valueChanges = output<string>();
  public statusChanges = output<{ isValid: boolean }>();
  public changeMode = output<boolean>();
  // Signals
  public _isValid = signal<boolean>(false);

  constructor() {
    // React to changes in the disabled input
    effect(() => {
      const disabled = this.disabled();
      if (disabled) {
        this.monthDayControl.disable();
        this.monthControl.disable();
        this.timeControl.disable();
      } else {
        this.monthDayControl.enable();
        this.monthControl.enable();
        this.timeControl.enable();
      }
    });
  }

  public ngOnInit(): void {
    if (this.cron().length) {
      this.setFormValuesFromCron(true);
    }
    this.updateValidity();
  }

  public monthDayControl = new CoreInputDropDown({
    label: 'On the',
    showRequiredIndicator: true,
    options: this._getMonthDaysOptions(),
    onValueChange: () => this.onValueChange(),
    validators: [CoreInputValidators.required('Required field')],
  });

  public monthControl = new CoreInputDropDown({
    label: 'Month',
    showRequiredIndicator: true,
    options: this._getMonthsOptions(),
    onValueChange: () => this.onValueChange(),
    validators: [CoreInputValidators.required('Required field')],
  });

  public timeControl = new CoreInputTime({
    label: 'Time',
    showRequiredIndicator: true,
    onValueChange: () => this.onValueChange(),
    validators: [CoreInputValidators.required('Time is required')],
  });

  public onValueChange(): void {
    this.updateValidity();
    this.updateCronBasedOnFormValues();
  }

  public getCronValue(): string {
    const selectedTime = DateTime.fromJSDate(this.timeControl.value as Date);
    const min: number = selectedTime.minute;
    const hour: number = selectedTime.hour;
    const monthDay = this.monthDayControl.value as number | string;
    const month = this.monthControl.value as number;

    return `${min} ${hour} ${monthDay || '*'} ${month || '*'} *`;
  }

  private setFormValuesFromCron(changeMode = false): void {
    try {
      const cronSegments = this.cron().split(' ');
      const min: number = +cronSegments[0];
      const hour: number = +cronSegments[1];
      const monthDay: string = cronSegments[2];
      const month: string = cronSegments[3];
      // Validate monthDay
      if (monthDay !== 'L' && (isNaN(Number(monthDay)) || +monthDay < 1 || +monthDay > 31)) {
        throw new Error('Invalid month day value');
      }
      // Validate month
      if (isNaN(Number(month)) || +month < 1 || +month > 12) {
        throw new Error('Invalid month value');
      }
      this.timeControl.setValue(DateTime.now().set({ minute: min, hour: hour }).toJSDate());
      if (monthDay === 'L') {
        this.monthDayControl.setValue('L');
      } else {
        this.monthDayControl.setValue(Number(monthDay));
      }
      this.monthControl.setValue(Number(month));
      this.updateCronBasedOnFormValues();
    } catch {
      // Handle errors and optionally emit changeMode
      if (changeMode) {
        this.changeMode.emit(true);
      }
    }
  }

  private updateCronBasedOnFormValues(): void {
    this.cron.set(this.getCronValue());
    this.valueChanges.emit(this.cron());
  }

  private updateValidity(): void {
    const monthDayOptionIsValid = this.monthDayControl.value != null;
    const monthOptionIsValid = this.monthControl.value != null;
    const timeIsValid = DateTime.fromJSDate(this.timeControl.value as Date).isValid;
    const previousStatus = this._isValid();
    this._isValid.set(monthOptionIsValid && timeIsValid && monthDayOptionIsValid);

    if (previousStatus !== this._isValid()) {
      this.statusChanges.emit({ isValid: this._isValid() });
    }
  }

  private _getMonthsOptions(): CoreInputDropDownOption[] {
    return [
      { value: 1, label: 'January' },
      { value: 2, label: 'February' },
      { value: 3, label: 'March' },
      { value: 4, label: 'April' },
      { value: 5, label: 'May' },
      { value: 6, label: 'June' },
      { value: 7, label: 'July' },
      { value: 8, label: 'August' },
      { value: 9, label: 'September' },
      { value: 10, label: 'October' },
      { value: 11, label: 'November' },
      { value: 12, label: 'December' },
    ];
  }

  private _getMonthDaysOptions(): CoreInputDropDownOption[] {
    return [
      { value: 1, label: '1st' },
      { value: 2, label: '2nd' },
      { value: 3, label: '3rd' },
      { value: 4, label: '4th' },
      { value: 5, label: '5th' },
      { value: 6, label: '6th' },
      { value: 7, label: '7th' },
      { value: 8, label: '8th' },
      { value: 9, label: '9th' },
      { value: 10, label: '10th' },
      { value: 11, label: '11th' },
      { value: 12, label: '12th' },
      { value: 13, label: '13th' },
      { value: 14, label: '14th' },
      { value: 15, label: '15th' },
      { value: 16, label: '16th' },
      { value: 17, label: '17th' },
      { value: 18, label: '18th' },
      { value: 19, label: '19th' },
      { value: 20, label: '20th' },
      { value: 21, label: '21st' },
      { value: 22, label: '22nd' },
      { value: 23, label: '23rd' },
      { value: 24, label: '24th' },
      { value: 25, label: '25th' },
      { value: 26, label: '26th' },
      { value: 27, label: '27th' },
      { value: 28, label: '28th' },
      { value: 29, label: '29th' },
      { value: 30, label: '30th' },
      { value: 31, label: '31st' },
      { value: 'L', label: 'Last' },
    ];
  }
}
