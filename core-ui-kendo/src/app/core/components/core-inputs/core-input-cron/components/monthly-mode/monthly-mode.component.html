<!-- Mode Control -->
@if (_modeControl()) {
  <core-input-drop-down [control]="_modeControl()!" />
}
@switch (monthlyMode()) {
  @case ("DaysOfMonth") {
    <app-monthly-day-of-month-mode
      [cron]="cron()"
      [isDisabled]="isDisabled()"
      (onStatusChanges)="onStatusChanges.emit($event)"
      (changeMode)="changeMode.emit($event)"
      (onValueChanges)="onValueChanges.emit($event)"
    />
  }
  @case ("DaysOfWeek") {
    <app-monthly-day-of-week-mode
      [cron]="cron()"
      [isDisabled]="isDisabled()"
      (onStatusChanges)="onStatusChanges.emit($event)"
      (changeMode)="changeMode.emit($event)"
      (onValueChanges)="onValueChanges.emit($event)"
    />
  }
}
