<!-- Mode Control -->
@if (_modeControl()) {
  <core-input-drop-down [control]="_modeControl()!" />
}
@switch (yearlyMode()) {
  @case ("DaysOfMonth") {
    <app-yearly-day-of-month-mode
      [cron]="cron()"
      [disabled]="isDisabled()"
      (statusChanges)="statusChanges.emit($event)"
      (changeMode)="changeMode.emit($event)"
      (valueChanges)="valueChanges.emit($event)"
    />
  }
  @case ("DaysOfWeek") {
    <app-yearly-day-of-week-mode
      [cron]="cron()"
      [disabled]="isDisabled()"
      (statusChanges)="statusChanges.emit($event)"
      (changeMode)="changeMode.emit($event)"
      (valueChanges)="valueChanges.emit($event)"
    />
  }
}
