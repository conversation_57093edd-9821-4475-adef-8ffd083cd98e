/* eslint-disable @angular-eslint/no-output-on-prefix */
// Angular
import { Component, effect, input, model, OnInit, output, signal } from '@angular/core';
// Core UI
import { CoreInputDropDown } from '../../../core-input-drop-down/core-input-drop-down';
import { CoreInputValidators } from '../../../shared/classes/core-input-validators';
import { CoreInputDropDownComponent } from '../../../core-input-drop-down/core-input-drop-down.component';
// Components
import { MonthlyDayOfMonthModeComponent } from './components/monthly-day-of-month-mode/monthly-day-of-month-mode.component';
import { MonthlyDayOfWeekModeComponent } from './components/monthly-day-of-week-mode/monthly-day-of-week-mode.component';

@Component({
  selector: 'app-monthly-mode',
  templateUrl: './monthly-mode.component.html',
  imports: [MonthlyDayOfMonthModeComponent, CoreInputDropDownComponent, MonthlyDayOfWeekModeComponent],
})
export class MonthlyModeComponent implements OnInit {
  // [ Inputs ]
  public cron = model.required<string>();
  public monthlyMode = model.required<string | null>();
  public isDisabled = input<boolean>(false);
  // [ Outputs ]
  public onValueChanges = output<string>();
  public onStatusChanges = output<{ isValid: boolean }>();
  public changeMode = output<boolean>();
  // Signals
  public _modeControl = signal<CoreInputDropDown | null>(null);

  constructor() {
    // React to changes in the disabled input
    effect(() => {
      const disabled = this.isDisabled();
      if (disabled) {
        this._modeControl()?.disable();
      } else {
        this._modeControl()?.enable();
      }
    });
  }

  public ngOnInit(): void {
    this._modeControl.set(
      new CoreInputDropDown({
        label: 'Recurrence',
        showRequiredIndicator: true,
        options: [
          { label: 'Day of the month', value: 'DaysOfMonth' },
          { label: 'Day of the week', value: 'DaysOfWeek' },
        ],
        validators: [CoreInputValidators.required('Required field')],
        value: this.monthlyMode() ?? 'DaysOfMonth',
        onValueChange: (value) => {
          this.monthlyMode.set(value as string);
          this.cron.set('');
          this.onValueChanges.emit('');
        },
      }),
    );
  }
}
