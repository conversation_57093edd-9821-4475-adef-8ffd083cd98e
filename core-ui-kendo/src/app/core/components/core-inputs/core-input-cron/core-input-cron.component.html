<div [ngClass]="_labelWrapperClass()">
  <!-- Label (optional)-->
  <div class="label">
    @if (control().options.label()) {
      <kendo-label
        [ngStyle]="{ 'min-width': control().options.labelMinWidth() }"
        [labelCssStyle]="{ 'font-size': '0.9rem' }"
        [for]="input"
        [text]="control().options.label()!"
        [optional]="control().options.optional()"
      />
      @if (this.control().options.showRequiredIndicator() && this.control().options.required()) {
        <span class="required-indicator">*</span>
      }
    }
    <!-- Tooltip Icon-->
    @if (control().options.info()) {
      <div kendoTooltip position="bottom" [titleTemplate]="titleTemplate" [tooltipWidth]="200">
        <kendo-svg-icon [icon]="infoCircleIcon" [title]="getInfoText()" />
      </div>
      <ng-template #titleTemplate> {{ getInfoTitle() }} </ng-template>
    }
  </div>
  <!-- Mode -->
  <core-input-drop-down [control]="modeControl" />

  @switch (selectedMode()) {
    @case ("Daily") {
      <app-daily-mode
        [cron]="cronValue()"
        [isDisabled]="isDisabled()"
        (onStatusChanges)="onStatusChanges($event)"
        (onValueChanges)="onValueChanges($event)"
      />
    }
    @case ("Weekly") {
      <app-weekly-mode
        [cron]="cronValue()"
        [isDisabled]="isDisabled()"
        (onStatusChanges)="onStatusChanges($event)"
        (changeMode)="changeMode($event)"
        (onValueChanges)="onValueChanges($event)"
      />
    }
    @case ("Monthly") {
      <app-monthly-mode
        [cron]="cronValue()"
        [isDisabled]="isDisabled()"
        [monthlyMode]="monthlyMode()"
        (onStatusChanges)="onStatusChanges($event)"
        (changeMode)="changeMode($event)"
        (onValueChanges)="onValueChanges($event)"
      />
    }
    @case ("Yearly") {
      <app-yearly-mode
        [cron]="cronValue()"
        [isDisabled]="isDisabled()"
        [yearlyMode]="yearlyMode()"
        (statusChanges)="onStatusChanges($event)"
        (changeMode)="changeMode($event)"
        (valueChanges)="onValueChanges($event)"
      />
    }
    @case ("Advanced") {
      <app-advanced-mode
        [cron]="cronValue()"
        [isDisabled]="isDisabled()"
        (onStatusChanges)="onStatusChanges($event)"
        (onValueChanges)="onValueChanges($event)"
      />
    }
  }
  <!-- Input -->
  <div class="input-wrapper">
    <kendo-textbox
      #_inputRef
      #input
      [formControl]="control()"
      [(value)]="cronValue"
      [coreFocusControl]="_inputRef"
      [attr.required]="control().options.required()"
      [style]="{ display: 'none' }"
    />
    <!-- Hint (optional) -->
    <kendo-formhint
      >{{ cronTextHint() }}
      @if (cronTextHint() !== "Invalid cron expression" && cronValue().length > 0) {
        ({{ cronValue() }})
      }
    </kendo-formhint>
    <!-- Errors (optional) -->
    @if (control().touched && control().errors) {
      <kendo-formerror>{{ control().errors | errorMessage }}</kendo-formerror>
    }
  </div>
</div>
