// Angular
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
// Core UI
import { CoreInputTextComponent } from './core-input-text.component';
import { CoreInputsModule } from '../core-inputs.module';
import { CoreInputText } from './core-input-text';
import { CoreInputValidators } from '../shared/classes/core-input-validators';

describe('Core Input Text Component', () => {
  let component: CoreInputTextComponent;
  let fixture: ComponentFixture<CoreInputTextComponent>;
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CoreInputsModule],
    }).compileComponents();

    fixture = TestBed.createComponent(CoreInputTextComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    fixture.componentRef.setInput('control', new CoreInputText({ label: 'Test' }));
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });

  it('should display the provided label', () => {
    fixture.componentRef.setInput('control', new CoreInputText({ label: 'Test' }));
    fixture.detectChanges();
    const labelElement = fixture.debugElement.query(By.css('.k-label'));
    expect((labelElement.nativeElement as HTMLElement).textContent).toContain('Test');
  });

  it('should display the provided label on top', () => {
    fixture.componentRef.setInput('control', new CoreInputText({ label: 'Test', labelPosition: 'top' }));
    fixture.detectChanges();
    const labelElement = fixture.debugElement.query(By.css('.k-label'));
    expect(fixture.debugElement.query(By.css('.core-input-text-label-on-top'))).toBeTruthy();
    expect((labelElement.nativeElement as HTMLElement).textContent).toContain('Test');
  });

  it('should display the provided label on left', () => {
    fixture.componentRef.setInput('control', new CoreInputText({ label: 'Test', labelPosition: 'left' }));
    fixture.detectChanges();
    const labelElement = fixture.debugElement.query(By.css('.k-label'));
    expect(fixture.debugElement.query(By.css('.core-input-text-label-on-left'))).toBeTruthy();
    expect((labelElement.nativeElement as HTMLElement).textContent).toContain('Test');
  });

  it('should display the provided placeholder', () => {
    fixture.componentRef.setInput('control', new CoreInputText({ label: 'Test', placeholder: 'Test Placeholder' }));
    fixture.detectChanges();
    const textBox = fixture.debugElement.query(By.css('kendo-textbox'));
    const inputRef = textBox.query(By.css('input'));
    expect((inputRef.nativeElement as HTMLInputElement).placeholder).toBe('Test Placeholder');
  });

  it('should apply the specified mask', () => {
    fixture.componentRef.setInput('control', new CoreInputText({ label: 'Test', mask: '(###) 000-0000' }));
    fixture.detectChanges();
    const textBox = fixture.debugElement.query(By.css('kendo-textbox'));
    const inputRef = textBox.query(By.css('input'));
    expect((inputRef.nativeElement as HTMLInputElement).getAttribute('mask')).toBe('(###) 000-0000');
  });

  it('should apply min-width to label', () => {
    fixture.componentRef.setInput('control', new CoreInputText({ label: 'Test', labelMinWidth: '100px' }));
    fixture.detectChanges();
    const labelElement = fixture.debugElement.query(By.css('kendo-label'));
    expect(labelElement.styles['min-width']).toBe('100px');
  });

  it('should display optional next to label if set to true', () => {
    fixture.componentRef.setInput('control', new CoreInputText({ label: 'Test', optional: true }));
    fixture.detectChanges();
    const labelElement = fixture.debugElement.query(By.css('kendo-label'));
    expect((labelElement.nativeElement as HTMLElement).textContent).toContain('(Optional)');
  });

  it('should make input required and show indicator if required validator is provided and showRequiredIndicator is set to true', () => {
    fixture.componentRef.setInput(
      'control',
      new CoreInputText({
        label: 'Test',
        validators: [CoreInputValidators.required('Required field')],
        showRequiredIndicator: true,
      }),
    );
    fixture.detectChanges();
    const textBox = fixture.debugElement.query(By.css('kendo-textbox'));
    const inputRef = textBox.query(By.css('input'));
    const indicator = fixture.debugElement.query(By.css('.required-indicator'));
    expect((inputRef.nativeElement as HTMLInputElement).required).toBe(true);
    expect(indicator).toBeTruthy();
  });

  it('should show tooltip with correct title and text if info is provided', () => {
    fixture.componentRef.setInput(
      'control',
      new CoreInputText({
        label: 'Test',
        info: { text: 'Tooltip Text', title: 'Tooltip Title' },
      }),
    );
    fixture.detectChanges();
    const tooltipElement = fixture.debugElement.query(By.css('div[kendoTooltip]'));
    const tooltipIcon = fixture.debugElement.query(By.css('kendo-svg-icon'));
    // Verify that tooltip elements are present
    expect(tooltipElement).toBeTruthy();
    expect(tooltipIcon).toBeTruthy();
    // Verify that the component methods return the correct values
    const component = fixture.componentInstance;
    expect(component.getInfoTitle()).toBe('Tooltip Title');
    expect(component.getInfoText()).toBe('Tooltip Text');
  });

  describe('Accessibility', () => {
    it('should have correct aria-label for input', () => {
      fixture.componentRef.setInput('control', new CoreInputText({ label: 'Test' }));
      fixture.detectChanges();
      const textBox = fixture.debugElement.query(By.css('kendo-textbox'));
      const inputRef = textBox.query(By.css('input'));
      expect((inputRef.nativeElement as HTMLInputElement).getAttribute('aria-label')).toBe('Test');
    });

    it('should have correct aria-required for input if required validator is provided', () => {
      fixture.componentRef.setInput(
        'control',
        new CoreInputText({
          label: 'Test',
          validators: [CoreInputValidators.required('Required field')],
        }),
      );
      fixture.detectChanges();
      const textBox = fixture.debugElement.query(By.css('kendo-textbox'));
      const inputRef = textBox.query(By.css('input'));
      expect((inputRef.nativeElement as HTMLInputElement).getAttribute('aria-required')).toBe('true');
    });

    it('should be focused if initialFocused is set to true', () => {
      fixture.componentRef.setInput('control', new CoreInputText({ label: 'Test' }));
      fixture.componentRef.setInput('initialFocused', true);
      fixture.detectChanges();
      const textBox = fixture.debugElement.query(By.css('kendo-textbox'));
      const inputRef = textBox.query(By.css('input'));
      console.log(document.activeElement)
      expect(document.activeElement).toBe(inputRef.nativeElement as HTMLElement);
    });
  });
});
