<div [ngClass]="_labelWrapperClass()">
  <!-- Label (optional)-->
  <div class="label">
    @if (control().options.label()) {
      <kendo-label
        [ngStyle]="{ 'min-width': control().options.labelMinWidth() }"
        [labelCssStyle]="{ 'font-size': '0.9rem' }"
        [for]="input"
        [text]="control().options.label()!"
        [optional]="control().options.optional()"
      />
      @if (this.control().options.showRequiredIndicator() && this.control().options.required()) {
        <span class="required-indicator">*</span>
      }
    }
    <!-- Tooltip Icon-->
    @if (control().options.info()) {
      <div kendoTooltip position="bottom" [titleTemplate]="titleTemplate" [tooltipWidth]="200">
        <kendo-svg-icon [icon]="infoCircleIcon" [title]="getInfoText()" />
      </div>
      <ng-template #titleTemplate>
        <div class="tooltip-title">{{ getInfoTitle() }}</div>
      </ng-template>
    }
  </div>
  <!-- Input -->
  <div class="input-wrapper">
    <kendo-textbox
      #input
      [formControl]="control()"
      [coreFocusControl]="_inputRef()!"
      [initialFocused]="initialFocused()"
      [placeholder]="control().options.placeholder()!"
      [readonly]="control().options.readonly()"
      [style]="{ width: '100%' }"
      [inputAttributes]="{
        inputmode: control().options.inputMode()!,
        mask: control().options.mask()!,
        'aria-label': control().options.label()!,
        'aria-required': control().options.required() ? 'true' : 'false',
      }"
      (keydown.enter)="control().onSpecialKeyPressed.next('enter')"
    />
    <!-- Hint or Error -->
    @if (!control().options.hideMessage()) {
      <div class="input-message">
        <!-- Errors (optional) -->
        @if (control().touched && control().errors) {
          <kendo-formerror>{{ control().errors | errorMessage }}</kendo-formerror>
          <!-- Hint (optional) -->
        } @else if (control().options.hint()) {
          <kendo-formhint>{{ control().options.hint() }}</kendo-formhint>
        }
      </div>
    }
  </div>
</div>
