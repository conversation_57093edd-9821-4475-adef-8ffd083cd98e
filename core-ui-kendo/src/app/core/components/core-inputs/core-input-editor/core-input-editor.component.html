<div [ngClass]="_labelWrapperClass()" [ngStyle]="control().options.ngStyle()">
  <!-- Label (optional)-->
  <div class="label">
    @if (control().options.label()) {
      <kendo-label
        [ngStyle]="{ 'min-width': control().options.labelMinWidth() }"
        [labelCssStyle]="{ 'font-size': '0.9rem' }"
        [for]="input"
        [text]="control().options.label()!"
        [optional]="control().options.optional()"
      />
      @if (this.control().options.showRequiredIndicator() && this.control().options.required()) {
        <span class="required-indicator">*</span>
      }
    }
    <!-- Tooltip Icon-->
    @if (control().options.info()) {
      <div kendoTooltip position="bottom" [titleTemplate]="titleTemplate" [tooltipWidth]="200">
        <kendo-svg-icon [icon]="infoCircleIcon" [title]="getInfoText()" />
      </div>
      <ng-template #titleTemplate> {{ getInfoTitle() }} </ng-template>
    }
  </div>
  <!-- Input -->

  <div class="input-wrapper">
    <kendo-editor
      #input
      kendoTooltip
      [formControl]="control()"
      [coreFocusControl]="_inputRef()!"
      [initialFocused]="initialFocused()"
      [readonly]="control().options.readonly()"
      [attr.required]="control().options.required()"
      [style]="{ width: '100%', height: '100%', color: 'var(--text)' }"
      [iframe]="false"
    >
      <kendo-toolbar [overflow]="true">
        <kendo-toolbar-buttongroup>
          <!-- Font Format Buttons -->
          <!-- Bold button -->
          <kendo-toolbar-button kendoEditorBoldButton></kendo-toolbar-button>
          <!-- Italic button -->
          <kendo-toolbar-button kendoEditorItalicButton></kendo-toolbar-button>
          <!-- Underline button -->
          <kendo-toolbar-button kendoEditorUnderlineButton></kendo-toolbar-button>
        </kendo-toolbar-buttongroup>

        <!-- Format Dropdown -->
        <kendo-toolbar-dropdownlist kendoEditorFormat></kendo-toolbar-dropdownlist>

        <!-- Text Alignment Buttons -->
        <kendo-toolbar-buttongroup>
          <!-- Align Left button -->
          <kendo-toolbar-button kendoEditorAlignLeftButton></kendo-toolbar-button>
          <!-- Align Center button -->
          <kendo-toolbar-button kendoEditorAlignCenterButton></kendo-toolbar-button>
          <!-- Align Right button -->
          <kendo-toolbar-button kendoEditorAlignRightButton></kendo-toolbar-button>
          <!-- Justify button -->
          <kendo-toolbar-button kendoEditorAlignJustifyButton></kendo-toolbar-button>
        </kendo-toolbar-buttongroup>

        <!-- List Formatting Buttons -->
        <kendo-toolbar-buttongroup>
          <!-- Insert Unordered List button -->
          <kendo-toolbar-button kendoEditorInsertUnorderedListButton></kendo-toolbar-button>
          <!-- Insert Ordered List button -->
          <kendo-toolbar-button kendoEditorInsertOrderedListButton></kendo-toolbar-button>
          <!-- Indent button -->
          <kendo-toolbar-button kendoEditorIndentButton></kendo-toolbar-button>
          <!-- Outdent button -->
          <kendo-toolbar-button kendoEditorOutdentButton></kendo-toolbar-button>
        </kendo-toolbar-buttongroup>

        <!-- Link Buttons -->
        <kendo-toolbar-buttongroup>
          <!-- Create Link button -->
          <kendo-toolbar-button kendoEditorCreateLinkButton></kendo-toolbar-button>
          <!-- Unlink button -->
          <kendo-toolbar-button kendoEditorUnlinkButton></kendo-toolbar-button>
        </kendo-toolbar-buttongroup>

        <!-- Insert Image Button -->
        <kendo-toolbar-button kendoEditorInsertImageButton></kendo-toolbar-button>
        <!-- Text Color -->
        <kendo-toolbar-colorpicker kendoEditorForeColor></kendo-toolbar-colorpicker>

        <!-- Dropdown Button for Adding Text -->
        @if (control().options.insertOptions().length > 0) {
          <kendo-toolbar-dropdownbutton
            textField="label"
            [svgIcon]="clipboardTextIcon"
            [data]="control().options.insertOptions()"
            (itemClick)="input.exec('insertText', $event)"
          />
        }
      </kendo-toolbar>
    </kendo-editor>

    <!-- Hint or Error -->
    @if (!control().options.hideMessage()) {
      <div class="input-message">
        <!-- Errors (optional) -->
        @if (control().touched && control().errors) {
          <kendo-formerror>{{ control().errors | errorMessage }}</kendo-formerror>
          <!-- Hint (optional) -->
        } @else if (control().options.hint()) {
          <kendo-formhint>{{ control().options.hint() }}</kendo-formhint>
        }
      </div>
    }
  </div>
</div>
