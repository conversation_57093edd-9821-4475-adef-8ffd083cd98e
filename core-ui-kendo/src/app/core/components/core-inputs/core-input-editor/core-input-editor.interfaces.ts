import { WritableSignal } from '@angular/core';
import { CoreInputOptions, _CoreInputOptions } from '../shared/interfaces/core-input-options.interface';

export interface CoreInputEditorOptions extends CoreInputOptions<string> {
  insertOptions?: CoreInputEditorInsertOption[];
  ngStyle?: Record<string, string>;
}

export interface _CoreInputEditorOptions extends _CoreInputOptions {
  insertOptions: WritableSignal<CoreInputEditorInsertOption[]>;
  ngStyle: WritableSignal<Record<string, string>>;
}

export interface CoreInputEditorInsertOption {
  label: string;
  text: string;
}
