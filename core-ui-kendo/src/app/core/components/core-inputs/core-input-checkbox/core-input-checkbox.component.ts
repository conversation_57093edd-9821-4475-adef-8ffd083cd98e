// Angular
import { Component, input, On<PERSON><PERSON>roy, OnInit, viewChild } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
// Kendo UI
import { CheckBoxComponent, CheckBoxModule, FormFieldModule } from '@progress/kendo-angular-inputs';
import { LabelModule } from '@progress/kendo-angular-label';
import { SVGIconModule } from '@progress/kendo-angular-icons';
import { TooltipsModule } from '@progress/kendo-angular-tooltip';
// Classes
import { CoreInputCheckbox } from './core-input-checkbox';
// Pipes
import { ErrorMessagePipe } from '../shared/pipes/error-message.pipe';
// Directives
import { CoreFocusControlDirective } from '../../../directives/core-focus-control.directive';
// Icon
import { infoCircleIcon } from '@progress/kendo-svg-icons';

@Component({
  selector: 'core-input-checkbox',
  templateUrl: './core-input-checkbox.component.html',
  styleUrls: ['../shared/styles/core-input.styles.scss', './core-input-checkbox.component.scss'],
  imports: [
    ReactiveFormsModule,
    FormFieldModule,
    LabelModule,
    CheckBoxModule,
    ErrorMessagePipe,
    CoreFocusControlDirective,
    SVGIconModule,
    TooltipsModule,
  ],
})
export class CoreInputCheckboxComponent implements OnInit, OnDestroy {
  // [ Inputs ]

  public control = input.required<CoreInputCheckbox>();
  public initialFocused = input<boolean>(false);
  // Icon
  public infoCircleIcon = infoCircleIcon;

  // [ View children ]
  // Note: Needed for core-focus-control.directive.ts
  public _inputRef = viewChild<CheckBoxComponent>('input');

  // [ Internal ]

  private _subscription$ = new Subscription();

  public ngOnInit(): void {
    this._subscription$.add(
      this.control().valueChanges.subscribe((value: boolean | null) => {
        this.control().onValueChange(value) as void;
      }),
    );
  }

  public getInfoText(): string {
    const info = this.control().options.info();
    return typeof info === 'string' ? info : (info?.text ?? '');
  }

  public getInfoTitle(): string {
    const info = this.control().options.info();
    return typeof info === 'string' ? '' : (info?.title ?? '');
  }

  public ngOnDestroy(): void {
    this._subscription$.unsubscribe();
  }
}
