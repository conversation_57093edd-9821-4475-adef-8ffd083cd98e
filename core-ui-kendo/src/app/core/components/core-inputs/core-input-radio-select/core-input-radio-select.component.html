<div [ngClass]="_labelWrapperClass()">
  <!-- Label (optional)-->
  <div class="label">
    @if (control().options.label()) {
      <kendo-label
        [ngStyle]="{ 'min-width': control().options.labelMinWidth() }"
        [labelCssStyle]="{ 'font-size': '0.9rem' }"
        [text]="control().options.label()!"
        [optional]="control().options.optional()"
      />
      @if (this.control().options.showRequiredIndicator() && this.control().options.required()) {
        <span class="required-indicator">*</span>
      }
    }
    <!-- Tooltip Icon-->
    @if (control().options.info()) {
      <div kendoTooltip position="bottom" [titleTemplate]="titleTemplate" [tooltipWidth]="200">
        <kendo-svg-icon [icon]="infoCircleIcon" [title]="getInfoText()" />
      </div>
      <ng-template #titleTemplate> {{ getInfoTitle() }} </ng-template>
    }
  </div>
  <!-- Options -->
  <div class="options" [class.column]="control().options.direction() === 'vertical'">
    <!-- Option Wrapper -->
    @for (option of control().options.options(); track option.value) {
      <div class="option">
        <kendo-radiobutton
          #options
          kendoTooltip
          [id]="option.value"
          [formControl]="control()"
          [value]="option.value"
          [attr.required]="control().options.required()"
        ></kendo-radiobutton>
        <kendo-label class="k-radio-label" [for]="options" [text]="option.label"></kendo-label>
      </div>
    }
  </div>
  <!-- Hint or Error -->
  @if (!control().options.hideMessage()) {
    <div class="input-message">
      <!-- Errors (optional) -->
      @if (control().touched && control().errors) {
        <kendo-formerror>{{ control().errors | errorMessage }}</kendo-formerror>
        <!-- Hint (optional) -->
      } @else if (control().options.hint()) {
        <kendo-formhint>{{ control().options.hint() }}</kendo-formhint>
      }
    </div>
  }
</div>
