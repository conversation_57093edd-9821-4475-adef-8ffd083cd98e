<div [ngClass]="_labelWrapperClass()">
  <!-- Label (optional)-->
  <div class="label">
    @if (control().options.label()) {
      <kendo-label
        [ngStyle]="{ 'min-width': control().options.labelMinWidth() }"
        [labelCssStyle]="{ 'font-size': '0.9rem' }"
        [for]="input"
        [text]="control().options.label()!"
        [optional]="control().options.optional()"
      />
      @if (this.control().options.showRequiredIndicator() && this.control().options.required()) {
        <span class="required-indicator">*</span>
      }
    }
    <!-- Tooltip Icon-->
    @if (control().options.info()) {
      <div kendoTooltip position="bottom" [titleTemplate]="titleTemplate" [tooltipWidth]="200">
        <kendo-svg-icon [icon]="infoCircleIcon" [title]="getInfoText()" />
      </div>
      <ng-template #titleTemplate> {{ getInfoTitle() }} </ng-template>
    }
  </div>
  <!-- Input -->
  <div class="input-wrapper">
    <kendo-numerictextbox
      #input
      kendoTooltip
      [formControl]="control()"
      [coreFocusControl]="_inputRef()!"
      [initialFocused]="initialFocused()"
      [autoCorrect]="true"
      [format]="control().options.format()"
      [min]="control().options.min()!"
      [max]="control().options.max()!"
      [step]="control().options.step()"
      [decimals]="control().options.decimals()"
      [readonly]="control().options.readonly()"
      [inputAttributes]="{ inputmode: control().options.inputMode()! }"
      [attr.required]="control().options.required()"
      [style]="{ width: '100%' }"
      (keydown.enter)="control().onSpecialKeyPressed.next('enter')"
    />
    <!-- Hint or Error -->
    @if (!control().options.hideMessage()) {
      <div class="input-message">
        <!-- Errors (optional) -->
        @if (control().touched && control().errors) {
          <kendo-formerror>{{ control().errors | errorMessage }}</kendo-formerror>
          <!-- Hint (optional) -->
        } @else if (control().options.hint()) {
          <kendo-formhint>{{ control().options.hint() }}</kendo-formhint>
        }
      </div>
    }
  </div>
</div>
