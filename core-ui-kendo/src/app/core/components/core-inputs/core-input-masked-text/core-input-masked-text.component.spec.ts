// Angular
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
// Core UI
import { CoreInputMaskedTextComponent } from './core-input-masked-text.component';
import { CoreInputsModule } from '../core-inputs.module';
import { CoreInputMaskedText } from './core-input-masked-text';

describe('Core Input Masked Text Component', () => {
  let component: CoreInputMaskedTextComponent;
  let fixture: ComponentFixture<CoreInputMaskedTextComponent>;
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CoreInputsModule],
    }).compileComponents();

    fixture = TestBed.createComponent(CoreInputMaskedTextComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    fixture.componentRef.setInput('control', new CoreInputMaskedText({ label: 'Test', mask: '(###) 000-0000' }));
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });

  // it('should display the provided label', () => {
  //   fixture.componentRef.setInput('control', new CoreInputText({ label: 'Test' }));
  //   fixture.detectChanges();
  //   const labelElement = fixture.debugElement.query(By.css('.k-label'));
  //   expect((labelElement.nativeElement as HTMLElement).textContent).toContain('Test');
  // });

  // it('should display the provided label on top', () => {
  //   fixture.componentRef.setInput('control', new CoreInputText({ label: 'Test', labelPosition: 'top' }));
  //   fixture.detectChanges();
  //   const labelElement = fixture.debugElement.query(By.css('.k-label'));
  //   expect(fixture.debugElement.query(By.css('.core-input-text-label-on-top'))).toBeTruthy();
  //   expect((labelElement.nativeElement as HTMLElement).textContent).toContain('Test');
  // });

  // it('should display the provided label on left', () => {
  //   fixture.componentRef.setInput('control', new CoreInputText({ label: 'Test', labelPosition: 'left' }));
  //   fixture.detectChanges();
  //   const labelElement = fixture.debugElement.query(By.css('.k-label'));
  //   expect(fixture.debugElement.query(By.css('.core-input-text-label-on-left'))).toBeTruthy();
  //   expect((labelElement.nativeElement as HTMLElement).textContent).toContain('Test');
  // });

  // it('should display the provided placeholder', () => {
  //   fixture.componentRef.setInput('control', new CoreInputText({ label: 'Test', placeholder: 'Test Placeholder' }));
  //   fixture.detectChanges();
  //   const textBox = fixture.debugElement.query(By.css('kendo-textbox'));
  //   const inputRef = textBox.query(By.css('input'));
  //   expect((inputRef.nativeElement as HTMLInputElement).placeholder).toBe('Test Placeholder');
  // });

  // it('should apply the specified mask', () => {
  //   fixture.componentRef.setInput('control', new CoreInputText({ label: 'Test', mask: '(###) 000-0000' }));
  //   fixture.detectChanges();
  //   const textBox = fixture.debugElement.query(By.css('kendo-textbox'));
  //   const inputRef = textBox.query(By.css('input'));
  //   expect((inputRef.nativeElement as HTMLInputElement).getAttribute('mask')).toBe('(###) 000-0000');
  // });
});
