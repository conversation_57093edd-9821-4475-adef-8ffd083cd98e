import { signal } from '@angular/core';
import { CoreInput } from '../shared/classes/core-input';
import { _CoreInputSliderOptions, CoreInputSliderOptions } from './core-input-slider.interfaces';

export class CoreInputSlider extends CoreInput<number | null> {
  declare public options: _CoreInputSliderOptions;

  constructor(options: CoreInputSliderOptions) {
    // Call the parent constructor
    super(options.value ?? null, options);

    // Append implementation specific options with default values
    this.options = {
      ...this.options,
      showButtons: signal(options.showButtons ?? false),
      showNumericInput: signal(options.showNumericInput ?? true),
      min: signal(options.min),
      max: signal(options.max),
      smallStep: signal(options.smallStep),
      largeStep: signal(options.largeStep),
    };
  }
}
