import { Signal } from '@angular/core';
import { _CoreInputOptions, CoreInputOptions } from '../shared/interfaces/core-input-options.interface';

export interface CoreInputSliderOptions extends CoreInputOptions<number> {
  showButtons?: boolean; // Default: false
  showNumericInput?: boolean; // Default: true
  min: number;
  max: number;
  smallStep: number;
  largeStep: number;
}

export interface _CoreInputSliderOptions extends _CoreInputOptions {
  showButtons: Signal<boolean>;
  showNumericInput: Signal<boolean>;
  min: Signal<number>;
  max: Signal<number>;
  smallStep: Signal<number>;
  largeStep: Signal<number>;
}
