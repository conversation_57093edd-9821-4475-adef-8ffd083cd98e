// Angular
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { ReactiveFormsModule } from '@angular/forms';
import { Component, computed, input, OnDestroy, OnInit, viewChild, ViewEncapsulation } from '@angular/core';
// Kendo UI
import { FormFieldModule, NumericTextBoxModule, SliderComponent, SliderModule } from '@progress/kendo-angular-inputs';
import { LabelModule } from '@progress/kendo-angular-label';
import { SVGIconModule } from '@progress/kendo-angular-icons';
import { TooltipsModule } from '@progress/kendo-angular-tooltip';
// Classes
import { CoreInputSlider } from './core-input-slider';
// Pipes
import { ErrorMessagePipe } from '../shared/pipes/error-message.pipe';
// Directives
import { CoreFocusControlDirective } from '../../../directives/core-focus-control.directive';
// Icon
import { infoCircleIcon } from '@progress/kendo-svg-icons';

@Component({
  selector: 'core-input-slider',
  templateUrl: './core-input-slider.component.html',
  styleUrls: ['./core-input-slider.component.scss', '../shared/styles/core-input.styles.scss'],
  encapsulation: ViewEncapsulation.None, // IMPORTANT! Disable view encapsulation to allow custom styles to be applied to child components, use with a wrapper tag
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormFieldModule,
    LabelModule,
    NumericTextBoxModule,
    ErrorMessagePipe,
    CoreFocusControlDirective,
    SVGIconModule,
    TooltipsModule,
    SliderModule,
  ],
})
export class CoreInputSliderComponent implements OnInit, OnDestroy {
  // [ Inputs ]
  public control = input.required<CoreInputSlider>();
  public initialFocused = input<boolean>(false);
  // Icon
  public infoCircleIcon = infoCircleIcon;

  // [ View children ]
  // Note: Needed for core-focus-control.directive.ts
  public _inputRef = viewChild<SliderComponent>('input');

  // [ Computed ]

  public _labelWrapperClass = computed(() => {
    return this.control().options.labelPosition() === 'top'
      ? 'core-input-text-label-on-top'
      : 'core-input-text-label-on-left';
  });

  // [ Internal ]

  private _subscription$ = new Subscription();

  public ngOnInit(): void {
    this._subscription$.add(
      this.control().valueChanges.subscribe((value: number | null) => {
        this.control().onValueChange(value) as void;
      }),
    );
  }

  public getInfoText(): string {
    const info = this.control().options.info();
    return typeof info === 'string' ? info : (info?.text ?? '');
  }

  public getInfoTitle(): string {
    const info = this.control().options.info();
    return typeof info === 'string' ? '' : (info?.title ?? '');
  }

  public ngOnDestroy(): void {
    this._subscription$.unsubscribe();
  }
}
