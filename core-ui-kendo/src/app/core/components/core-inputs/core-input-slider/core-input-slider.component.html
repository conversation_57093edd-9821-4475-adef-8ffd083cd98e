<div [ngClass]="_labelWrapperClass()">
  <!-- Label (optional)-->
  <div class="label">
    @if (control().options.label()) {
      <kendo-label
        [ngStyle]="{ 'min-width': control().options.labelMinWidth() }"
        [labelCssStyle]="{ 'font-size': '0.9rem' }"
        [for]="input"
        [text]="control().options.label()!"
        [optional]="control().options.optional()"
      />
      @if (this.control().options.showRequiredIndicator() && this.control().options.required()) {
        <span class="required-indicator">*</span>
      }
    }
    <!-- Tooltip Icon-->
    @if (control().options.info()) {
      <div kendoTooltip position="bottom" [titleTemplate]="titleTemplate" [tooltipWidth]="200">
        <kendo-svg-icon [icon]="infoCircleIcon" [title]="getInfoText()" />
      </div>
      <ng-template #titleTemplate> {{ getInfoTitle() }} </ng-template>
    }
  </div>
  <!-- Input -->
  <div class="row input-wrapper">
    <div class="row">
      <kendo-slider
        #input
        kendoTooltip
        [formControl]="control()"
        [coreFocusControl]="_inputRef()!"
        [initialFocused]="initialFocused()"
        [showButtons]="control().options.showButtons()"
        [min]="control().options.min()!"
        [max]="control().options.max()!"
        [smallStep]="control().options.smallStep()"
        [largeStep]="control().options.largeStep()"
        [readonly]="control().options.readonly()"
        [attr.required]="control().options.required()"
        [(value)]="control().value"
      />
      @if (control().options.showNumericInput()) {
        <kendo-numerictextbox
          kendoTooltip
          [autoCorrect]="true"
          [formControl]="control()"
          [min]="control().options.min()!"
          [max]="control().options.max()!"
          [format]="'#'"
          [readonly]="control().options.readonly()"
          [inputAttributes]="{ inputmode: control().options.inputMode()! }"
          [attr.required]="control().options.required()"
          [(value)]="control().value"
          class="numeric-textbox"
        />
      }
    </div>
    @if (!control().options.showNumericInput()) {
      <span class="slider-value">{{ control().value ?? control().options.min() }}</span>
    }
    <!-- Hint (optional) -->
    @if (!control().options.hideMessage()) {
      @if (control().options.hint() && !control().errors) {
        <kendo-formhint>{{ control().options.hint() }}</kendo-formhint>
      }
      <!-- Errors (optional) -->
      @if (control().touched && control().errors) {
        <kendo-formerror>{{ control().errors | errorMessage }}</kendo-formerror>
      }
    }
  </div>
</div>
