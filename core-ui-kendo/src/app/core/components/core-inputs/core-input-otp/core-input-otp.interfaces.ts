import { Signal } from '@angular/core';
import { _CoreInputOptions, CoreInputOptions } from '../shared/interfaces/core-input-options.interface';
import { CoreInputOtpType } from './core-input-otp.types';

export interface CoreInputOtpOptions extends CoreInputOptions<number | string | null> {
  type?: CoreInputOtpType; // Default: 'number'
  separator?: string;
  groupLength?: number[];
  length?: number; // Default: 4
}

export interface _CoreInputOtpOptions extends _CoreInputOptions {
  type: Signal<CoreInputOtpType>;
  separator: Signal<string>;
  groupLength: Signal<number[]>;
  length: Signal<number>;
}
