import { signal } from '@angular/core';
import { CoreInput } from '../shared/classes/core-input';
import { _CoreInputOtpOptions, CoreInputOtpOptions } from './core-input-otp.interfaces';

export class CoreInputOtp extends CoreInput<number | string | null> {
  declare public options: _CoreInputOtpOptions;

  constructor(options: CoreInputOtpOptions) {
    // Call the parent constructor
    super(options.value ?? null, options);

    // Append implementation specific options with default values
    this.options = {
      ...this.options,
      type: signal(options.type ?? 'number'),
      length: signal(options.length ?? 4),
      separator: signal(options.separator ?? '-'),
      groupLength: signal(options.groupLength ?? []),
    };
  }
}
