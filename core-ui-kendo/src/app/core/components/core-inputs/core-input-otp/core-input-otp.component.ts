// Angular
import { CommonModule } from '@angular/common';
import { Component, computed, input, On<PERSON><PERSON>roy, OnInit, viewChild, ViewEncapsulation } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
// Kendo UI
import { FormFieldModule, OTPInputComponent } from '@progress/kendo-angular-inputs';
import { LabelModule } from '@progress/kendo-angular-label';
import { SVGIconModule } from '@progress/kendo-angular-icons';
import { TooltipsModule } from '@progress/kendo-angular-tooltip';
import { infoCircleIcon } from '@progress/kendo-svg-icons';
// Core UI
import { ErrorMessagePipe } from '../shared/pipes/error-message.pipe';
// Directives
import { CoreFocusControlDirective } from '../../../directives/core-focus-control.directive';
// Classes
import { CoreInputOtp } from './core-input-otp';

@Component({
  selector: 'core-input-otp',
  templateUrl: './core-input-otp.component.html',
  styleUrls: ['./core-input-otp.component.scss', '../shared/styles/core-input.styles.scss'],
  encapsulation: ViewEncapsulation.None, // IMPORTANT! Disable view encapsulation to allow custom styles to be applied to child components, use with a wrapper tag
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormFieldModule,
    LabelModule,
    ErrorMessagePipe,
    CoreFocusControlDirective,
    SVGIconModule,
    TooltipsModule,
    OTPInputComponent,
  ],
})
export class CoreInputOtpComponent implements OnInit, OnDestroy {
  // [ Inputs ]
  public control = input.required<CoreInputOtp>();
  public initialFocused = input<boolean>(false);
  // Icon
  public infoCircleIcon = infoCircleIcon;

  // [ View children ]
  // Note: Needed for core-focus-control.directive.ts
  public _inputRef = viewChild<OTPInputComponent>('input');

  // [ Computed ]

  public _labelWrapperClass = computed(() => {
    return this.control().options.labelPosition() === 'top'
      ? 'core-input-text-label-on-top'
      : 'core-input-text-label-on-left';
  });

  // [ Internal ]

  private _subscription$ = new Subscription();

  public ngOnInit(): void {
    this._subscription$.add(
      this.control().valueChanges.subscribe((value: number | string | null) => {
        this.control().onValueChange(value) as void;
      }),
    );
  }

  public getInfoText(): string {
    const info = this.control().options.info();
    return typeof info === 'string' ? info : (info?.text ?? '');
  }

  public getInfoTitle(): string {
    const info = this.control().options.info();
    return typeof info === 'string' ? '' : (info?.title ?? '');
  }

  public ngOnDestroy(): void {
    this._subscription$.unsubscribe();
  }
}
