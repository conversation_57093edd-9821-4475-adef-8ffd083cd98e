:host {
  display: block;
}

kendo-formerror {
  white-space: pre-wrap;
  font-size: 11.5px;
}

.input-message {
  min-height: 1.15rem;
}

.core-input-text-label-on-top {
  display: flex;
  flex-flow: column nowrap;
  align-items: stretch;
  gap: 0.125rem;
}

.core-input-text-label-on-left {
  display: flex;
  flex-flow: row nowrap;
  align-items: flex-start;
  gap: 0.5rem;

  > kendo-label {
    padding-top: 0.25rem;
  }
}

.label {
  display: flex;
  gap: 0.2rem;

  .required-indicator {
    color: var(--accent-primary-color);
  }

  kendo-label {
    padding-bottom: 0.15rem;
  }

  kendo-svg-icon {
    display: flex;
    align-items: flex-start;
  }
}

.input-wrapper {
  display: flex;
  flex-flow: column nowrap;
  flex: 1 1 0;
}
