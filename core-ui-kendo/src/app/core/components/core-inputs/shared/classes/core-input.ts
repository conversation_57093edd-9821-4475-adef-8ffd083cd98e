// Angular
import { signal } from '@angular/core';
import { FormControl } from '@angular/forms';
// Classes
import { CoreInputValidators } from './core-input-validators';
// Interfaces
import { CoreInputOptions, _CoreInputOptions } from '../interfaces/core-input-options.interface';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export class CoreInput<TValue = any> extends FormControl {
  // [ Public ]

  public options: _CoreInputOptions;
  public onValueChange: (value: TValue | null) => void | Promise<void>;

  constructor(value: TValue, options: CoreInputOptions<TValue>) {
    // Call the parent constructor with the `value`, `disabled` and `validators` data
    super(
      { value, disabled: options.disabled ?? false },
      CoreInputValidators.getValidatorFunctions(options.validators),
    );

    // Set the options with default values
    this.options = {
      label: signal(options.label ?? null),
      labelPosition: signal(options.labelPosition ?? 'top'),
      labelMinWidth: signal(options.labelMinWidth ?? null),
      hint: signal(options.hint ?? null),
      readonly: signal(options.readonly ?? false),
      optional: signal(options.optional ?? false),
      required: signal(CoreInputValidators.hasValidator('required', options.validators)),
      showRequiredIndicator: signal(options.showRequiredIndicator ?? true),
      info: signal(this.formatInfo(options.info)),
      hideMessage: signal(options.hideMessage ?? false),
      inputMode: signal(options.inputMode ?? 'text'),
    };

    // Set the `onValueChange` callback
    this.onValueChange = options.onValueChange ?? (() => void 0);
  }

  private formatInfo(info: CoreInputOptions<TValue>['info']): { title: string; text: string } | null {
    if (typeof info === 'string') {
      return { title: '', text: info };
    }
    return info ?? null;
  }

  public setHint(hint: string): void {
    this.options.hint.set(hint);
  }
}
