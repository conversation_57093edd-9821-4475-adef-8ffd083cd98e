/* eslint-disable @typescript-eslint/no-misused-promises */

// Angular
import { ChangeDetectorRef, Component, effect, inject, input, OnInit, signal, viewChild } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, RouterModule } from '@angular/router';
// Kendo UI
import { SelectEvent, TabStripComponent, TabStripModule } from '@progress/kendo-angular-layout';
import { SVGIconModule } from '@progress/kendo-angular-icons';
// Classes
import { CoreNavigationTabs } from './core-navigation-tabs';

@Component({
  selector: 'core-navigation-tabs',
  templateUrl: './core-navigation-tabs.component.html',
  styleUrl: './core-navigation-tabs.component.scss',
  imports: [RouterModule, SVGIconModule, TabStripModule],
})
export class CoreNavigationTabsComponent implements OnInit {
  // Inputs
  public navigationTabs = input.required<CoreNavigationTabs>();

  // View child
  public _kendoTabStrip = viewChild.required(TabStripComponent);

  // Dependencies
  private _route = inject(ActivatedRoute);
  private _router = inject(Router);
  private _changeDetectorRef = inject(ChangeDetectorRef);

  // Internal properties
  private _selectedTabIndex = signal<number>(-1);

  constructor() {
    effect(() => {
      const selectedTabIndex = this._selectedTabIndex();
      if (selectedTabIndex !== -1) {
        // Update the selected tab index in the navigation tabs
        this._kendoTabStrip().selectTab(selectedTabIndex);
        this._changeDetectorRef.detectChanges();
      }
    });
  }

  public async ngOnInit(): Promise<void> {
    // Subscribe to router events to update the selected tab index
    this.subscribeToRouterEvents();
    // Get current active route
    let currentRoute = this._route.snapshot.children[0]?.routeConfig?.path ?? null;

    // Auto navigate to the first item if no child route is active
    if (currentRoute == null && this.navigationTabs()._options().autoNavigate) {
      // Get the first item
      const firstItem = this.navigationTabs()._options().tabs[0];
      if (firstItem) {
        await this._router.navigate([firstItem.route], {
          relativeTo: this._route,
        });
        currentRoute = firstItem.route;
      }
    }

    // Select the current item
    const selectedItemIndex = this.navigationTabs()
      ._options()
      .tabs.findIndex((i) => i.route === currentRoute);
    setTimeout(() => {
      this._selectedTabIndex.set(selectedItemIndex);
    });
  }

  // Events

  public async onTabSelect(e: SelectEvent): Promise<void> {
    // Do not select new tab by default
    e.preventDefault();

    // Determine if the navigation is successful
    // Get the target route
    const targetRoute = this.navigationTabs()._options().tabs[e.index].route;
    const currentRoute = this._route.snapshot.children[0]?.routeConfig?.path ?? null;

    let success = true;

    // Only navigate if the URL is different
    if (targetRoute !== currentRoute) {
      success = await this._router.navigate([targetRoute], {
        relativeTo: this._route,
      });
    }

    // Swith to the selected tab if navigation was successful
    if (success) {
      this._selectedTabIndex.set(e.index);
    }
  }

  private subscribeToRouterEvents(): void {
    this._router.events.subscribe(async (event) => {
      if (event instanceof NavigationEnd) {
        const currentRoute = this._route.snapshot.children[0]?.routeConfig?.path ?? null;
        const selectedItemIndex = this.navigationTabs()
          ._options()
          .tabs.findIndex((i) => i.route === currentRoute);
        if (selectedItemIndex !== -1) {
          this._selectedTabIndex.set(selectedItemIndex);
        } else {
          // Navigate to the first available tab
          const firstAvailableTab = this.navigationTabs()._options().tabs[0];
          if (firstAvailableTab) {
            await this._router.navigate([firstAvailableTab.route], {
              relativeTo: this._route,
            });
          }
        }
      }
    });
  }
}
