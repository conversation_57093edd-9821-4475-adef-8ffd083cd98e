<kendo-breadcrumb [items]="_items()" (itemClick)="onItemClick($event)">
  <ng-template kendoBreadCrumbItemTemplate let-item let-index="index">
    <div class="item" role="button" [class.active]="index === _items().length - 1" [class.icon-fix]="!item.text">
      @if(item.svgIcon) {
      <core-icon [icon]="item.svgIcon" />
      }
      <span class="text-center-fix">{{ item.text }}</span>
    </div>
  </ng-template>
</kendo-breadcrumb>
