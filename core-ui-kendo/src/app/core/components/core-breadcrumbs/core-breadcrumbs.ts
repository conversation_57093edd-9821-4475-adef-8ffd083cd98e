// Angular
import { Component, inject, OnDestroy, OnInit, signal } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { filter, Subscription } from 'rxjs';
// Kendo UI
import { BreadCrumbItem, KENDO_NAVIGATION } from '@progress/kendo-angular-navigation';
import { homeIcon } from '@progress/kendo-svg-icons';
// Core UI
import { _CoreBreadCrumbItem, CoreBreadCrumbItemData } from './core-breadcrumbs.interfaces';
import { CoreIconModule } from '../core-icon';

@Component({
  selector: 'core-breadcrumbs',
  templateUrl: './core-breadcrumbs.html',
  styleUrl: './core-breadcrumbs.scss',
  imports: [KENDO_NAVIGATION, CoreIconModule],
})
export class CoreBreadcrumbsComponent implements OnInit, OnDestroy {
  // [ Internal ]

  public _items = signal<_CoreBreadCrumbItem[]>([]);

  private _activatedRoute = inject(ActivatedRoute);
  private _router = inject(Router);
  private _subscription$ = new Subscription();

  public ngOnInit(): void {
    // Build initial items
    void this._buildItems();
    // Rebuild items when navigation changes
    this._subscription$.add(
      this._router.events.pipe(filter((e) => e instanceof NavigationEnd)).subscribe(() => {
        void this._buildItems();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscription$.unsubscribe();
  }

  public async onItemClick(item: BreadCrumbItem): Promise<void> {
    const index = this._items().findIndex((e) => e.text === item.text);
    this._items.set(this._items().slice(0, index + 1));
    const _item = item as _CoreBreadCrumbItem;
    if (_item === this._items()[0]) {
      await this._router.navigate([(item as _CoreBreadCrumbItem).path], { relativeTo: this._activatedRoute.parent });
    } else {
      await this._router.navigate([(item as _CoreBreadCrumbItem).path], { relativeTo: this._activatedRoute });
    }
  }

  private _buildItems(): void {
    // Build items from activatedRoute
    const root: _CoreBreadCrumbItem = {
      svgIcon: homeIcon,
      path: this._activatedRoute.snapshot.url[0].path,
    };
    const children: _CoreBreadCrumbItem[] = [];
    if (this._activatedRoute.children.length > 0) {
      children.push(...this.getRouteChildrenAsItems(this._activatedRoute.children));
    }
    this._items.set([root, ...children]);
  }

  private getRouteChildrenAsItems(routes: ActivatedRoute[]): _CoreBreadCrumbItem[] {
    if (routes.length === 0) return [];
    const items = [];
    for (const route of routes) {
      items.push(this.getItemFromRoute(route));
      if (route.children) {
        items.push(...this.getRouteChildrenAsItems(route.children));
      }
    }
    return items;
  }

  private getItemFromRoute(route: ActivatedRoute): _CoreBreadCrumbItem {
    return this.getItemFromRouteData(route) ?? this.getItemFromRoutePath(route);
  }

  private getItemFromRouteData(route: ActivatedRoute): _CoreBreadCrumbItem | null {
    if ('breadcrumb' in route.snapshot.data) {
      return {
        ...(route.snapshot.data['breadcrumb'] as CoreBreadCrumbItemData),
        path: route.snapshot.url[0].path,
      };
    }
    return null;
  }

  private getItemFromRoutePath(route: ActivatedRoute): _CoreBreadCrumbItem {
    return {
      text: this.convertPathToLabel(route.snapshot.url[0].path),
      path: route.snapshot.url[0].path,
    };
  }

  private convertPathToLabel(path: string): string {
    const words = path.split('-');
    let label = '';
    for (let i = 0; i < words.length; i++) {
      const firstLetter = words[i][0].toLocaleUpperCase();
      const rest = words[i].split('').slice(1).join('');
      label += firstLetter + rest;
      if (i < words.length - 1) {
        label += ' ';
      }
    }
    return label;
  }
}
