// Angular
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { menuIcon } from '@progress/kendo-svg-icons';
// Core UI
import { CoreButtonComponent } from './core-button.component';
import { CoreButtonModule } from './core-button.module';
import { CoreIcon } from '../core-icon/core-icon.interfaces';

describe('Core Button Component', () => {
  let component: CoreButtonComponent;
  let fixture: ComponentFixture<CoreButtonComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CoreButtonModule],
    }).compileComponents();

    fixture = TestBed.createComponent(CoreButtonComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });

  it('should display the provided label', () => {
    fixture.componentRef.setInput('label', 'Save Changes');
    fixture.detectChanges();
    const buttonElement = fixture.debugElement.query(By.css('button'));
    const buttonEl: HTMLButtonElement = buttonElement.nativeElement as HTMLButtonElement;
    expect(buttonEl.textContent?.trim()).toBe('Save Changes');
  });

  it('should be disabled when the disabled input is true', () => {
    fixture.componentRef.setInput('disabled', true);
    fixture.detectChanges();
    const buttonElement = fixture.debugElement.query(By.css('button'));
    const buttonEl: HTMLButtonElement = buttonElement.nativeElement as HTMLButtonElement;
    expect(buttonEl.disabled).toBe(true);
  });

  it('should be disabled and show loader when busy is true', () => {
    fixture.componentRef.setInput('busy', true);
    fixture.detectChanges();
    const buttonDebugElement = fixture.debugElement.query(By.css('button'));
    const loaderElement = fixture.debugElement.query(By.css('kendo-loader'));
    const buttonEl: HTMLButtonElement = buttonDebugElement.nativeElement as HTMLButtonElement;
    expect(buttonEl.disabled).toBe(true);
    expect(loaderElement).toBeTruthy();
  });

  it('should render an SVG icon when a CoreIcon object is provided', () => {
    const mockIcon: CoreIcon = menuIcon;
    fixture.componentRef.setInput('icon', mockIcon);
    fixture.detectChanges();
    const svgElement = fixture.debugElement.query(By.css('button kendo-svgicon'));
    expect(svgElement).toBeTruthy();
  });

  describe('Kendo class bindings', () => {
    it('should apply solid class for "default" type and "primary" color', () => {
      fixture.componentRef.setInput('type', 'default');
      fixture.componentRef.setInput('color', 'primary');
      fixture.detectChanges();
      const buttonDebugElement = fixture.debugElement.query(By.css('button'));
      expect(buttonDebugElement.classes['k-button-solid-primary']).toBe(true);
    });

    it('should apply outline class for "outline" type and "error" color', () => {
      fixture.componentRef.setInput('type', 'outline');
      fixture.componentRef.setInput('color', 'error');
      fixture.detectChanges();
      const buttonDebugElement = fixture.debugElement.query(By.css('button'));
      expect(buttonDebugElement.classes['k-button-outline-error']).toBe(true);
    });

    it('should apply flat class for "flat" type and "secondary" color', () => {
      fixture.componentRef.setInput('type', 'flat');
      fixture.componentRef.setInput('color', 'secondary');
      fixture.detectChanges();
      const buttonDebugElement = fixture.debugElement.query(By.css('button'));
      expect(buttonDebugElement.classes['k-button-flat-secondary']).toBe(true);
    });
  });

  describe('Accessibility & Focus Management', () => {
    it('should be focusable', () => {
      fixture.detectChanges();
      const buttonEl = fixture.debugElement.query(By.css('button')).nativeElement as HTMLElement;
      buttonEl.focus();
      fixture.detectChanges();
      expect(document.activeElement).toBe(buttonEl);
    });
    it('should apply the aria-label attribute', () => {
      const testAriaLabel = 'Close navigation menu';
      fixture.componentRef.setInput('ariaLabel', testAriaLabel);
      fixture.detectChanges();
      const buttonDebugElement = fixture.debugElement.query(By.css('button'));
      const buttonEl: HTMLButtonElement = buttonDebugElement.nativeElement as HTMLButtonElement;
      expect(buttonEl.getAttribute('aria-label')).toBe(testAriaLabel);
    });
  });
});
