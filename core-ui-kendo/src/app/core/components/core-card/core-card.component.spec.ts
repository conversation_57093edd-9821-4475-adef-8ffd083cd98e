// Angular
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { Component } from '@angular/core';
// Core UI
import { CoreCardModule } from './core-card.module';
import { CoreCardComponent } from './core-card.component';

@Component({
  template: `
    <core-card>
      <core-card-header>
        <h2 id="card-header-content">Card Title</h2>
      </core-card-header>
      <core-card-body>
        <p id="card-body-content">Card Body...</p>
      </core-card-body>
      <core-card-footer>
        <div id="card-footer-content">Card Footer...</div>
      </core-card-footer>
    </core-card>
  `,
  imports: [CoreCardModule],
})
class TestCardComponent {}

describe('Core Card Component', () => {
  let coreCardComponent: CoreCardComponent;
  let mainFixture: ComponentFixture<CoreCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CoreCardModule, TestCardComponent],
      declarations: [],
    }).compileComponents();

    mainFixture = TestBed.createComponent(CoreCardComponent);
    coreCardComponent = mainFixture.componentInstance;
  });

  it('should create', () => {
    mainFixture.detectChanges();
    expect(coreCardComponent).toBeTruthy();
  });

  it('should show core spinner when loading is set to true', () => {
    mainFixture.componentRef.setInput('loading', true);
    mainFixture.detectChanges();
    const coreSpinnerComponent = mainFixture.debugElement.query(By.css('core-spinner'));
    expect(coreSpinnerComponent).toBeTruthy();
  });

  it('should render projected content', () => {
    const fixture = TestBed.createComponent(TestCardComponent);
    fixture.detectChanges();
    const headerDebugElement = fixture.debugElement.query(By.css('#card-header-content'));
    const bodyDebugElement = fixture.debugElement.query(By.css('#card-body-content'));
    const footerDebugElement = fixture.debugElement.query(By.css('#card-footer-content'));
    expect(headerDebugElement).not.toBeNull();
    expect(bodyDebugElement).not.toBeNull();
    expect(footerDebugElement).not.toBeNull();
    expect((headerDebugElement.nativeElement as HTMLElement).textContent).toContain('Card Title');
    expect((bodyDebugElement.nativeElement as HTMLElement).textContent).toContain('Card Body...');
    expect((footerDebugElement.nativeElement as HTMLElement).textContent).toContain('Card Footer...');
  });
});
