// Angular
import { Component, computed, model, output } from '@angular/core';
// Kendo UI
import { chevronDoubleLeftIcon, menuIcon } from '@progress/kendo-svg-icons';
// Core UI
import { CoreButtonModule } from '../core-button';

@Component({
  selector: 'core-hamburger-button',
  templateUrl: './core-hamburger-button.html',
  styleUrl: './core-hamburger-button.scss',
  imports: [CoreButtonModule],
})
export class CoreHamburgerButton {
  // [ Public API ]

  public expanded = model<boolean>(true);
  public buttonClick = output<void>();

  // [ Internal ]

  public _icon = computed(() => (this.expanded() ? chevronDoubleLeftIcon : menuIcon));

  public onClick(): void {
    this.expanded.set(!this.expanded());
    this.buttonClick.emit();
  }
}
