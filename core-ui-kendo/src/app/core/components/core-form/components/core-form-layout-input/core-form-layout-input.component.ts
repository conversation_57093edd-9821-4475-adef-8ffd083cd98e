import { Component, input } from '@angular/core';
import {
  CoreInputsModule,
  CoreInputCheckbox,
  CoreInputDate,
  CoreInputDateTime,
  CoreInputMaskedText,
  CoreInputMultiselect,
  CoreInputNumeric,
  CoreInputPassword,
  CoreInputSelect,
  CoreInputSwitch,
  CoreInputText,
  CoreInputTextarea,
  CoreInputFileSelect,
  CoreInpuColorPicker,
  CoreInputDropDown,
  CoreInputEditor,
  CoreInputTime,
  CoreInputCron,
  CoreInputOtp,
  CoreInputRadioSelect,
  CoreInputSlider,
  CoreInputEmail,
} from '../../../core-inputs';
import { CoreInput } from '../../../core-inputs/shared/classes/core-input';
import { CoreFormItem } from '../../interfaces/core-form-item';

@Component({
  selector: 'core-form-layout-input',
  imports: [CoreInputsModule],
  templateUrl: './core-form-layout-input.component.html',
  styleUrl: './core-form-layout-input.component.scss',
})
export class CoreFormLayoutInputComponent {
  // [ Inputs ]
  public control = input.required<CoreInput & CoreFormItem>();
  public initialFocused = input<boolean>(false);

  // [ Internal Functions ]
  public $asText(input: CoreInput): CoreInputText {
    return input as CoreInputText;
  }

  public $asMaskedText(input: CoreInput): CoreInputMaskedText {
    return input as CoreInputMaskedText;
  }

  public $asTextarea(input: CoreInput): CoreInputTextarea {
    return input as CoreInputTextarea;
  }

  public $asPassword(input: CoreInput): CoreInputPassword {
    return input as CoreInputPassword;
  }

  public $asNumeric(input: CoreInput): CoreInputNumeric {
    return input as CoreInputNumeric;
  }

  public $asDate(input: CoreInput): CoreInputDate {
    return input as CoreInputDate;
  }

  public $asDateTime(input: CoreInput): CoreInputDateTime {
    return input as CoreInputDateTime;
  }

  public $asTime(input: CoreInput): CoreInputTime {
    return input as CoreInputTime;
  }

  public $asSelect(input: CoreInput): CoreInputSelect {
    return input as CoreInputSelect;
  }

  public $asMultiselect(input: CoreInput): CoreInputMultiselect {
    return input as CoreInputMultiselect;
  }

  public $asCheckbox(input: CoreInput): CoreInputCheckbox {
    return input as CoreInputCheckbox;
  }

  public $asSwitch(input: CoreInput): CoreInputSwitch {
    return input as CoreInputSwitch;
  }

  public $asFileSelect(input: CoreInput): CoreInputFileSelect {
    return input as CoreInputFileSelect;
  }

  public $asColorPicker(input: CoreInput): CoreInpuColorPicker {
    return input as CoreInpuColorPicker;
  }

  public $asDropdownSelect(input: CoreInput): CoreInputDropDown {
    return input as CoreInputDropDown;
  }

  public $asTextEditor(input: CoreInput): CoreInputEditor {
    return input as CoreInputEditor;
  }

  public $asCron(input: CoreInput): CoreInputCron {
    return input as CoreInputCron;
  }

  public $asOtp(input: CoreInput): CoreInputOtp {
    return input as CoreInputOtp;
  }

  public $asRadioSelect(input: CoreInput): CoreInputRadioSelect {
    return input as CoreInputRadioSelect;
  }

  public $asSlider(input: CoreInput): CoreInputSlider {
    return input as CoreInputSlider;
  }

  public $asEmail(input: CoreInput): CoreInputEmail {
    return input as CoreInputEmail;
  }
}
