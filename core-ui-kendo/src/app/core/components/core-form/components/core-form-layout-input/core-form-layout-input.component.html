@switch (control().options.kind()) {
  @case ("text") {
    <core-input-text [control]="$asText(control())" [initialFocused]="initialFocused()" />
  }
  @case ("maskedText") {
    <core-input-masked-text [control]="$asMaskedText(control())" [initialFocused]="initialFocused()" />
  }
  @case ("textarea") {
    <core-input-textarea [control]="$asTextarea(control())" [initialFocused]="initialFocused()" />
  }
  @case ("password") {
    <core-input-password [control]="$asPassword(control())" [initialFocused]="initialFocused()" />
  }
  @case ("numeric") {
    <core-input-numeric [control]="$asNumeric(control())" [initialFocused]="initialFocused()" />
  }
  @case ("date") {
    <core-input-date [control]="$asDate(control())" [initialFocused]="initialFocused()" />
  }
  @case ("datetime") {
    <core-input-date-time [control]="$asDateTime(control())" [initialFocused]="initialFocused()" />
  }
  @case ("time") {
    <core-input-time [control]="$asTime(control())" [initialFocused]="initialFocused()" />
  }
  @case ("select") {
    <core-input-select [control]="$asSelect(control())" [initialFocused]="initialFocused()" />
  }
  @case ("multiselect") {
    <core-input-multiselect [control]="$asMultiselect(control())" [initialFocused]="initialFocused()" />
  }
  @case ("checkbox") {
    <core-input-checkbox [control]="$asCheckbox(control())" [initialFocused]="initialFocused()" />
  }
  @case ("switch") {
    <core-input-switch [control]="$asSwitch(control())" [initialFocused]="initialFocused()" />
  }
  @case ("fileSelect") {
    <core-input-file-select [control]="$asFileSelect(control())" />
  }
  @case ("colorPicker") {
    <core-input-color-picker [control]="$asColorPicker(control())" [initialFocused]="initialFocused()" />
  }
  @case ("dropdownSelect") {
    <core-input-drop-down [control]="$asDropdownSelect(control())" [initialFocused]="initialFocused()" />
  }
  @case ("textEditor") {
    <core-input-editor [control]="$asTextEditor(control())" [initialFocused]="initialFocused()" />
  }
  @case ("cron") {
    <core-input-cron [control]="$asCron(control())" [initialFocused]="initialFocused()" />
  }
  @case ("otp") {
    <core-input-otp [control]="$asOtp(control())" [initialFocused]="initialFocused()" />
  }
  @case ("radioSelect") {
    <core-input-radio-select [control]="$asRadioSelect(control())" />
  }
  @case ("slider") {
    <core-input-slider [control]="$asSlider(control())" [initialFocused]="initialFocused()" />
  }
  @case ("email") {
    <core-input-email [control]="$asEmail(control())" [initialFocused]="initialFocused()" />
  }
  @default {
    <div>Unknown Form Control</div>
  }
}
