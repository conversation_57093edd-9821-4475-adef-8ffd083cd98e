import { Component, HostBinding, input } from '@angular/core';
import { CoreFormLayoutGroup } from './core-form-layout-group.interfaces';
import { CoreFormLayoutInputComponent } from '../core-form-layout-input/core-form-layout-input.component';
import { CoreInput } from '../../../core-inputs/shared/classes/core-input';
import { CoreFormItem } from '../../interfaces/core-form-item';
import { CoreFormLayoutInput } from '../core-form-layout-input/core-form-layout-input.interfaces';

@Component({
  selector: 'core-form-layout-group',
  imports: [CoreFormLayoutInputComponent],
  templateUrl: './core-form-layout-group.component.html',
  styleUrl: './core-form-layout-group.component.scss',
})
export class CoreFormLayoutGroupComponent {
  public controls = input.required<(CoreInput & CoreFormItem)[]>();
  public layoutGroup = input.required<CoreFormLayoutGroup>();
  public firstControlName = input<string | null>(null);

  public isLayoutGroup(
    layoutItem: CoreFormLayoutGroup | CoreFormLayoutInput | string,
  ): layoutItem is CoreFormLayoutGroup {
    return typeof layoutItem === 'object' && 'items' in layoutItem;
  }

  public getControlByControlName(controlName: string | CoreFormLayoutInput): (CoreInput & CoreFormItem) | undefined {
    if (typeof controlName === 'string') {
      return this.controls().find((control) => control.options.name() === controlName);
    } else {
      return this.controls().find((control) => control.options.name() === controlName.controlName);
    }
  }

  public getInputStyle(input: string | CoreFormLayoutInput) {
    if (typeof input === 'string') {
      return {};
    } else {
      return input.style ?? {};
    }
  }

  public shouldFocusControl(layoutItem: string | CoreFormLayoutInput): boolean {
    const controlName = typeof layoutItem === 'string' ? layoutItem : layoutItem.controlName;
    return this.firstControlName() === controlName;
  }

  @HostBinding('style')
  get style(): Record<string, string> {
    return {
      'flex-direction': this.layoutGroup().layout === 'horizontal' ? 'row' : 'column',
      gap: '8px',
      border: this.layoutGroup().label ? 'none' : (this.layoutGroup().borderStyle ?? 'none'),
      ...this.layoutGroup().style,
    };
  }
}
