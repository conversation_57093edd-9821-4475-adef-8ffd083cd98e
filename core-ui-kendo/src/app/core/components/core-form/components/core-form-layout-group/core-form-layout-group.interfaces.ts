import { CoreFormLayoutInput } from '../core-form-layout-input/core-form-layout-input.interfaces';

export interface CoreFormLayoutGroup {
  items: (CoreFormLayoutGroup | CoreFormLayoutInput | string)[];
  label?: string; // Optional layout label, default to {}
  layout?: 'vertical' | 'horizontal'; // Optional layout type, defaults to 'vertical'
  borderStyle?: string; // Optional border, default to {}
  style?: Record<string, string>; // Optional style
}
