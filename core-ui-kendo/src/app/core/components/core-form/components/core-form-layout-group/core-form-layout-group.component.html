@for (layoutItem of layoutGroup().items; track $index) {
  @if (isLayoutGroup(layoutItem)) {
    @if (layoutItem.label) {
      <fieldset [style.border]="layoutItem.borderStyle">
        <legend>{{ layoutItem.label }}</legend>
        <core-form-layout-group
          [layoutGroup]="layoutItem"
          [controls]="controls()"
          [firstControlName]="firstControlName()"
        />
      </fieldset>
    } @else {
      <core-form-layout-group
        [layoutGroup]="layoutItem"
        [controls]="controls()"
        [firstControlName]="firstControlName()"
      />
    }
  } @else {
    @let control = getControlByControlName(layoutItem);
    @if (control) {
      <core-form-layout-input
        [control]="control"
        [style]="getInputStyle(layoutItem)"
        [initialFocused]="shouldFocusControl(layoutItem)"
      />
    }
  }
}
