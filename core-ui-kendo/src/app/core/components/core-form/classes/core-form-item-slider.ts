import { signal } from '@angular/core';
import { CoreInputSlider } from '../../core-inputs';
import { _CoreFormItemSliderOptions, CoreFormItemSliderOptions } from '../interfaces/core-form-item-slider-options';

export class CoreFormItemSlider extends CoreInputSlider {
  declare public options: _CoreFormItemSliderOptions;

  constructor(options: CoreFormItemSliderOptions) {
    // Call the parent constructor
    super(options);
    // Append implementation specific options with default values
    this.options = {
      ...this.options,
      kind: signal('slider'),
      name: signal(options.name),
    };
  }
}
