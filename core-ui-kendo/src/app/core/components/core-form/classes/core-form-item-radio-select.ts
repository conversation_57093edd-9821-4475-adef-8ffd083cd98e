import { signal } from '@angular/core';
import { CoreInputRadioSelect } from '../../core-inputs';
import {
  _CoreFormItemRadioSelectOptions,
  CoreFormItemRadioSelectOptions,
} from '../interfaces/core-form-item-radio-select-options';

export class CoreFormItemRadioSelect extends CoreInputRadioSelect {
  declare public options: _CoreFormItemRadioSelectOptions;

  constructor(options: CoreFormItemRadioSelectOptions) {
    // Call the parent constructor
    super(options);
    // Append implementation specific options with default values
    this.options = {
      ...this.options,
      kind: signal('radioSelect'),
      name: signal(options.name),
    };
  }
}
