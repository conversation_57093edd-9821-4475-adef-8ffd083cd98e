import { signal } from '@angular/core';
import { CoreInputEmail } from '../../core-inputs';
import { _CoreFormItemEmailOptions, CoreFormItemEmailOptions } from '../interfaces/core-form-item-email-options';

export class CoreFormItemEmail extends CoreInputEmail {
  declare public options: _CoreFormItemEmailOptions;

  constructor(options: CoreFormItemEmailOptions) {
    // Call the parent constructor
    super(options);
    // Append implementation specific options with default values
    this.options = {
      ...this.options,
      kind: signal('email'),
      name: signal(options.name),
    };
  }
}
