import { signal } from '@angular/core';
import { CoreInputOtp } from '../../core-inputs';
import { _CoreFormItemOtpOptions, CoreFormItemOtpOptions } from '../interfaces/core-form-item-otp-options';

export class CoreFormItemOtp extends CoreInputOtp {
  declare public options: _CoreFormItemOtpOptions;

  constructor(options: CoreFormItemOtpOptions) {
    // Call the parent constructor
    super(options);
    // Append implementation specific options with default values
    this.options = {
      ...this.options,
      kind: signal('otp'),
      name: signal(options.name),
    };
  }
}
