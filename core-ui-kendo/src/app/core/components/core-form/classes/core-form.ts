// Angular
import { FormArray } from '@angular/forms';
import { signal } from '@angular/core';
// Classes
import { CoreInput } from '../../core-inputs/shared/classes/core-input';
import { CoreFormValidators } from './core-form-validators';
import { CoreFormItemText } from './core-form-item-text';
import { CoreFormItemMaskedText } from './core-form-item-masked-text';
import { CoreFormItemTextarea } from './core-form-item-textarea';
import { CoreFormItemNumeric } from './core-form-item-numeric';
import { CoreFormItemPassword } from './core-form-item-password';
import { CoreFormItemSelect } from './core-form-item-select';
import { CoreFormItemMultiselect } from './core-form-item-multiselect';
import { CoreFormItemCheckbox } from './core-form-item-checkbox';
import { CoreFormItemDate } from './core-form-item-date';
import { CoreFormItemDateTime } from './core-form-item-date-time';
import { CoreFormItemSwitch } from './core-form-item-switch';
import { CoreFormItemFileSelect } from './core-form-item-file-select';
// Interfaces
import { CoreFormItem } from '../interfaces/core-form-item';
import { CoreFormOptions } from '../interfaces/core-form-options';
import { CoreFormItemTextOptions } from '../interfaces/core-form-item-text-options';
import { CoreFormItemMaskedTextOptions } from '../interfaces/core-form-item-masked-text-options';
import { CoreFormItemTextareaOptions } from '../interfaces/core-form-item-textarea-options';
import { CoreFormItemNumericOptions } from '../interfaces/core-form-item-numeric-options';
import { CoreFormItemPasswordOptions } from '../interfaces/core-form-item-password-options';
import { CoreFormItemSelectOptions } from '../interfaces/core-form-item-select-options';
import { CoreFormItemMultiselectOptions } from '../interfaces/core-form-item-multiselect-options';
import { CoreFormItemCheckboxOptions } from '../interfaces/core-form-item-checkbox-options';
import { CoreFormItemDateOptions } from '../interfaces/core-form-item-date-options';
import { CoreFormItemDateTimeOptions } from '../interfaces/core-form-item-date-time-options';
import { CoreFormItemSwitchOptions } from '../interfaces/core-form-item-switch-options';
import { CoreFormItemFileSelectOptions } from '../interfaces/core-form-item-file-select-options';
import { CoreFormItemColorPickerOptions } from '../interfaces/core-form-item-color-picker';
import { CoreFormItemColorPicker } from './core-form-item-color-picker';
import { CoreFormItemDropdownSelectOptions } from '../interfaces/core-form-item-dropdown-options';
import { CoreFormItemDropdownSelect } from './core-form-item-drop-down';
import { CoreFormItemEditorOptions } from '../interfaces/core-form-item-editor-options';
import { CoreFormItemEditor } from './core-form-item-editor';
import { CoreFormItemTime } from './core-form-item-time';
import { CoreFormItemTimeOptions } from '../interfaces/core-form-item-time-options';
import { CoreFormItemCronOptions } from '../interfaces/core-form-item-cron-options';
import { CoreFormItemCron } from './core-form-item-cron';
import { CoreFormItemOtpOptions } from '../interfaces/core-form-item-otp-options';
import { CoreFormItemOtp } from './core-form-item-otp';
import { CoreFormItemRadioSelectOptions } from '../interfaces/core-form-item-radio-select-options';
import { CoreFormItemRadioSelect } from './core-form-item-radio-select';
import { CoreFormItemSliderOptions } from '../interfaces/core-form-item-slider-options';
import { CoreFormItemSlider } from './core-form-item-slider';
import { CoreFormItemEmailOptions } from '../interfaces/core-form-item-email-options';
import { CoreFormItemEmail } from './core-form-item-email';
import { CoreFormLayoutGroup } from '../components/core-form-layout-group/core-form-layout-group.interfaces';

export class CoreForm extends FormArray {
  public override controls: (CoreInput & CoreFormItem)[] = [];

  public _layout = signal<CoreFormLayoutGroup | null>(null);

  public focused = signal<boolean>(false);

  constructor(controls: (CoreInput & CoreFormItem)[], options?: CoreFormOptions) {
    super(controls, CoreFormValidators.getValidatorFunctions(options?.validators));
    this.focused.set(options?.focused ?? false);

    // Set global options if provided
    if (options?.labelPosition) {
      controls.forEach((control) => control.options.labelPosition.set(options.labelPosition!));
    }
    if (options?.labelMinWidth) {
      controls.forEach((control) => control.options.labelMinWidth.set(options.labelMinWidth!));
    }
    if (options?.showRequiredIndicator !== undefined) {
      controls.forEach((control) => control.options.showRequiredIndicator.set(options.showRequiredIndicator!));
    }

    // Override the `controls` property with the `CoreFormItem` type
    this.controls = controls;

    if (options?.layout) {
      this.setLayout(options.layout);
    }
  }

  // [ Public methods ]
  public override removeAt(index: number, options?: { emitEvent?: boolean }): void {
    super.removeAt(index, options);
  }

  public setValueAsObject(formValue: Record<string, unknown>): void {
    const formArrayValue = this.controls.map((control) => formValue[control.options.name()]);
    this.setValue(formArrayValue);
  }

  public getValueAsObject<T>(): T {
    const value = this.controls.reduce((acc: Record<string, unknown>, control) => {
      acc[control.options.name()] = control.value ?? null;
      return acc;
    }, {});

    return value as T;
  }

  public getControlByName<T>(name: string): T | null {
    const control = this.controls.find((control) => control.options.name() === name) ?? null;
    return control as T;
  }

  public addControl(control: CoreInput & CoreFormItem): void {
    this.insert(this.controls.length, control);
  }

  public insertControlAfter(name: string, control: CoreInput & CoreFormItem): void {
    const index = this.controls.findIndex((c) => c.options.name() === name);
    if (index !== -1) {
      this.insert(index + 1, control);
    }
  }

  public removeControlByName(name: string): void {
    const index = this.controls.findIndex((c) => c.options.name() === name);
    if (index !== -1) {
      this.removeAt(index);
    }
  }

  // [ Layout Methods ]
  public setLayout(layout: CoreFormLayoutGroup): void {
    if (!this.isLayoutValid(layout)) {
      console.warn('Layout is invalid: Duplicate control names found.');
      return;
    }
    this._layout.set(layout);
  }

  public updateLayout(updater: (layout: CoreFormLayoutGroup) => CoreFormLayoutGroup): void {
    const currentLayout = this._layout();
    if (currentLayout) {
      const updatedLayout = updater(currentLayout);
      if (!this.isLayoutValid(updatedLayout)) {
        console.warn('Updated layout is invalid: Duplicate control names found.');
        return;
      }
      this._layout.set(updatedLayout);
    }
  }

  private isLayoutValid(layout: CoreFormLayoutGroup): boolean {
    // Get all control names from the layout
    const getControlNamesFromLayout = (group: CoreFormLayoutGroup): string[] => {
      const names: string[] = [];

      for (const item of group.items) {
        if (typeof item === 'string') {
          names.push(item);
        } else if (typeof item === 'object') {
          if ('controlName' in item) {
            names.push(item.controlName);
          } else if ('items' in item) {
            names.push(...getControlNamesFromLayout(item));
          }
        }
      }

      return names;
    };

    const controlNames = getControlNamesFromLayout(layout);
    const uniqueNames = new Set(controlNames);

    return controlNames.length === uniqueNames.size;
  }

  public getLayoutGroupByControlName(name: string): CoreFormLayoutGroup | null {
    if (!this._layout()) return null;

    // Recursively search for the layout group which contains CoreFormLayoutInput by controlName or string with same name and returns CoreFormLayoutGroup
    const findGroup = (group: CoreFormLayoutGroup): CoreFormLayoutGroup | null => {
      // Check if this group contains the control we're looking for
      const hasControl = group.items.some(
        (item) =>
          (typeof item === 'string' && item === name) ||
          (typeof item === 'object' && 'controlName' in item && item.controlName === name),
      );

      if (hasControl) {
        return group; // Return the group that contains the control
      }

      // Recursively search in nested groups
      for (const item of group.items) {
        if (typeof item === 'object' && 'items' in item) {
          const found = findGroup(item);
          if (found) {
            return found;
          }
        }
      }

      return null;
    };

    return findGroup(this._layout()!);
  }

  public getFirstControlNameInLayout(): string | null {
    if (!this._layout()) return null;

    // Recursively find the first control name in the layout order
    const findFirstControl = (group: CoreFormLayoutGroup): string | null => {
      for (const item of group.items) {
        if (typeof item === 'string') {
          // Check if this control actually exists in our controls array
          const controlExists = this.controls.some((control) => control.options.name() === item);
          if (controlExists) {
            return item;
          }
        } else if (typeof item === 'object') {
          if ('controlName' in item) {
            // Check if this control actually exists in our controls array
            const controlExists = this.controls.some((control) => control.options.name() === item.controlName);
            if (controlExists) {
              return item.controlName;
            }
          } else if ('items' in item) {
            // Recursively search in nested groups
            const found = findFirstControl(item);
            if (found) {
              return found;
            }
          }
        }
      }
      return null;
    };

    return findFirstControl(this._layout()!);
  }

  // [ Extension methods ]

  public static Text = (options: CoreFormItemTextOptions): CoreFormItemText => new CoreFormItemText(options);
  public static MaskedText = (options: CoreFormItemMaskedTextOptions): CoreFormItemMaskedText =>
    new CoreFormItemMaskedText(options);
  public static Textarea = (options: CoreFormItemTextareaOptions): CoreFormItemTextarea =>
    new CoreFormItemTextarea(options);
  public static Numeric = (options: CoreFormItemNumericOptions): CoreFormItemNumeric =>
    new CoreFormItemNumeric(options);
  public static Password = (options: CoreFormItemPasswordOptions): CoreFormItemPassword =>
    new CoreFormItemPassword(options);
  public static Select = (options: CoreFormItemSelectOptions): CoreFormItemSelect => new CoreFormItemSelect(options);
  public static Multiselect = (options: CoreFormItemMultiselectOptions): CoreFormItemMultiselect =>
    new CoreFormItemMultiselect(options);
  public static Checkbox = (options: CoreFormItemCheckboxOptions): CoreFormItemCheckbox =>
    new CoreFormItemCheckbox(options);
  public static Date = (options: CoreFormItemDateOptions): CoreFormItemDate => new CoreFormItemDate(options);
  public static DateTime = (options: CoreFormItemDateTimeOptions): CoreFormItemDateTime =>
    new CoreFormItemDateTime(options);
  public static Time = (options: CoreFormItemTimeOptions): CoreFormItemTime => new CoreFormItemTime(options);
  public static Switch = (options: CoreFormItemSwitchOptions): CoreFormItemSwitch => new CoreFormItemSwitch(options);
  public static FileSelect = (options: CoreFormItemFileSelectOptions): CoreFormItemFileSelect =>
    new CoreFormItemFileSelect(options);
  public static ColorPicker = (options: CoreFormItemColorPickerOptions): CoreFormItemColorPicker =>
    new CoreFormItemColorPicker(options);
  public static DropdownSelect = (options: CoreFormItemDropdownSelectOptions): CoreFormItemDropdownSelect =>
    new CoreFormItemDropdownSelect(options);
  public static TextEditor = (options: CoreFormItemEditorOptions): CoreFormItemEditor =>
    new CoreFormItemEditor(options);
  public static Cron = (options: CoreFormItemCronOptions): CoreFormItemCron => new CoreFormItemCron(options);
  public static Otp = (options: CoreFormItemOtpOptions): CoreFormItemOtp => new CoreFormItemOtp(options);
  public static RadioSelect = (options: CoreFormItemRadioSelectOptions): CoreFormItemRadioSelect =>
    new CoreFormItemRadioSelect(options);
  public static Slider = (options: CoreFormItemSliderOptions): CoreFormItemSlider => new CoreFormItemSlider(options);
  public static Email = (options: CoreFormItemEmailOptions): CoreFormItemEmail => new CoreFormItemEmail(options);
}
