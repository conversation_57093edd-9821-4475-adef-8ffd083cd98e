// Angular
import { Signal } from '@angular/core';
// Interfaces
import { CoreInputSliderOptions } from '../../core-inputs';
import { _CoreInputSliderOptions } from '../../core-inputs/core-input-slider/core-input-slider.interfaces';

export interface CoreFormItemSliderOptions extends CoreInputSliderOptions {
  name: string;
}

export interface _CoreFormItemSliderOptions extends _CoreInputSliderOptions {
  kind: Signal<'slider'>;
  name: Signal<string>;
}
