import { Signal } from '@angular/core';
import { CoreInputOtpOptions } from '../../core-inputs';
import { _CoreInputOtpOptions } from '../../core-inputs/core-input-otp/core-input-otp.interfaces';

export interface CoreFormItemOtpOptions extends CoreInputOtpOptions {
  name: string;
}

export interface _CoreFormItemOtpOptions extends _CoreInputOtpOptions {
  kind: Signal<'otp'>;
  name: Signal<string>;
}
