import { Signal } from '@angular/core';
import { CoreInputRadioSelectOptions } from '../../core-inputs';
import { _CoreInputRadioSelectOptions } from '../../core-inputs/core-input-radio-select/core-input-radio-select.interfaces';

export interface CoreFormItemRadioSelectOptions extends CoreInputRadioSelectOptions {
  name: string;
}

export interface _CoreFormItemRadioSelectOptions extends _CoreInputRadioSelectOptions {
  kind: Signal<'radioSelect'>;
  name: Signal<string>;
}
