@if (form()._layout()) {
  <core-form-layout-group
    [layoutGroup]="form()._layout()!"
    [controls]="form().controls"
    [firstControlName]="form().focused() ? form().getFirstControlNameInLayout() : null"
  />
} @else {
  @for (control of form().controls; track control.options.name(); let index = $index) {
    <core-form-layout-input [control]="control" [initialFocused]="form().focused() && index === 0" />
  }
}
