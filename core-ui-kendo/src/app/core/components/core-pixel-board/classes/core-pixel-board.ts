// Angular
import { computed, signal, WritableSignal } from '@angular/core';
import { Subject } from 'rxjs';
// Interfaces
import { CorePixelBoardOptions } from '../interfaces/core-pixel-board-options';
import { CorePixelBoardSize } from '../interfaces/core-pixel-board-size';
import { CorePixelDefault } from '../interfaces/core-pixel-default';
import { CorePixel } from '../interfaces/core-pixel';
import { CorePixelEvent } from '../interfaces/core-pixel-event';

export class CorePixelBoard<T = unknown> {
  // [ Public properties ]
  public size = computed(() => this._size());
  public defaultPixel = computed(() => this._defaultPixel());
  public backgroundColor = computed(() => this._backgroundColor());
  public padding = computed(() => this._padding());
  public gap = computed(() => this._gap());

  // [ Public events ]
  public onPixelClick = new Subject<CorePixelEvent<T>>();
  public onPixelMouseDown = new Subject<CorePixelEvent<T>>();
  public onPixelMouseMove = new Subject<CorePixelEvent<T>>();
  public onPixelMouseUp = new Subject<CorePixelEvent<T>>();

  // [ Public methods ]
  public updatePixels(pixels: CorePixel<T>[]): void {
    this._onUpdatePixels.next(pixels);
  }

  public refresh(): void {
    this._onRefresh.next();
  }

  public getPixels(): CorePixel<T>[] {
    return Array.from(this._activePixelsMap().values()).map((pixel) => ({
      row: pixel.row,
      column: pixel.column,
      color: pixel.color,
      value: pixel.value,
    }));
  }

  public getPixel(row: number, column: number, defaultIfNotFound = false): CorePixel<T> | null {
    const pixel = this._activePixelsMap().get(`${row}-${column}`);
    if (pixel) {
      return {
        row: pixel.row,
        column: pixel.column,
        color: pixel.color,
        value: pixel.value,
      };
    } else if (defaultIfNotFound) {
      return {
        row,
        column,
        color: this.defaultPixel().color,
        value: this.defaultPixel().value,
      };
    }
    return null;
  }

  public clear(): void {
    this._activePixelsMap.set(new Map<string, CorePixel<T>>());
    this._onRefresh.next();
  }

  // [ Internal properties ]
  public _size: WritableSignal<CorePixelBoardSize>;
  public _defaultPixel: WritableSignal<CorePixelDefault<T>>;
  public _activePixelsMap: WritableSignal<Map<string, CorePixel<T>>>;
  public _backgroundColor: WritableSignal<string>;
  public _padding: WritableSignal<number>;
  public _gap: WritableSignal<number>;

  // [ Internal events ]
  public _onPixelClick?: (pixel: CorePixel<T>, pixelBoard: CorePixelBoard<T>) => void | Promise<void>;
  public _onPixelMouseDown?: (pixel: CorePixel<T>, pixelBoard: CorePixelBoard<T>) => void | Promise<void>;
  public _onPixelMouseMove?: (pixel: CorePixel<T>, pixelBoard: CorePixelBoard<T>) => void | Promise<void>;
  public _onPixelMouseUp?: (pixel: CorePixel<T>, pixelBoard: CorePixelBoard<T>) => void | Promise<void>;
  public _onUpdatePixels = new Subject<CorePixel<T>[]>();
  public _onRefresh = new Subject<void>();

  constructor(options: CorePixelBoardOptions<T>) {
    this._size = signal({ rows: options.rows, columns: options.columns });
    this._defaultPixel = signal(options.defaultPixel);
    this._activePixelsMap = signal(this._convertToPixelMap(options.pixels));
    this._backgroundColor = signal(options.backgroundColor ?? '#434343');
    this._padding = signal(options.padding ?? 4);
    this._gap = signal(options.gap ?? 0);

    this._onPixelClick = options.onPixelClick;
    this._onPixelMouseDown = options.onPixelMouseDown;
    this._onPixelMouseMove = options.onPixelMouseMove;
    this._onPixelMouseUp = options.onPixelMouseUp;
  }

  // [ Private methods ]
  private _convertToPixelMap(pixels?: CorePixel<T>[]): Map<string, CorePixel<T>> {
    if (!pixels) {
      return new Map<string, CorePixel<T>>();
    }

    return pixels.reduce((acc, pixel) => {
      const key = `${pixel.row}-${pixel.column}`;
      acc.set(key, {
        row: pixel.row,
        column: pixel.column,
        color: pixel.color,
        value: pixel.value,
      });
      return acc;
    }, new Map<string, CorePixel<T>>());
  }
}
