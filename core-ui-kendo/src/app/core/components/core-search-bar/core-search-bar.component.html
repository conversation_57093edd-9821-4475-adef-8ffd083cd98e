<!-- eslint-disable @angular-eslint/template/elements-content -->
<kendo-textbox
  class="container"
  coreValueChanged
  [formControl]="searchBar()"
  [placeholder]="searchBar().options.placeholder()"
  [clearButton]="true"
  [valueChangeDelay]="searchBar().options.valueChangedDelay()"
  (valueChanged)="_onValueChanged()"
  (keydown.enter)="_onSearch()"
>
  <ng-template kendoTextBoxSuffixTemplate>
    <div class="search-actions">
      <!-- Search Button -->
      <button
        kendoButton
        [ngClass]="
          searchBar().options.showBarCodeReader() || showAdvancedFilter() ? 'button-flat' : 'button-rounded-right'
        "
        [svgIcon]="searchIcon"
        [disabled]="searchBar().disabled"
        (click)="_onSearch()"
      >
        {{ searchBar().options.searchLabel() }}
      </button>

      <!-- CodeReader -->
      @if (searchBar().options.showBarCodeReader()) {
        <button
          kendoButton
          [ngClass]="showAdvancedFilter() ? 'button-flat' : 'button-rounded-right'"
          [svgIcon]="barcodeIcon"
          [disabled]="searchBar().disabled"
          (click)="_onBarcodeClick()"
        >
          {{ searchBar().options.searchLabel() }}
        </button>
      }
      <!-- Advanced Filter Button -->
      @if (showAdvancedFilter()) {
        <button
          kendoButton
          class="button-rounded-right"
          [themeColor]="advancedFilterColor()"
          [svgIcon]="advancedFilterIcon()"
          [disabled]="searchBar().disabled"
          (click)="_onAdvancedFilterClick()"
        ></button>
      }
    </div>
  </ng-template>
</kendo-textbox>
