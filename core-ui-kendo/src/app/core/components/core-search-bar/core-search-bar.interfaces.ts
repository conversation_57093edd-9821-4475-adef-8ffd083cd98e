// import { _CoreInputOptions } from '../core-inputs/shared/interfaces/core-input-options.interface';
import { Signal } from '@angular/core';
import { CoreDialogAdvancedFilterOptions } from './dialogs/core-dialog-advanced-filter-component/core-dialog-advanced-filter.interfaces';
import { CoreFilterGroup } from '../../shared/interfaces/core-filter.interfaces';

export interface CoreSearchBarOptions {
  searchLabel?: string; // Default: ''
  value?: string; // Default: ''
  placeholder?: string; // Default: 'Search...'
  disabled?: boolean; // Default: false
  showBarCodeReader?: boolean; // Default: false
  advancedFilterOptions?: CoreDialogAdvancedFilterOptions;
  disableOnCallback?: boolean; // Default: false
  valueChangedDelay?: number; // Default: 400
  onSearchValueChange?: (value: string | null) => void | Promise<void>;
  onSearchValueChanged?: (value: string | null) => void | Promise<void>;
  onSearch?: (value: string | null) => void | Promise<void>;
  onApplyAdvancedFilters?: (filters: CoreFilterGroup | null) => void | Promise<void>;
}

export interface _CoreSearchBarOptions {
  searchLabel: Signal<string>;
  placeholder: Signal<string>;
  showBarCodeReader: Signal<boolean>;
  advancedFilterOptions: Signal<CoreDialogAdvancedFilterOptions | null>;
  valueChangedDelay: Signal<number>;
  disableOnCallback: Signal<boolean>;
}
