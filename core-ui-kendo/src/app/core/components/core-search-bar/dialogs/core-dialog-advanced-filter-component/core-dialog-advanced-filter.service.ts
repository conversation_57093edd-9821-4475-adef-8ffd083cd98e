// Angular
import { Injectable, inject, signal } from '@angular/core';
import { DialogResult, DialogService } from '@progress/kendo-angular-dialog';
// RxJS
import { firstValueFrom } from 'rxjs';
import { first } from 'rxjs/operators';
// Core
import {
  CoreTableColumn,
  CoreTableColumnNumber,
  CoreTableColumnDate,
  CoreTableColumnDateTime,
  CoreTableColumnBoolean,
  CoreTableColumnSwitch,
} from '../../../core-table';
// Shared
import { CoreFilterExpression } from '../../../../shared/interfaces/core-filter-expression.interfaces';
// Components
import { CoreDialogAdvancedFilterComponent } from './core-dialog-advanced-filter.component';
// Interfaces
import {
  _CoreDialogAdvancedFilterOptions,
  CoreDialogAdvancedFilterOptions,
  CoreDialogAdvancedFilterOutput,
} from './core-dialog-advanced-filter.interfaces';
// Constants
import { CORE_DEFAULT_FILTER } from './core-dialog-advanced-filter.constants';

@Injectable()
export class CoreDialogAdvancedFilterService {
  private _dialog = inject(DialogService);

  public async open(options: CoreDialogAdvancedFilterOptions): Promise<CoreDialogAdvancedFilterOutput> {
    const dialogOptions: _CoreDialogAdvancedFilterOptions = {
      filters: signal(this._toCoreFilterExpression(options.columns)),
      value: signal(options.value ?? CORE_DEFAULT_FILTER),
    };

    const idealWidth = 900;
    const idealHeight = 600;

    const dialogWidth = window.innerWidth > idealWidth ? idealWidth : Math.floor(window.innerWidth);
    const dialogHeight = window.innerHeight > idealHeight ? idealHeight : Math.floor(window.innerHeight * 0.95);

    // Generate dialog popup
    const dialogRef = this._dialog.open({
      title: 'Advanced Filter',
      content: CoreDialogAdvancedFilterComponent,
      minHeight: Math.min(dialogHeight, 500),
      maxHeight: Math.max(dialogHeight, 800),
      minWidth: Math.min(dialogWidth, 800),
      maxWidth: Math.max(dialogWidth, 1000),
    });

    const dialog = dialogRef.content.instance as CoreDialogAdvancedFilterComponent;
    dialog.options.set(dialogOptions);

    // Get result of opened dialog
    const result: DialogResult = await firstValueFrom(dialogRef.result.pipe(first()));
    return result as CoreDialogAdvancedFilterOutput;
  }

  private _toCoreFilterExpression(columns: CoreTableColumn[]): CoreFilterExpression[] {
    return columns
      .filter((column) => column.filter && column.type !== 'actions')
      .map((column): CoreFilterExpression => {
        // Check if any column has filterOptions to make it an enum
        if (column.filterOptions && column.filterOptions.length > 0) {
          return {
            field: column.filterField ?? column.field,
            title: column.label,
            editor: column.filter === 'multiselect' ? 'multiselect' : 'select',
            values: column.filterOptions.map((option) => ({
              value: String(option.value),
              label: option.label,
            })),
          };
        }

        // Add type-specific properties
        switch (column.type) {
          case 'number':
            return {
              field: column.filterField ?? column.field,
              title: column.label,
              editor: 'number',
              format: (column as CoreTableColumnNumber).format ?? 'n2',
            };
          case 'date':
            return {
              field: column.filterField ?? column.field,
              title: column.label,
              editor: 'date',
              format: (column as CoreTableColumnDate).format ?? 'MM/dd/yyyy',
            };
          case 'datetime':
            return {
              field: column.filterField ?? column.field,
              title: column.label,
              editor: 'datetime',
              format: (column as CoreTableColumnDateTime).format ?? 'MM/dd/yyyy HH:mm',
            };
          case 'boolean':
            return {
              field: column.filterField ?? column.field,
              title: column.label,
              editor: 'boolean',
              trueLabel: (column as CoreTableColumnBoolean).trueLabel ?? 'Yes',
              falseLabel: (column as CoreTableColumnBoolean).falseLabel ?? 'No',
            };
          case 'switch':
            return {
              field: column.filterField ?? column.field,
              title: column.label,
              editor: 'boolean',
              trueLabel: (column as CoreTableColumnSwitch<{ id: string | number }>).trueLabel ?? 'Active',
              falseLabel: (column as CoreTableColumnSwitch<{ id: string | number }>).falseLabel ?? 'Inactive',
            };
          default:
            return {
              field: column.filterField ?? column.field,
              title: column.label,
              editor: 'string',
            };
        }
      });
  }
}
