<kendo-filter #filter kendoTooltip [value]="_value()" (valueChange)="_onValueChange($event)">
  @for (filterExpression of options()?.filters() ?? []; track filterExpression.field) {
    @switch (filterExpression.editor) {
      @case ("string") {
        <kendo-filter-field
          [field]="filterExpression.field"
          [title]="filterExpression.title ?? filterExpression.field"
          [operators]="['eq', 'neq', 'contains', 'doesnotcontain', 'startswith', 'endswith']"
          editor="string"
        />
      }
      @case ("number") {
        <kendo-filter-field
          [field]="filterExpression.field"
          [title]="filterExpression.title ?? filterExpression.field"
          [operators]="['eq', 'neq', 'gt', 'gte', 'lt', 'lte']"
          editor="number"
          [editorFormat]="filterExpression.format"
        />
      }
      @case ("boolean") {
        <kendo-filter-field
          [field]="filterExpression.field"
          [title]="filterExpression.title ?? filterExpression.field"
          editor="boolean"
        >
          <ng-template kendoFilterValueEditorTemplate let-currentItem>
            <kendo-dropdownlist
              textField="label"
              valueField="value"
              [data]="[
                {
                  value: true,
                  label: filterExpression.trueLabel ?? 'Yes',
                },
                {
                  value: false,
                  label: filterExpression.falseLabel ?? 'No',
                },
              ]"
              [(ngModel)]="currentItem.value"
              (ngModelChange)="_onValueChange(filter.value)"
            />
          </ng-template>
        </kendo-filter-field>
      }
      @case ("date") {
        <kendo-filter-field
          [field]="filterExpression.field"
          [title]="filterExpression.title ?? filterExpression.field"
          [operators]="['eq', 'neq', 'gt', 'gte', 'lt', 'lte']"
          editor="date"
          [editorFormat]="filterExpression.format"
        />
      }
      @case ("datetime") {
        <kendo-filter-field
          [field]="filterExpression.field"
          [title]="filterExpression.title ?? filterExpression.field"
          [operators]="['eq', 'neq', 'gt', 'gte', 'lt', 'lte']"
          editor="date"
        >
          <ng-template kendoFilterValueEditorTemplate let-currentItem>
            <kendo-datetimepicker
              [format]="filterExpression.format ?? 'MM/dd/yyyy HH:mm'"
              [disabled]="currentItem.operator === 'isnull' || currentItem.operator === 'isnotnull'"
              [(ngModel)]="currentItem.value"
              (ngModelChange)="_onValueChange(filter.value)"
            />
          </ng-template>
        </kendo-filter-field>
      }
      @case ("select") {
        <kendo-filter-field
          [field]="filterExpression.field"
          [title]="filterExpression.title ?? filterExpression.field"
          [operators]="['eq', 'neq']"
          editor="string"
        >
          <ng-template kendoFilterValueEditorTemplate let-currentItem>
            <kendo-dropdownlist
              [disabled]="currentItem.operator === 'isnull' || currentItem.operator === 'isnotnull'"
              textField="label"
              valueField="value"
              [data]="filterExpression.values"
              [(ngModel)]="currentItem.value"
              (ngModelChange)="_onValueChange(filter.value)"
            />
          </ng-template>
        </kendo-filter-field>
      }
      @case ("multiselect") {
        <kendo-filter-field
          [field]="filterExpression.field"
          [title]="filterExpression.title ?? filterExpression.field"
          [operators]="['contains', 'doesnotcontain']"
          editor="string"
        >
          <ng-template kendoFilterValueEditorTemplate let-currentItem>
            <kendo-multiselect
              [style]="{ 'min-width': '140px' }"
              textField="label"
              valueField="value"
              [data]="filterExpression.values"
              [valuePrimitive]="true"
              [kendoDropDownFilter]="{ operator: 'contains', caseSensitive: false }"
              [(ngModel)]="currentItem.value"
              (ngModelChange)="_onValueChange(filter.value)"
            />
          </ng-template>
        </kendo-filter-field>
      }
    }
  }
</kendo-filter>

<kendo-dialog-actions [layout]="'start'">
  <!-- Cancel Button -->
  <core-button [label]="'Cancel'" (click)="_onCancel()" />
  <!-- Reset Filters Button -->
  <core-button [label]="'Reset Filters'" [color]="'default'" (click)="_onResetFilters()" />
  <!-- Apply Filters Button -->
  <core-button [label]="'Apply Filters'" class="apply-filters-btn" [color]="'primary'" (click)="_onApplyFilters()" />
</kendo-dialog-actions>
