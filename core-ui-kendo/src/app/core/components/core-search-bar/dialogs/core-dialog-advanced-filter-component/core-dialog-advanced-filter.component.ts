/* eslint-disable @typescript-eslint/no-unsafe-call */

// Angular
import { Component, computed, signal, inject, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
// Kendo UI
import { KENDO_FILTER } from '@progress/kendo-angular-filter';
import { KENDO_DATETIMEPICKER } from '@progress/kendo-angular-dateinputs';
import { KENDO_DROPDOWNS } from '@progress/kendo-angular-dropdowns';
import { CompositeFilterDescriptor } from '@progress/kendo-data-query';
import { DialogModule, DialogContentBase, DialogRef } from '@progress/kendo-angular-dialog';
import { TooltipDirective } from '@progress/kendo-angular-tooltip';
// Core
import { CoreButtonModule } from '../../../core-button';
// Shared
import { CoreFilterGroup } from '../../../../shared/interfaces/core-filter.interfaces';
// Helpers
import { CoreKendoFilterTypeConverter } from '../../../../shared/helpers/core-kendo-filter-type-converter';
// Interfaces
import { _CoreDialogAdvancedFilterOptions } from './core-dialog-advanced-filter.interfaces';
// Constants
import { CORE_DEFAULT_FILTER } from './core-dialog-advanced-filter.constants';

@Component({
  selector: 'core-dialog-advanced-filter-component',
  templateUrl: './core-dialog-advanced-filter.component.html',
  styleUrl: './core-dialog-advanced-filter.component.scss',
  imports: [
    FormsModule,
    KENDO_FILTER,
    KENDO_DROPDOWNS,
    KENDO_DATETIMEPICKER,
    DialogModule,
    CoreButtonModule,
    TooltipDirective,
  ],
})
export class CoreDialogAdvancedFilterComponent extends DialogContentBase implements OnInit {
  // [ Dependencies ]
  public _dialogRef = inject(DialogRef);

  // [ Signals ]
  public options = signal<_CoreDialogAdvancedFilterOptions | null>(null);
  private _filters = signal<CompositeFilterDescriptor | null>(null);

  // [ Computed ]
  public _value = computed<CompositeFilterDescriptor>(() => {
    const value = this.options()?.value();
    if (!value) return { logic: 'and', filters: [] };

    const kendoFilters = CoreKendoFilterTypeConverter.toKendoFilterGroup(value);
    if (!kendoFilters || !('filters' in kendoFilters)) return { logic: 'and', filters: [] };

    return kendoFilters;
  });

  // [ Lifecycle Hooks ]
  public ngOnInit(): void {
    this._filters.set(this._value());
  }

  // [ Events ]
  public _onValueChange(value: CompositeFilterDescriptor): void {
    this._filters.set(value);
  }

  public _onApplyFilters(): void {
    this._dialogRef.close({
      action: 'apply',
      filters: CoreKendoFilterTypeConverter.fromKendoFilterGroup(this._filters() ?? undefined),
    });
  }

  public _onResetFilters(): void {
    this.options.update((options) => {
      return {
        filters: options!.filters,
        value: signal<CoreFilterGroup>(CORE_DEFAULT_FILTER),
      };
    });

    this._filters.set({ logic: 'and', filters: [] });
  }

  public _onCancel(): void {
    this._dialogRef.close({ action: 'cancel' });
  }
}
