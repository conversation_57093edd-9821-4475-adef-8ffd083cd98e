import { Signal } from '@angular/core';
import { CoreTableColumn } from '../../../core-table';
import { CoreFilterExpression } from '../../../../shared/interfaces/core-filter-expression.interfaces';
import { CoreFilterGroup } from '../../../../shared/interfaces/core-filter.interfaces';

export interface CoreDialogAdvancedFilterOptions {
  columns: CoreTableColumn[];
  value?: CoreFilterGroup;
}

export interface _CoreDialogAdvancedFilterOptions {
  filters: Signal<CoreFilterExpression[]>;
  value: Signal<CoreFilterGroup>;
}

export interface CoreDialogAdvancedFilterOutput {
  action: 'apply' | 'cancel';
  filters?: CoreFilterGroup;
}
