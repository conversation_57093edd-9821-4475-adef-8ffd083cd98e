/* eslint-disable @typescript-eslint/no-unsafe-call */

// Angular
import { AfterViewInit, Component, ElementRef, inject, OnD<PERSON>roy, viewChild } from '@angular/core';
// ZXing
import { BrowserMultiFormatReader } from '@zxing/library/esm/browser/BrowserMultiFormatReader';
import BarcodeFormat from '@zxing/library/esm/core/BarcodeFormat';
import DecodeHintType from '@zxing/library/esm/core/DecodeHintType';
// Kendo UI
import { KENDO_DROPDOWNLIST } from '@progress/kendo-angular-dropdowns';
import { DialogRef } from '@progress/kendo-angular-dialog';
// Interfaces
import { MediaSourceInfo } from './core-dialog-barcode-reader.interfaces';

@Component({
  selector: 'core-dialog-barcode-reader',
  templateUrl: './core-dialog-barcode-reader.component.html',
  styleUrls: ['./core-dialog-barcode-reader.component.scss'],
  imports: [KENDO_DROPDOWNLIST],
})
export class DialogBarcodeReaderComponent implements AfterViewInit, OnDestroy {
  private _dialogRef = inject(DialogRef);

  public output = viewChild.required<ElementRef<HTMLVideoElement>>('output');

  public sources: MediaSourceInfo[] = [];
  public source!: MediaSourceInfo;
  public codeReader!: BrowserMultiFormatReader;

  private stream!: MediaStream;

  public ngAfterViewInit(): void {
    void this.initCamera();
  }

  private async initCamera(): Promise<void> {
    // Get camera sources
    this.sources = await this.getVideoSources();

    // Set default source if available
    if (this.sources.length > 0) {
      this.source = this.sources[0];
      await this.useSelectedStream();
    }
  }

  public ngOnDestroy() {
    this.closeStream();
  }

  // [ Template functions ]
  public async useSelectedStream(): Promise<void> {
    // Close existing stream
    this.closeStream();

    // Open new stream
    const constrains: MediaStreamConstraints = {
      video: {
        deviceId: this.source.id,
        facingMode: 'environment',
      },
    };
    this.stream = await navigator.mediaDevices.getUserMedia(constrains);
    this.output().nativeElement.srcObject = this.stream;

    // Configure the reader
    const hints = new Map();
    const formats = [
      BarcodeFormat.AZTEC,
      BarcodeFormat.CODE_39,
      BarcodeFormat.CODE_128,
      BarcodeFormat.DATA_MATRIX,
      BarcodeFormat.EAN_8,
      BarcodeFormat.EAN_13,
      BarcodeFormat.ITF,
      BarcodeFormat.PDF_417,
      BarcodeFormat.QR_CODE,
      // BarcodeFormat.UPC_A,
      // BarcodeFormat.UPC_E
    ];
    hints.set(DecodeHintType.POSSIBLE_FORMATS, formats);

    this.codeReader = new BrowserMultiFormatReader(hints);
    await this.codeReader.decodeFromVideoDevice(this.source.id, this.output().nativeElement, (result) => {
      if (result) {
        this._dialogRef.close({ code: result.getText() });
      }
    });
  }

  // [ Internal functions ]
  private closeStream(): void {
    if (this.stream != null) {
      this.stream.getTracks().forEach((track) => track.stop());
      this.codeReader.reset();
    }
  }

  private async getVideoSources(): Promise<MediaSourceInfo[]> {
    const allDevices = await navigator.mediaDevices.enumerateDevices();
    return allDevices
      .filter((d) => d.kind === 'videoinput')
      .map((d, i) => {
        return {
          id: d.deviceId,
          name: d.label || `Camera ${i + 1}`,
        };
      });
  }
}
