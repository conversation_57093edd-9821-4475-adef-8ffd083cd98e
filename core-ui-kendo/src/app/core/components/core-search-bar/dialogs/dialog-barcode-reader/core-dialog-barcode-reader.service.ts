// Angular
import { inject, Injectable } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { first } from 'rxjs/operators';
// Kendo UI
import { DialogCloseResult, DialogResult, DialogService } from '@progress/kendo-angular-dialog';
// Components
import { DialogBarcodeReaderComponent } from './core-dialog-barcode-reader.component';

@Injectable()
export class CoreDialogBarcodeReaderService {
  private _dialog = inject(DialogService);

  public async open(): Promise<string> {
    // Generate dialog popup
    const dialog = this._dialog.open({
      title: 'Barcode Scanner',
      content: DialogBarcodeReaderComponent,
      width: '95vw',
      maxWidth: '1920px',
      height: '95vh',
      maxHeight: '1080px',
    });

    // Get result of opened dialog
    const result: DialogResult = await firstValueFrom(dialog.result.pipe(first()));

    if (result instanceof DialogCloseResult) {
      return 'cancel';
    } else if (result && typeof result === 'object' && 'code' in result) {
      return (result as { code: string }).code || '';
    }

    return '';
  }
}
