.view-container {
  position: absolute !important;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.container {
  background-color: #000000;
  overflow: hidden;
}

.camera-list {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 300px;
}

.output {
  position: absolute;
  top: 50%;
  left: 50%;
  min-height: 100%;
  min-width: 100%;
  transform: translate(-50%, -50%);
}

// Focus square styles
.frame {
  position: absolute;
  width: 400px;
  height: 400px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;

  --frame-color: var(--kendo-color-secondary, #347b5f);

  &-corner {
    position: absolute;
    // length of each corner leg
    width: 20px;
    height: 20px;
    box-sizing: content-box;

    &.--top-left {
      top: 0;
      left: 0;
      border-top: 4px solid var(--frame-color);
      border-left: 4px solid var(--frame-color);
    }

    &.--top-right {
      top: 0;
      right: 0;
      border-top: 4px solid var(--frame-color);
      border-right: 4px solid var(--frame-color);
    }

    &.--bottom-left {
      bottom: 0;
      left: 0;
      border-bottom: 4px solid var(--frame-color);
      border-left: 4px solid var(--frame-color);
    }

    &.--bottom-right {
      bottom: 0;
      right: 0;
      border-bottom: 4px solid var(--frame-color);
      border-right: 4px solid var(--frame-color);
    }
  }
}

@media only screen and (max-width: 576px) {
  .frame {
    width: 200px;
    height: 200px;
  }
}
