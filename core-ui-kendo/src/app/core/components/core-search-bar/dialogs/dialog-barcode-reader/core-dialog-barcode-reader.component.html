<div class="view-container container">
  <video #output autoplay muted playsinline class="output"></video>

  @if (sources && sources.length > 1) {
    <kendo-dropdownlist
      [data]="sources"
      [valueField]="'id'"
      [textField]="'name'"
      (valueChange)="useSelectedStream()"
      class="camera-list"
    />
  }

  <div class="frame">
    <span class="frame-corner --top-left"></span>
    <span class="frame-corner --top-right"></span>
    <span class="frame-corner --bottom-left"></span>
    <span class="frame-corner --bottom-right"></span>
  </div>
</div>
