// Angular
import { Component, computed, inject, input, OnD<PERSON>roy, OnInit, output, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
// RxJS
import { Subscription } from 'rxjs';
// Kendo UI
import { TextBoxModule } from '@progress/kendo-angular-inputs';
// Core
import { ButtonModule } from '@progress/kendo-angular-buttons';
import { CoreSearchBar } from './core-search-bar';
import { FilterProcessingHelper } from '../../shared/helpers/core-filter-processing.helper';
// Services
import { CoreDialogBarcodeReaderService } from './dialogs/dialog-barcode-reader/core-dialog-barcode-reader.service';
import { CoreDialogAdvancedFilterService } from './dialogs/core-dialog-advanced-filter-component/core-dialog-advanced-filter.service';
// Icons
import { searchIcon, qrCodeIcon, filterIcon, filterClearIcon, SVGIcon } from '@progress/kendo-svg-icons';
// Interfaces
import { CoreDialogAdvancedFilterOptions } from './dialogs/core-dialog-advanced-filter-component/core-dialog-advanced-filter.interfaces';
// Constants
import { CORE_DEFAULT_FILTER } from './dialogs/core-dialog-advanced-filter-component/core-dialog-advanced-filter.constants';
// Shared
import { CoreFilterGroup } from '../../shared/interfaces/core-filter.interfaces';
import { ValueChangedDirective } from '../../shared/directives/value-changed.directive';

@Component({
  selector: 'core-search-bar',
  templateUrl: './core-search-bar.component.html',
  styleUrl: './core-search-bar.component.scss',
  imports: [CommonModule, TextBoxModule, ReactiveFormsModule, ButtonModule, ValueChangedDirective],
  providers: [CoreDialogBarcodeReaderService, CoreDialogAdvancedFilterService],
})
export class CoreSearchBarComponent implements OnInit, OnDestroy {
  // [ Dependencies ]
  private _dialogBarcodeReader = inject(CoreDialogBarcodeReaderService);
  private _dialogAdvancedFilter = inject(CoreDialogAdvancedFilterService);

  // [ Inputs ]
  public searchBar = input.required<CoreSearchBar>();

  // [ Signals ]
  private _lastAdvancedFilterValue = signal<CoreFilterGroup | null | undefined>(undefined);

  // [ Computed ]
  public showAdvancedFilter = computed<boolean>(() => {
    const filters = this.searchBar().options.advancedFilterOptions()?.columns;
    return filters ? filters?.length > 0 : false;
  });

  private _advancedFilterOptions = computed<CoreDialogAdvancedFilterOptions | null>(() => {
    const options = this.searchBar().options.advancedFilterOptions();
    if (!options) return null;

    const value =
      this._lastAdvancedFilterValue() === undefined
        ? (options.value ?? CORE_DEFAULT_FILTER)
        : this._lastAdvancedFilterValue();

    return {
      columns: options.columns,
      value: value!,
    };
  });

  private _isAdvancedFilterEmpty = computed<boolean>(() => {
    const isDefaultFilter =
      this._advancedFilterOptions()!.value === CORE_DEFAULT_FILTER || this._advancedFilterOptions()!.value == null;
    const isLastValueEmpty = this._lastAdvancedFilterValue() == null;

    return isDefaultFilter && isLastValueEmpty;
  });

  public advancedFilterIcon = computed<SVGIcon>(() => (this._isAdvancedFilterEmpty() ? filterIcon : filterClearIcon));
  public advancedFilterColor = computed<'secondary' | 'warning'>(() =>
    this._isAdvancedFilterEmpty() ? 'secondary' : 'warning',
  );

  // [ Outputs ]
  public searchKeyPressed = output<string>();

  // [ Icons ]
  public searchIcon = searchIcon;
  public barcodeIcon = qrCodeIcon;

  // [ Subscriptions ]
  private _subscriptions$ = new Subscription();

  // [ Lifecycle Hooks ]
  public ngOnInit(): void {
    this._subscriptions$.add(
      this.searchBar().valueChanges.subscribe(() => {
        void this._onValueChange();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  // [ Events ]
  public async _onValueChange(): Promise<void> {
    const value = this.searchBar().value as string | null;
    const disableOnCallback = this.searchBar().options.disableOnCallback();

    if (this.searchBar().onValueChange) {
      if (disableOnCallback) this.searchBar().disable({ emitEvent: false });
      await this.searchBar().onValueChange!(value);
      if (disableOnCallback) this.searchBar().enable({ emitEvent: false });
    }
  }

  public async _onValueChanged(): Promise<void> {
    const value = this.searchBar().value as string | null;
    const disableOnCallback = this.searchBar().options.disableOnCallback();

    // Call the onValueChanged callback
    if (this.searchBar().onValueChanged) {
      if (disableOnCallback) this.searchBar().disable({ emitEvent: false });
      await this.searchBar().onValueChanged!(value);
      if (disableOnCallback) this.searchBar().enable({ emitEvent: false });
    }

    // Emit the valueChanged event
    this.searchBar().valueChanged$.next(value);
  }

  public async _onSearch(): Promise<void> {
    const value = this.searchBar().value as string | null;
    const disableOnCallback = this.searchBar().options.disableOnCallback();

    // Call the onSearch callback
    if (this.searchBar().onSearch) {
      if (disableOnCallback) this.searchBar().disable({ emitEvent: false });
      await this.searchBar().onSearch!(value);
      if (disableOnCallback) this.searchBar().enable({ emitEvent: false });
    }

    // Emit the search event
    this.searchBar().search$.next(value);
  }

  public async _onBarcodeClick(): Promise<void> {
    const code = await this._dialogBarcodeReader.open();

    if (code !== 'cancel' && code) {
      this.searchBar().setValue(code);

      await this._onValueChanged();
      await this._onSearch();
    }
  }

  public async _onAdvancedFilterClick(): Promise<void> {
    if (!this._advancedFilterOptions()) return;

    const dialogResult = await this._dialogAdvancedFilter.open(this._advancedFilterOptions()!);

    if (dialogResult.action === 'apply') {
      this._lastAdvancedFilterValue.set(dialogResult.filters);
      await this._onApplyAdvancedFilters();
    }
  }

  private async _onApplyAdvancedFilters(): Promise<void> {
    const filters = FilterProcessingHelper.processFilters(this._lastAdvancedFilterValue()!);
    const disableOnCallback = this.searchBar().options.disableOnCallback();

    if (this.searchBar().onApplyAdvancedFilters) {
      if (disableOnCallback) this.searchBar().disable({ emitEvent: false });
      await this.searchBar().onApplyAdvancedFilters!(filters);
      if (disableOnCallback) this.searchBar().enable({ emitEvent: false });
    }

    // Emit the advancedFilter event
    this.searchBar().applyAdvancedFilters$.next(filters);
  }
}
