// Angular
import { FormControl } from '@angular/forms';
// Interfaces
import { _CoreSearchBarOptions, CoreSearchBarOptions } from './core-search-bar.interfaces';
import { Subject } from 'rxjs';
import { signal } from '@angular/core';
import { CoreFilterGroup } from '../../shared/interfaces/core-filter.interfaces';

export class CoreSearchBar extends FormControl {
  // [ Properties ]
  public options: _CoreSearchBarOptions;

  // [ Callbacks ]
  public onValueChange?: (value: string | null) => void | Promise<void>;
  public onValueChanged?: (value: string | null) => void | Promise<void>;
  public onSearch?: (value: string | null) => void | Promise<void>;
  public onApplyAdvancedFilters?: (filters: CoreFilterGroup | null) => void | Promise<void>;

  // [ Public Events ]
  public readonly valueChanged$ = new Subject<string | null>();
  public readonly search$ = new Subject<string | null>();
  public readonly applyAdvancedFilters$ = new Subject<CoreFilterGroup | null>();

  constructor(options: CoreSearchBarOptions) {
    super({ value: options.value ?? '', disabled: options.disabled ?? false });

    // Set options with default values
    this.options = {
      searchLabel: signal(options.searchLabel ?? ''),
      placeholder: signal(options.placeholder ?? 'Search...'),
      showBarCodeReader: signal(options.showBarCodeReader ?? false),
      advancedFilterOptions: signal(options.advancedFilterOptions ?? null),
      valueChangedDelay: signal(options.valueChangedDelay ?? 300),
      disableOnCallback: signal(options.disableOnCallback ?? false),
    };

    // Set the `onValueChange` callback
    this.onValueChange = options.onSearchValueChange;

    // Set the `onValueChanged` callback
    this.onValueChanged = options.onSearchValueChanged;

    // Set the `onSearch` callback
    this.onSearch = options.onSearch;

    // Set the `onAdvancedFilter` callback
    this.onApplyAdvancedFilters = options.onApplyAdvancedFilters;
  }
}
