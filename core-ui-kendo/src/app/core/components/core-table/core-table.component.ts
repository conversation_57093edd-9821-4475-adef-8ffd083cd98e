// Angular
import {
  AfterContentInit,
  Component,
  computed,
  contentChild,
  contentChildren,
  inject,
  input,
  On<PERSON><PERSON>roy,
  OnInit,
  Signal,
  signal,
  viewChild,
  WritableSignal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
// Kendo UI
import {
  CellClickEvent,
  ColumnMenuService,
  ColumnReorderEvent,
  DataLayoutModeSettings,
  DataStateChangeEvent,
  GridComponent,
  GridModule,
  RowReorderEvent,
  SelectionEvent,
} from '@progress/kendo-angular-grid';
import { CompositeFilterDescriptor } from '@progress/kendo-data-query';
import { arrowRotateCcwIcon } from '@progress/kendo-svg-icons';
import { ContextMenuComponent, ContextMenuModule, ContextMenuSelectEvent } from '@progress/kendo-angular-menu';
// Core Modules
import { CoreButtonModule } from '../core-button/core-button.module';
// Classes
import { CoreTable } from './classes/core-table';
import { CoreTableColumn } from './classes/core-table-column';
import { CoreTableColumnBoolean } from './classes/core-table-column-boolean';
import { CoreTableColumnDate } from './classes/core-table-column-date';
import { CoreTableColumnDateTime } from './classes/core-table-column-date-time';
import { CoreTableColumnNumber } from './classes/core-table-column-number';
import { CoreTableColumnSwitch } from './classes/core-table-column-switch';
import { CoreTableColumnActions } from './classes/core-table-column-actions';
// Components
import { CoreTableActionsComponent } from './components/core-table-actions/core-table-actions.component';
import { CoreTableSwitchComponent } from './components/core-table-switch/core-table-switch.component';
import { CoreTableBooleanFilterComponent } from './components/core-table-boolean-filter/core-table-boolean-filter.component';
import { CoreTableDateFilterComponent } from './components/core-table-date-filter/core-table-date-filter.component';
import { CoreTableMultiSelectFilterComponent } from './components/core-table-multiselect-filter/core-table-multiselect-filter.component';
import { CoreTableNumberFilterComponent } from './components/core-table-number-filter/core-table-number-filter.component';
import { CoreTableStringFilterComponent } from './components/core-table-string-filter/core-table-string-filter.component';
// Pipes
import { CoreTableNumberValuePipe } from './pipes/core-table-number-value.pipe';
import { CoreTableDateValuePipe } from './pipes/core-table-date-value.pipe';
import { CoreTableBooleanValuePipe } from './pipes/core-table-boolean-value.pipe';
// Directives
import { CoreTableColumnTemplateDirective } from './directives/core-table-column-template.directive';
import { CoreTableDetailsTemplateDirective } from './directives/core-table-details-template.directive';
import { CoreTableRowDragHintTemplateDirective } from './directives/core-table-row-drag-hint-template.directive';
// Types
import { _CoreTableSelectable } from './types/core-table-selectable.type';
// Services
import { CoreTableService } from './core-table.service';
// Interfaces
import { CoreTableRow } from './interfaces/core-table-row';
import { CoreTablePageableData } from './interfaces/core-table-pageable-data';
import { _CoreTableColumnSort } from './interfaces/core-table-column-sort';
import { CoreTableDragRow } from './interfaces/core-table-dragged-row';
import { CoreTableRowDropPosition } from './interfaces/core-table-row-drop-position';
import { CoreTableRowReorderEvent } from './interfaces/core-table-row-reorder-event';
import { CoreTableCustomFooterDirective } from './directives/core-table-custom-footer.directive';
import { CoreSearchBarComponent } from '../core-search-bar/core-search-bar.component';
import { CoreToolbarComponent } from '../core-toolbar';
import { CoreTableContextMenuOptions } from './interfaces/core-table-context-menu-options';
import { CoreSizeChangeDirective } from '../../shared/directives/core-size-change.directive';

@Component({
  selector: 'core-table',
  templateUrl: './core-table.component.html',
  styleUrl: './core-table.component.scss',
  providers: [CoreTableService],
  imports: [
    CommonModule,
    GridModule,
    CoreButtonModule,
    CoreTableActionsComponent,
    CoreTableSwitchComponent,
    CoreTableBooleanFilterComponent,
    CoreTableBooleanValuePipe,
    CoreTableDateFilterComponent,
    CoreTableDateValuePipe,
    CoreTableMultiSelectFilterComponent,
    CoreTableNumberFilterComponent,
    CoreTableNumberValuePipe,
    CoreTableStringFilterComponent,
    CoreToolbarComponent,
    CoreSearchBarComponent,
    ContextMenuModule,
    CoreSizeChangeDirective,
  ],
})
export class CoreTableComponent<TRow extends CoreTableRow> implements OnInit, AfterContentInit, OnDestroy {
  // Inputs
  public table = input.required<CoreTable<TRow>>();
  public columns: WritableSignal<CoreTableColumn[]> = signal([]);

  // Content children
  public _columnTemplateRefs = contentChildren(CoreTableColumnTemplateDirective);
  public _detailsTemplateRef = contentChild(CoreTableDetailsTemplateDirective);
  public _rowDragHintTemplateRef = contentChild(CoreTableRowDragHintTemplateDirective);
  public _customFooterTemplateRef = contentChild(CoreTableCustomFooterDirective);
  // View children
  public _gridContextMenu = viewChild<ContextMenuComponent>('gridmenu');
  // Signals
  public gridWidth = signal<number>(0);
  public _showResponsiveTable = computed(() => {
    if (this.table()._responsiveMaxWidth()) {
      return this.table()._responsiveMaxWidth()! >= this.gridWidth();
    } else {
      return false;
    }
  });
  // Private
  private columnsInitialSnapshot: CoreTableColumn[] = [];

  // Dependencies
  public _service = inject(CoreTableService);

  // Internal
  private _grid = viewChild<GridComponent>('grid');
  public _resetIcon = arrowRotateCcwIcon;

  public _sort: Signal<_CoreTableColumnSort[]> = computed(() =>
    this.table()._state.sort() == null
      ? []
      : [
          {
            field: this.table()._state.sort()!.field,
            dir: this._service.toKendoSortDirection(this.table()._state.sort()!.direction),
          },
        ],
  );

  public _filter: Signal<CompositeFilterDescriptor | null> = computed(() => {
    const filter = this._service.toKendoFilterGroup(this.table()._state.filter());
    if (filter && 'filters' in filter) return filter;
    return null;
  });

  public _pageSize: Signal<number> = computed(() => this.table()._state.paging()?.take ?? 0);

  public _total: Signal<number> = computed(() =>
    'data' in this.table()._data()
      ? (this.table()._data() as CoreTablePageableData<CoreTableRow>).total
      : (this.table()._data() as CoreTableRow[]).length,
  );

  public _selectable: Signal<_CoreTableSelectable> = computed(() =>
    this.table()._selectable === false ? false : { mode: this.table()._selectable as 'single' | 'multiple' },
  );

  public _sortable = computed(() => !this.table()._rowReorderable);

  private _subscription$ = new Subscription();

  // eslint-disable-next-line @typescript-eslint/no-misused-promises
  public async ngOnInit(): Promise<void> {
    this.columns.set(this.table()._state.columns());
    this.columnsInitialSnapshot = this.table()._state.columns();
    this._subscribeToEvents();
    await this._loadData();
  }

  public ngAfterContentInit(): void {
    this._assignTemplateRefs();
  }

  public ngOnDestroy(): void {
    this._subscription$.unsubscribe();
  }

  // [ Events ]

  public onFilterChange(): void {
    // Reset the paging when the filter changes
    if (this.table()._state.pageable) {
      this.table()._state.paging.set({
        skip: 0,
        take: this.table()._state.paging()!.take,
      });
    }
  }

  public async onStateChange(e: DataStateChangeEvent): Promise<void> {
    // Update sort state
    this.table()._state.sort.set(
      (e.sort ?? []).filter((s) => !!s.dir).length > 0
        ? {
            field: e.sort![0].field,
            direction: this._service.fromKendoSortDirection(e.sort![0].dir!),
          }
        : null,
    );

    // Update filter state
    this.table()._state.filter.set(this._service.fromKendoFilterGroup(e.filter));

    // Update paging state
    if (this.table()._state.pageable) {
      this.table()._state.paging.set({
        skip: e.skip,
        take: e.take,
      });
    }

    // Load the data
    await this._loadData();
  }

  public async onSelectionChange(e: SelectionEvent): Promise<void> {
    // Make sure that the selected rows are not null
    if (e.selectedRows == null) return;

    // Deselect items
    const deselectRows = (e.deselectedRows ?? []).map((r) => r.dataItem as TRow);
    if (deselectRows.length > 0) {
      this.table().selectedRows.next(this.table().selectedRows.value.filter((r) => !deselectRows.includes(r)));
    }

    // Select items
    const selectRows = (e.selectedRows ?? []).map((r) => r.dataItem as TRow);
    if (selectRows.length > 0) {
      this.table().selectedRows.next([...this.table().selectedRows.value, ...selectRows]);
    }

    // Call the onSelectionChange method
    await this.table()._onSelectionChange(this.table().selectedRows.value);
  }

  public onColumnReorder(e: ColumnReorderEvent): void {
    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    setTimeout(async () => {
      // Prevent ordering before Checkbox column
      if (e.newIndex === 0) {
        this._grid()!.reorderColumn(this._grid()!.columns.get(e.oldIndex)!, e.oldIndex);
      }
      // Catch secondary automatic event (after attempt to reorder Checkbox column)
      else if (e.oldIndex !== 0) {
        await this.onColumnChange();
      }
    });
  }

  // TODO: Extend this logic to support multiple dragged rows (?)
  public async onRowReorder(e: RowReorderEvent) {
    const eventDropTargetRow = e.dropTargetRow;

    const draggedRow: CoreTableDragRow = {
      rowData: e.draggedRows[0].dataItem as CoreTableRow,
      rowIndex: e.draggedRows[0].rowIndex,
    };

    const dropTargetRow: CoreTableDragRow | undefined = eventDropTargetRow
      ? {
          rowData: eventDropTargetRow.dataItem as CoreTableRow,
          rowIndex: eventDropTargetRow.rowIndex,
        }
      : undefined;

    const dropPosition: CoreTableRowDropPosition = e.dropPosition;

    const oldIndex = draggedRow.rowIndex;
    let newIndex = oldIndex;

    this.table()._data.update((data) => {
      const dataArray = Array.isArray(data) ? data : data.data;

      // Remove the dragged row from the array
      const otherRows = dataArray.filter((item) => item !== draggedRow.rowData);

      // Determine the new index
      if (dropTargetRow) {
        const targetRow = dropTargetRow.rowData;
        const targetRowIndex = otherRows.findIndex((x) => x === targetRow);
        newIndex = targetRowIndex + (dropPosition === 'after' ? 1 : 0);
      } else {
        newIndex = otherRows.length;
      }

      // Insert the dragged row at the new index
      otherRows.splice(newIndex, 0, draggedRow.rowData);

      return { data: otherRows, total: otherRows.length };
    });

    const coreTableRowReorderParameters: CoreTableRowReorderEvent = {
      rowData: draggedRow.rowData,
      oldIndex: oldIndex,
      newIndex: newIndex,
    };

    await this.table()._onRowReorder(coreTableRowReorderParameters);
    // Emit the row reorder event
    this.table().onRowReorder.next(coreTableRowReorderParameters);
  }

  public async onColumnChange(): Promise<void> {
    // Wait for the columns to be updated
    await new Promise<void>((resolve) => setTimeout(resolve, 0));

    // Merge table columns with grid columns
    const columns: { field: string; width: number | null; index: number; visible: boolean }[] = [];
    let i = 0;
    this._grid()?.columnList.forEach((c) => {
      if ((c as unknown as { field: string }).field) {
        columns.push({
          field: (c as unknown as { field: string }).field,
          width: c.width != null ? Math.round(c.width) : null,
          index: c.orderIndex ?? 0,
          visible: !c.hidden,
        });
        this.columnsInitialSnapshot[i++].visible = !c.hidden;
      }
    });
    this.onSizeChanged(this.gridWidth());
    // Call the onColumnsChange callback method excluding the order index
    await this.table()._onColumnsChange(
      columns
        .sort((a, b) => a.index - b.index)
        .map((c) => ({
          field: c.field,
          width: c.width,
          visible: c.visible,
        })),
    );
    // Emit the columns change event
    this.table().onColumnsChange.next(
      columns
        .sort((a, b) => a.index - b.index)
        .map((c) => ({
          field: c.field,
          width: c.width,
          visible: c.visible,
        })),
    );
  }

  public onResetFilters(service: ColumnMenuService): void {
    this.table()._state.filter.set(null);
    this.table().refresh();
    service.close();
  }

  public onResetColumns(service: ColumnMenuService): void {
    // Remove all columns
    this.columns.set([]);

    setTimeout(() => {
      // Reset columns
      this.table()._state.columns.update((columns) => {
        columns.forEach((column) => {
          // Find the column default state
          const columnState = this.table()._state.defaultColumnsState.find((c) => c.field === column.field);
          if (columnState == null) return;
          // Reset the column state
          column.width = columnState.width;
          column.visible = columnState.visible;
          column.index = columnState.index;
        });
        return columns;
      });

      // Set new columns
      this.columnsInitialSnapshot = this.table()._state.columns();
      this.onSizeChanged(this.gridWidth());

      // Call the onColumnsChange callback method
      this.table()._onColumnsChange(
        this.table()
          ._state.columns()
          .filter((c) => c.visible)
          .sort((a, b) => (a.index ?? 0) - (b.index ?? 0))
          .map((c) => ({
            field: c.field,
            width: c.width,
            visible: c.visible,
          })),
      ) as void;

      // Emit the columns change event
      this.table().onColumnsChange.next(
        this.table()
          ._state.columns()
          .filter((c) => c.visible)
          .sort((a, b) => (a.index ?? 0) - (b.index ?? 0))
          .map((c) => ({
            field: c.field,
            width: c.width,
            visible: c.visible,
          })),
      );

      // Close the column menu
      service.close();
    });
  }

  public contextMenu = signal<CoreTableContextMenuOptions[]>([]);
  private clickedRow: TRow | null = null;

  public async onCellClick(e: CellClickEvent): Promise<void> {
    const contextMenuOptions = await this.table()._onContextMenu(e.dataItem as TRow);

    if (e.type === 'contextmenu') {
      const originalEvent = e.originalEvent as MouseEvent;
      originalEvent.preventDefault();

      // Dynamic context menu options
      if (this.table()._contextMenuOptions().length === 0) {
        this.contextMenu.set(contextMenuOptions);
        this.clickedRow = e.dataItem as TRow;
      } else {
        // Array of context menu options is set in the table options
        this.contextMenu.set(this.table()._contextMenuOptions());
        this.clickedRow = e.dataItem as TRow;
      }

      this._gridContextMenu()?.show({
        left: originalEvent.pageX,
        top: originalEvent.pageY,
      });
    }
  }

  public async onGridContextMenu(event: MouseEvent): Promise<void> {
    event.preventDefault();

    // Check if the click was inside a cell
    const target = event.target as HTMLElement;
    const isCellClick = target.closest('.k-grid-content td') !== null;

    // If the click was inside a cell, do nothing (will be handled by onCellClick)
    if (isCellClick) return;

    // If the click was outside a cell, show the context menu with no row selected
    const contextMenuOptions = await this.table()._onContextMenu(null);

    if (contextMenuOptions.length > 0) {
      this.contextMenu.set(contextMenuOptions);

      this._gridContextMenu()?.show({
        left: event.pageX,
        top: event.pageY,
      });
    }
  }

  public onContextMenuSelect(event: ContextMenuSelectEvent): void {
    const menuOptions = (event.item as { data: CoreTableContextMenuOptions }).data;
    if (!menuOptions) return;

    const options = {
      id: menuOptions.id,
      row: this.clickedRow,
    };
    // Subject emits the selected context menu item
    this.table().onContextMenuItemClick.next(options);
    // Call the onContextMenuItemClick callback method
    void this.table()._onContextMenuItemClick(options);
  }

  public onSizeChanged(gridWidth?: number): void {
    if (!gridWidth) return;
    this.gridWidth.set(gridWidth);

    if (this._showResponsiveTable()) {
      this.gridDataLayout.set({ mode: 'stacked', stackedCols: 1 });
    } else {
      this.gridDataLayout.set({ mode: 'columns' });
    }

    if (this.columnsInitialSnapshot.some((col) => col.width == null && col.visible)) {
      this.columns.set(this.table()._state.columns());
      return;
    }
    // Calculate the width of fixed columns
    const rowReorderableColumnWidth = this.table()._rowReorderable ? 40 : 0;
    const selectColumnWidth = this.table()._showSelectColumn ? 40 : 0;
    const detailsColumnWidth = this.table()._showDetails ? 40 : 0;
    const totalFixedWidth = rowReorderableColumnWidth + selectColumnWidth + detailsColumnWidth;
    const scrollbarWidth = this.getScrollbarWidth();
    // Recalculate columns width
    this._service.calculateColumnsWidth(
      this.columns,
      this.columnsInitialSnapshot,
      gridWidth - scrollbarWidth,
      totalFixedWidth,
    );
  }

  public gridDataLayout = signal<DataLayoutModeSettings>({ mode: 'columns' });

  // [ Casts ]

  public $asNumberColumn(column: CoreTableColumn): CoreTableColumnNumber {
    return column as CoreTableColumnNumber;
  }

  public $asDateColumn(column: CoreTableColumn): CoreTableColumnDate {
    return column as CoreTableColumnDate;
  }

  public $asDateTimeColumn(column: CoreTableColumn): CoreTableColumnDateTime {
    return column as CoreTableColumnDateTime;
  }

  public $asBooleanColumn(column: CoreTableColumn): CoreTableColumnBoolean {
    return column as CoreTableColumnBoolean;
  }

  public $asActiveColumn(column: CoreTableColumn): CoreTableColumnSwitch<CoreTableRow> {
    return column as CoreTableColumnSwitch<CoreTableRow>;
  }

  public $asActionsColumn(column: CoreTableColumn): CoreTableColumnActions<CoreTableRow> {
    return column as CoreTableColumnActions<CoreTableRow>;
  }

  // [ Helpers ]

  public _showDetails(row: CoreTableRow): boolean {
    return typeof this.table()._showDetails === 'function'
      ? (this.table()._showDetails as (row: TRow) => boolean)(row as TRow)
      : (this.table()._showDetails as boolean);
  }

  public _isRowSelectable(): boolean {
    return this._selectable() !== false;
  }

  // [ Internal methods ]

  private _assignTemplateRefs(): void {
    for (const template of this._columnTemplateRefs()) {
      // Find the column by field name
      const column = this.table()
        ._state.columns()
        .find((c) => c.field === template.field());
      if (column == null) throw new Error(`Template column not found: ${template.field()}`);

      // Assign the template
      column.templateRef = template.templateRef;
    }
  }

  private _subscribeToEvents(): void {
    this._subscription$.add(this.table()._refresh$.subscribe(() => void this._loadData()));
  }

  // Note: Optimize scrollbar measurement
  private getScrollbarWidth(): number {
    // Create a temporary element to measure scrollbar width
    const scrollElement = document.createElement('div');
    scrollElement.style.visibility = 'hidden';
    scrollElement.style.overflow = 'scroll';
    scrollElement.style.width = '100px';
    scrollElement.style.height = '100px';
    document.body.appendChild(scrollElement);

    // Measure the inner width without scrollbar
    const content = document.createElement('div');
    content.style.width = '100%';
    scrollElement.appendChild(content);
    const scrollbarWidth = scrollElement.offsetWidth - content.offsetWidth;
    // Clean up
    document.body.removeChild(scrollElement);

    return scrollbarWidth + 0.5;
  }

  private async _loadData(): Promise<void> {
    this.table()._loading.set(true);

    // Call a data function
    const rawData = await this.table()._dataFn(this.table()._state);

    // Set the data
    this.table()._data.set(rawData);

    this.table()._loading.set(false);

    await this.table()._onDataLoaded();
  }
}
