@if (table().toolbar() || table().searchBar()) {
  <div class="header">
    @if (table().toolbar()) {
      <core-toolbar [toolbar]="table().toolbar()!" />
    }

    @if (table().searchBar()) {
      <core-search-bar [searchBar]="table().searchBar()!" />
    }
  </div>
}

<kendo-grid
  #grid
  coreSizeChange
  [data]="table()._data()"
  [columnMenu]="{ filter: true }"
  [sortable]="_sortable()"
  [sort]="_sort()"
  [filterable]="'menu'"
  [filter]="_filter()"
  [pageable]="true"
  [pageSize]="_pageSize()"
  [skip]="table()._state.paging()?.skip"
  [reorderable]="table()._reorderable"
  [rowReorderable]="table()._rowReorderable"
  [resizable]="table()._resizable"
  [loading]="table()._loading()"
  [ngStyle]="table()._ngStyle"
  [selectable]="_selectable()"
  [isRowSelectable]="_isRowSelectable.bind(this)"
  [kendoGridSelectBy]="'id'"
  [adaptiveMode]="'auto'"
  [dataLayoutMode]="gridDataLayout()"
  [(selectedKeys)]="table()._state.selectedIds"
  [(expandedDetailKeys)]="table()._state.expandedDetailIds"
  [kendoGridExpandDetailsBy]="'id'"
  (cellClick)="onCellClick($event)"
  (contextmenu)="onGridContextMenu($event)"
  (filterChange)="onFilterChange()"
  (dataStateChange)="onStateChange($event)"
  (selectionChange)="onSelectionChange($event)"
  (columnReorder)="onColumnReorder($event)"
  (rowReorder)="onRowReorder($event)"
  (columnResize)="onColumnChange()"
  (columnVisibilityChange)="onColumnChange()"
  (onSizeChange)="onSizeChanged($event?.width)"
>
  <!-- Column Menu -->
  <ng-template kendoGridColumnMenuTemplate let-service="service" let-column="column">
    @if (column.sortable) {
      <kendo-grid-columnmenu-sort [service]="service" />
    }
    <kendo-grid-columnmenu-chooser [service]="service" />
    @if (column.filterable) {
      <kendo-grid-columnmenu-filter [service]="service" />
    }
    <kendo-grid-columnmenu-item text="Reset" [svgIcon]="_resetIcon">
      <ng-template kendoGridColumnMenuItemContentTemplate>
        <div class="reset-section">
          <core-button [label]="'Reset Filters'" (click)="onResetFilters(service)" />
          <core-button [label]="'Reset Columns'" (click)="onResetColumns(service)" />
        </div>
      </ng-template>
    </kendo-grid-columnmenu-item>
  </ng-template>

  <!-- Responsive Toolbar -->
  @if (_showResponsiveTable() && table()._showResponsiveToolbar) {
    <kendo-toolbar overflow="scroll" [showText]="'always'" showIcon="always" [size]="'small'">
      <kendo-toolbar-button kendoGridFilterTool />
      <kendo-toolbar-button kendoGridSortTool />
      <kendo-toolbar-button kendoGridColumnChooserTool [allowHideAll]="false" />
    </kendo-toolbar>
  }

  <!-- Select column -->
  @if (table()._showSelectColumn) {
    <kendo-grid-checkbox-column
      [showSelectAll]="true"
      [width]="40"
      [columnMenu]="false"
      [reorderable]="false"
    ></kendo-grid-checkbox-column>
  }

  <!-- Row Reorder Column -->
  @if (table()._rowReorderable && table()._showRowReorderColumn) {
    <kendo-grid-rowreorder-column [width]="40" [columnMenu]="false">
      @if (_rowDragHintTemplateRef()?.templateRef) {
        <ng-template kendoGridRowDragHintTemplate let-dataItem>
          <ng-container *ngTemplateOutlet="_rowDragHintTemplateRef()?.templateRef!; context: { $implicit: dataItem }" />
        </ng-template>
      }
    </kendo-grid-rowreorder-column>
  }

  <!-- Dynamic columns -->
  @for (column of columns(); let columnIndex = $index; track column.field) {
    <kendo-grid-column
      [field]="column.field"
      [title]="column.label"
      [sortable]="_sortable() && !!column.sort"
      [filterable]="!!column.filter"
      [reorderable]="column.reorderable"
      [resizable]="column.resizable"
      [columnMenu]="column.showMenu && column.type !== 'actions'"
      [includeInChooser]="column.type !== 'actions'"
      [hidden]="!column.visible"
      [width]="column.width!"
      [sticky]="column.sticky && !_detailsTemplateRef()?.templateRef?.elementRef?.nativeElement"
      [style]="{ 'text-align': column.align, padding: column.padding }"
    >
      <!-- Filter -->
      <ng-template kendoGridFilterMenuTemplate let-filter let-_column="column" let-filterService="filterService">
        @switch (column.filter) {
          <!-- String -->
          @case ("string") {
            <core-table-string-filter [column]="_column" [filter]="filter" [filterService]="filterService" />
          }
          <!-- Number -->
          @case ("number") {
            <core-table-number-filter
              [column]="_column"
              [filter]="filter"
              [filterService]="filterService"
              [format]="$asNumberColumn(column).format"
            />
          }
          <!-- Date -->
          @case ("date") {
            <core-table-date-filter
              [column]="_column"
              [filter]="filter"
              [filterService]="filterService"
              [format]="$asDateColumn(column).format"
            />
          }
          <!-- Boolean -->
          @case ("boolean") {
            <core-table-boolean-filter
              [column]="_column"
              [filter]="filter"
              [filterService]="filterService"
              [trueLabel]="$asBooleanColumn(column).trueLabel"
              [falseLabel]="$asBooleanColumn(column).falseLabel"
            />
          }
          <!-- Multiselect -->
          @case ("multiselect") {
            <core-table-multiselect-filter
              [column]="_column"
              [filter]="filter"
              [filterService]="filterService"
              [options]="column.filterOptions"
            />
          }
          @default {
            <span>Unkown Filter Type</span>
          }
        }
      </ng-template>

      <!-- Cell -->
      <ng-template kendoGridCellTemplate let-dataItem>
        <!-- Cell Content Wrapper -->
        <div
          [class.responsive-cell-style]="_showResponsiveTable()"
          [class.bottom-border]="_showResponsiveTable() && columnIndex < columns().length - 1"
        >
          <!-- Custom template -->
          @if (!!column.templateRef) {
            <ng-container *ngTemplateOutlet="column.templateRef; context: { $implicit: dataItem }"></ng-container>
          } @else {
            <!-- Default cells -->
            @switch (column.type) {
              <!-- Number -->
              @case ("number") {
                {{ dataItem[column.field] | numberValue: $asNumberColumn(column).format }}
              }
              <!-- Date -->
              @case ("date") {
                {{ dataItem[column.field] | dateValue: $asDateColumn(column).format }}
              }
              <!-- Date -->
              @case ("datetime") {
                {{ dataItem[column.field] | dateValue: $asDateTimeColumn(column).format }}
              }
              <!-- Boolean -->
              @case ("boolean") {
                {{
                  dataItem[column.field]
                    | booleanValue: $asBooleanColumn(column).trueLabel : $asBooleanColumn(column).falseLabel
                }}
              }
              <!-- Active -->
              @case ("switch") {
                <core-table-switch [column]="$asActiveColumn(column)" [dataItem]="dataItem" />
              }
              <!-- Actions -->
              @case ("actions") {
                <core-table-actions
                  [column]="$asActionsColumn(column)"
                  [style]="gridDataLayout().mode === 'stacked' ? { 'justify-content': 'left' } : null"
                  [dataItem]="dataItem"
                />
              }
              @default {
                {{ dataItem[column.field] }}
              }
            }
          }
        </div>
      </ng-template>
    </kendo-grid-column>
  }

  <!-- Context Menu -->
  <kendo-contextmenu
    #gridmenu
    [kendoMenuHierarchyBinding]="contextMenu()"
    [textField]="['label']"
    childrenField="children"
    svgIconField="svgIcon"
    separatorField="separator"
    (select)="onContextMenuSelect($event)"
  />

  <!-- Details -->
  @if (_detailsTemplateRef()?.templateRef) {
    <ng-template kendoGridDetailTemplate [kendoGridDetailTemplateShowIf]="_showDetails.bind(this)" let-dataItem>
      <ng-container
        *ngTemplateOutlet="_detailsTemplateRef()?.templateRef!; context: { $implicit: dataItem }"
      ></ng-container>
    </ng-template>
  }
  <!-- Pager -->
  <ng-template kendoPagerTemplate>
    <div class="pager-wrapper">
      @if (table()._state.pageable) {
        <kendo-pager-prev-buttons></kendo-pager-prev-buttons>
        <kendo-pager-numeric-buttons [buttonCount]="3"></kendo-pager-numeric-buttons>
        <kendo-pager-next-buttons></kendo-pager-next-buttons>
        <kendo-pager-page-sizes [pageSizes]="table()._pageSizes()"></kendo-pager-page-sizes>
        <kendo-pager-info [style.flex]="this.table()._showCustomFooter ? 'none' : 'null'"></kendo-pager-info>
        <!-- TODO: add || table()._showFooterTemplate -->
        @if (table()._showSelectedCount || table()._showCustomFooter) {
          <div class="pager-info">
            <!-- Selected Count -->
            @if (table()._showSelectedCount) {
              <span>
                <span class="pager-divider --left">|</span>
                <span>
                  Selected: {{ table()._state.selectedIds().length }} item{{
                    table()._state.selectedIds().length > 1 ? "s" : ""
                  }}
                </span>
              </span>
            }
            <!-- Custom Footer -->
            @if (table()._showCustomFooter && _customFooterTemplateRef()?.templateRef) {
              <div [class]="`pager-info__footer --${this.table()._customFooterPosition}`">
                <span class="pager-info__footer-divider --left">|</span>
                <ng-container *ngTemplateOutlet="_customFooterTemplateRef()?.templateRef!" />
                <span class="pager-info__footer-divider --right">|</span>
              </div>
            }
          </div>
        }
      } @else {
        @if (table()._showSelectedCount) {
          <span>
            Selected:&nbsp;<strong>{{ table()._state.selectedIds().length }}</strong
            >&nbsp; item{{ table()._state.selectedIds().length > 1 ? "s" : "" }}
          </span>
        }
        <span class="footer">Total: {{ _total() }}</span>
      }
    </div>
  </ng-template>
</kendo-grid>
