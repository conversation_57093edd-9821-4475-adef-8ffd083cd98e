/* eslint-disable @typescript-eslint/restrict-template-expressions */
// Angular
import { Injectable, WritableSignal } from '@angular/core';
// Kendo UI
import { CompositeFilterDescriptor, FilterDescriptor } from '@progress/kendo-data-query';
// Interfaces
import { CoreTableFilter, CoreTableFilterGroup } from './interfaces/core-table-filter';
import { _CoreTableFilterOperator, CoreTableFilterOperator } from './types/core-table-filter-operator.type';
// Classes
import { CoreTableColumn } from './classes/core-table-column';

@Injectable()
export class CoreTableService {
  public fromKendoSortDirection(direction: 'asc' | 'desc'): 'ASC' | 'DESC' {
    switch (direction) {
      case 'asc':
        return 'ASC';
      case 'desc':
        return 'DESC';
      default:
        throw new Error(`Unknown sort direction: ${direction}`);
    }
  }

  public toKendoSortDirection(direction: 'ASC' | 'DESC'): 'asc' | 'desc' {
    switch (direction) {
      case 'ASC':
        return 'asc';
      case 'DESC':
        return 'desc';
      default:
        throw new Error(`Unknown sort direction: ${direction}`);
    }
  }

  public fromKendoFilterGroup(filterRoot?: CompositeFilterDescriptor): CoreTableFilterGroup | null {
    if (filterRoot == null || filterRoot.filters.length === 0) return null;
    // Convert the filter to the internal format
    const _filterRoot: CoreTableFilterGroup = {
      logic: this._fromKendoFilterLogic(filterRoot.logic),
      filters: (filterRoot.filters as CompositeFilterDescriptor[]).map((fg) => ({
        logic: this._fromKendoFilterLogic(fg.logic),
        filters: (fg.filters as FilterDescriptor[]).map((f) => ({
          field: f.field as string,
          operator: this._fromKendoFilterOperator(f.operator as _CoreTableFilterOperator),
          value: f.value as string | number | boolean | Date | null,
        })),
      })),
    };

    return _filterRoot;
  }

  public toKendoFilterGroup(
    filterGroup: CoreTableFilterGroup | CoreTableFilter | null,
  ): CompositeFilterDescriptor | FilterDescriptor | null {
    if (filterGroup == null) return null;

    if ('filters' in filterGroup) {
      // It's a CoreTableFilterGroup
      const _filterGroup: CompositeFilterDescriptor = {
        logic: this._toKendoFilterLogic(filterGroup.logic),
        filters: filterGroup.filters.map((fg) => this.toKendoFilterGroup(fg) as CompositeFilterDescriptor),
      };
      return _filterGroup;
    } else {
      // It's a CoreTableFilter
      const _filter: FilterDescriptor = {
        field: filterGroup.field,
        operator: this._toKendoFilterOperator(filterGroup.operator),
        value: filterGroup.value,
      };
      return _filter;
    }
  }

  public calculateColumnsWidth(
    tableColumns: WritableSignal<CoreTableColumn[]>,
    initialColumns: CoreTableColumn[],
    gridWidth: number,
    fixedColumnsWidth: number,
  ): void {
    const columns = initialColumns.filter((col) => col.visible === true);
    // Fixed columns (fixed: true)
    const fixedColumns = columns.filter((col) => col.fixed);
    const fixedWidth = fixedColumns.reduce((sum, col) => sum + (col.width ?? 0), 0);

    // Action and switch columns
    const actionAndSwitchColumns = columns.filter((col) => col.type === 'switch' || col.type === 'actions');
    const actionAndSwitchWidth = actionAndSwitchColumns.reduce((sum, col) => sum + (col.width ?? 0), 0);

    // Stretched columns (not fixed, not action/switch)
    const stretchedColumns = columns.filter((col) => !col.fixed && col.type !== 'switch' && col.type !== 'actions');
    const stretchWidth = stretchedColumns.reduce((sum, col) => sum + (col.width ?? 0), 0);

    // Calculate total used width and free space
    const totalUsedWidth = fixedColumnsWidth + fixedWidth + actionAndSwitchWidth + stretchWidth;
    const freeSpace = gridWidth - totalUsedWidth;

    // If there is free space, distribute it among stretched columns
    if (freeSpace > 0 && stretchedColumns.length > 0) {
      // Calculate the extra width for each stretched column
      const extraWidth = freeSpace / stretchedColumns.length;
      const newColumns = initialColumns.map((col) => {
        if (stretchedColumns.includes(col)) {
          let newWidth = (col.width ?? 0) + extraWidth;
          if (col.minWidth != null && newWidth < col.minWidth) {
            newWidth = Number(col.minWidth);
          }
          return { ...col, width: newWidth };
        }
        return col;
      });
      tableColumns.update(() => newColumns);
    } else {
      tableColumns.update(() => initialColumns);
    }
  }

  // [ Internal methods ]

  private _fromKendoFilterOperator(operator: _CoreTableFilterOperator): CoreTableFilterOperator {
    switch (operator) {
      case 'eq':
        return 'EQUALS';
      case 'neq':
        return 'NOT_EQUALS';
      case 'contains':
        return 'CONTAINS';
      case 'startsWith':
        return 'STARTS_WITH';
      case 'endsWith':
        return 'ENDS_WITH';
      case 'lt':
        return 'LESS_THAN';
      case 'lte':
        return 'LESS_THAN_OR_EQUAL';
      case 'gt':
        return 'GREATER_THAN';
      case 'gte':
        return 'GREATER_THAN_OR_EQUAL';
      default:
        throw new Error(`Unknown filter operator: ${operator}`);
    }
  }

  private _toKendoFilterOperator(operator: CoreTableFilterOperator): _CoreTableFilterOperator {
    switch (operator) {
      case 'EQUALS':
        return 'eq';
      case 'NOT_EQUALS':
        return 'neq';
      case 'CONTAINS':
        return 'contains';
      case 'STARTS_WITH':
        return 'startsWith';
      case 'ENDS_WITH':
        return 'endsWith';
      case 'LESS_THAN':
        return 'lt';
      case 'LESS_THAN_OR_EQUAL':
        return 'lte';
      case 'GREATER_THAN':
        return 'gt';
      case 'GREATER_THAN_OR_EQUAL':
        return 'gte';
      default:
        throw new Error(`Unknown filter operator: ${operator}`);
    }
  }

  private _fromKendoFilterLogic(logic: 'and' | 'or'): 'AND' | 'OR' {
    switch (logic) {
      case 'and':
        return 'AND';
      case 'or':
        return 'OR';
      default:
        throw new Error(`Unknown filter logic: ${logic}`);
    }
  }

  private _toKendoFilterLogic(logic: 'AND' | 'OR'): 'and' | 'or' {
    switch (logic) {
      case 'AND':
        return 'and';
      case 'OR':
        return 'or';
      default:
        throw new Error(`Unknown filter logic: ${logic}`);
    }
  }
}
