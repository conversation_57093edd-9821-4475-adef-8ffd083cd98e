:host {
  display: flex;
  flex-flow: column nowrap;
}

kendo-grid {
  flex: 1 1 auto;
  overflow: auto;
}

.header {
  display: flex;
  flex-flow: row;
  align-items: center;
  z-index: 1;
  padding: 6px;
  border: 1px solid var(--border-color);
  background: var(--toolbar-bg);
  box-shadow: var(--toolbar-shadow);
}

core-search-bar {
  flex-grow: 1;
}

.footer {
  margin-left: auto;
}

.details {
  display: flex;
  flex-flow: column nowrap;
  gap: 0.5rem;
}

.reset-section {
  display: flex;
  flex-flow: column nowrap;
  gap: 0.25rem;
  padding: 0.25rem;
}

.responsive-cell-style {
  padding: 0.25rem 0;
}

.bottom-border {
  border-bottom: 1px solid var(--border-color);
}

.pager-info {
  display: flex;
  flex-grow: 1;
  align-items: center;
  margin-left: auto;
  gap: 8px;
  padding: 0 8px;

  &__footer {
    width: 100%;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;

    &.--left {
      justify-content: flex-start;

      & .pager-info__footer-divider.--left {
        display: block;
        margin-right: 8px;
      }
    }

    &.--center {
      justify-content: center;
    }

    &.--right {
      justify-content: flex-end;

      & .pager-info__footer-divider.--right {
        display: block;
        margin-left: 8px;
      }
    }

    &-divider {
      display: none;
    }
  }
}

.pager-wrapper {
  flex: 1 1 0;
  display: flex;
  align-items: center;
  flex-flow: row wrap;
}

@media (max-width: 780px) {
  .pager-divider {
    display: none;
  }
}
