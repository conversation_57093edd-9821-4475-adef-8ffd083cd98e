// Angular
import { computed, signal, WritableSignal } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
// Classes
import { CoreTableState } from './core-table-state';
import { CoreTableColumnBoolean } from './core-table-column-boolean';
import { CoreTableColumnDate } from './core-table-column-date';
import { CoreTableColumnNumber } from './core-table-column-number';
import { CoreTableColumnString } from './core-table-column-string';
// Types
import { CoreTableDataFn } from '../types/core-table-data-fn.type';
import { CoreTableSelectable } from '../types/core-table-selectable.type';
// Interfaces
import { CoreTableColumnActions } from './core-table-column-actions';
import { CoreTableColumnSwitch } from './core-table-column-switch';
import { CoreTableColumnSwitchOptions } from '../interfaces/core-table-column-switch-options';
import { CoreTableColumnBooleanOptions } from '../interfaces/core-table-column-boolean-options';
import { CoreTableColumnDateOptions } from '../interfaces/core-table-column-date-options';
import { CoreTableColumnDateTimeOptions } from '../interfaces/core-table-column-date-time-options';
import { CoreTableColumnNumberOptions } from '../interfaces/core-table-column-number-options';
import { CoreTableColumnOptions } from '../interfaces/core-table-column-options';
import { CoreTableOptions } from '../interfaces/core-table-options';
import { CoreTableColumnActionsOptions } from '../interfaces/core-table-column-actions-options';
import { CoreTableRow } from '../interfaces/core-table-row';
import { CoreTableColumnDateTime } from './core-table-column-date-time';
import { CoreTableColumnChangeItem } from '../interfaces/core-table-column-change-item';
import { CoreTablePageableData } from '../interfaces/core-table-pageable-data';
import { CoreTableRefreshOptions } from '../interfaces/core-table-refresh-options';
import { CoreTableRowReorderEvent } from '../interfaces/core-table-row-reorder-event';
import { CoreTableCustomFooterPosition, CoreTableCustomFooterType } from '../interfaces/core-table-footer-options';
import { CoreSearchBarOptions } from '../../core-search-bar/core-search-bar.interfaces';
import { CoreSearchBar } from '../../core-search-bar/core-search-bar';
import { CoreToolbar } from '../../core-toolbar';
import { CoreTableContextMenuOptions } from '../interfaces/core-table-context-menu-options';

export class CoreTable<TRow extends CoreTableRow> {
  // [ Public properties ]

  public selectedRows: BehaviorSubject<TRow[]>;
  public data = computed(() => this._data());

  // [ Public events ]

  public onSelectionChange: Subject<TRow[]>;
  public onContextMenuItemClick = new Subject<{ id: string | number; row: TRow | null }>();
  public onColumnsChange = new Subject<CoreTableColumnChangeItem[]>();
  public onRowReorder = new Subject<CoreTableRowReorderEvent>();

  // [ Private properties ]

  public _dataFn: (state: CoreTableState) => CoreTableDataFn;
  public _data: WritableSignal<CoreTableRow[] | CoreTablePageableData<CoreTableRow>> = signal([]);
  public _state: CoreTableState;
  public _loading: WritableSignal<boolean>;
  public _selectable: CoreTableSelectable;
  public _showSelectColumn: boolean;
  public _showRowReorderColumn: boolean;
  public _showSelectedCount: boolean;
  public _pageSizes: WritableSignal<number[]>;
  public _reorderable: boolean;
  public _rowReorderable: boolean;
  public _resizable: boolean;
  public _showDetails: boolean | ((row: TRow) => boolean);
  public _responsiveMaxWidth: WritableSignal<number | null> = signal(null);
  public _showResponsiveToolbar: boolean;
  public _contextMenuOptions: WritableSignal<CoreTableContextMenuOptions[]>;
  public _showCustomFooter: boolean;
  public _customFooterPosition: CoreTableCustomFooterPosition | null = null;
  public _customFooterType: CoreTableCustomFooterType | null = null;
  public _ngStyle: Record<string, string>;

  public toolbar = signal<CoreToolbar | null>(null);
  public searchBar = signal<CoreSearchBar | null>(null);

  // [ Events ]

  public _refresh$ = new Subject<void>();

  constructor(options: CoreTableOptions<TRow>) {
    this._state = new CoreTableState(
      options.state,
      options.columns,
      options?.pageable ?? false,
      options?.pageSize ?? 25,
    );

    this._dataFn = options.data;
    this._loading = signal(false);
    this._selectable = options.selectable ?? false;
    this._showSelectColumn = options.showSelectColumn ?? false;
    this._showSelectedCount = options.showSelectedCount ?? false;
    this._showRowReorderColumn = options.showRowReorderColumn ?? true;
    this._reorderable = options.reorderable ?? false;
    this._rowReorderable = options.rowReorderable ?? false;
    this._resizable = options.resizable ?? false;
    this._contextMenuOptions = signal(options.contextMenuOptions ?? []);
    this.selectedRows = new BehaviorSubject<TRow[]>([]);
    this.onSelectionChange = this.selectedRows.asObservable() as Subject<TRow[]>;
    this._pageSizes = signal(options.pageSizes ?? [25, 100, 1000]);
    this._showDetails = options.showDetails ?? false;
    this._responsiveMaxWidth = signal(options.responsiveMaxWidth ?? null);
    this._showResponsiveToolbar = options.showResponsiveToolbar ?? false;
    this._ngStyle = options.ngStyle ?? {};

    // Set Table Callbacks
    this._onSelectionChange = options.onSelectionChange ?? (() => void 0);
    this._onColumnsChange = options.onColumnsChange ?? (() => void 0);
    this._onRowReorder = options.onRowReorder ?? (() => void 0);
    this._onDataLoaded = options.onDataLoaded ?? (() => void 0);
    this._onContextMenu = options.onContextMenu ?? (() => []);
    this._onContextMenuItemClick = options.onContextMenuItemClick ?? (() => void 0);

    if (options.customFooter) {
      this._showCustomFooter = true;
      this._customFooterPosition = options.customFooter.position ?? 'right';
      this._customFooterType = options.customFooter.type ?? 'info';
    } else {
      this._showCustomFooter = false;
    }

    // Toolbar
    if (options.toolbarOptions) {
      this.toolbar.set(
        new CoreToolbar({
          items: options.toolbarOptions.items,
          style: options.toolbarOptions.style ?? { background: 'none' },
          overflow: options.toolbarOptions.overflow,
        }),
      );
    }

    // Search Bar
    if (options.searchBarOptions) {
      const searchBarOptions: CoreSearchBarOptions = {
        ...options.searchBarOptions,
      };

      if (options.searchBarOptions.showAdvancedFilter) {
        searchBarOptions.advancedFilterOptions = {
          columns: this._state.columns(),
          value: options.searchBarOptions.advancedFilterValue,
        };
      }

      this.searchBar.set(new CoreSearchBar(searchBarOptions));
    }
  }

  // [ Callbacks ]

  // Table Callbacks
  public _onSelectionChange: (rows: TRow[]) => void | Promise<void>;
  public _onColumnsChange: (columns: CoreTableColumnChangeItem[]) => void | Promise<void>;
  public _onRowReorder: (event: CoreTableRowReorderEvent) => void | Promise<void>;
  public _onDataLoaded: () => void | Promise<void>;
  public _onContextMenu: (row: TRow | null) => CoreTableContextMenuOptions[] | Promise<CoreTableContextMenuOptions[]>;
  public _onContextMenuItemClick: (item: { id: string | number; row: TRow | null }) => void | Promise<void>;

  // [ Public methods ]

  public refresh(options: CoreTableRefreshOptions | undefined = undefined): void {
    if (options?.collapseDetails) {
      this._state.expandedDetailIds.set([]);
    }

    if (options?.clearSelection) {
      this.setSelectedIds([]);
    }

    this._refresh$.next();
  }

  public setSelectedIds(ids: (string | number)[]): void {
    this._state.selectedIds.set(ids);

    // Get current selected rows and all rows
    const selectedRows = this.selectedRows.value;
    const allRows =
      typeof this._data() === 'object' && 'data' in this._data()
        ? (this._data() as CoreTablePageableData<TRow>).data
        : (this._data() as TRow[]);
    // Add new selected rows if not already selected
    const newSelectedRows = allRows.filter(
      (row) => ids.includes(row.id) && !selectedRows.some((selectedRow) => selectedRow.id === row.id),
    );
    // Remove unselected rows
    const unselectedRows = selectedRows.filter((row) => !ids.includes(row.id));
    this.selectedRows.next([...selectedRows.filter((row) => !unselectedRows.includes(row)), ...newSelectedRows]);

    // Emit selection change event
    void this._onSelectionChange(this.selectedRows.value);
  }

  public setPageSizes(pageSizes: number[]): void {
    this._pageSizes.set(pageSizes);
  }

  // [ Extensions ]

  public static ColumnString(options: CoreTableColumnOptions) {
    return new CoreTableColumnString({ filter: 'string', ...options });
  }

  public static ColumnNumber(options: CoreTableColumnNumberOptions) {
    return new CoreTableColumnNumber({ filter: 'number', ...options });
  }

  public static ColumnDate(options: CoreTableColumnDateOptions) {
    return new CoreTableColumnDate({ filter: 'date', ...options });
  }

  public static ColumnDateTime(options: CoreTableColumnDateTimeOptions) {
    return new CoreTableColumnDateTime({ filter: 'date', ...options });
  }

  public static ColumnBoolean(options: CoreTableColumnBooleanOptions) {
    return new CoreTableColumnBoolean({ filter: 'boolean', ...options });
  }

  public static ColumnSwitch<TRow extends CoreTableRow>(options: CoreTableColumnSwitchOptions<TRow>) {
    return new CoreTableColumnSwitch<TRow>({
      filter: 'boolean',
      width: 100,
      ...options,
    });
  }

  public static ColumnActions<TRow extends CoreTableRow>(options: CoreTableColumnActionsOptions<TRow>) {
    return new CoreTableColumnActions<TRow>(options);
  }
}
