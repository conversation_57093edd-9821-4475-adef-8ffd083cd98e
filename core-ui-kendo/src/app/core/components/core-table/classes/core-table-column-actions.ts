// Classes
import { CoreTableColumn } from './core-table-column';
// Interfaces
import { CoreTableRow } from '../interfaces/core-table-row';
import {
  _CoreTableColumnActionsOptions,
  CoreTableColumnActionsOptions,
} from '../interfaces/core-table-column-actions-options';

export class CoreTableColumnActions<TRow extends CoreTableRow> extends CoreTableColumn {
  public _options: _CoreTableColumnActionsOptions<TRow>;

  constructor(options: CoreTableColumnActionsOptions<TRow>) {
    super('actions', {
      align: 'center',
      width: 64,
      filter: false,
      sort: false,
      ...options,
      field: '__actions',
      label: '',
    });

    this._options = {
      ...options,
      type: options.type ?? 'menu',
      sticky: options.sticky ?? false,
      actions: options.actions,
    };
  }
}
