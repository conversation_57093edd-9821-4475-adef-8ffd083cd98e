// Angular
import { TemplateRef } from '@angular/core';
// Types
import { CoreTableColumnType } from '../types/core-table-column-type.type';
import { CoreTableColumnAlign } from '../types/core-table-column-align';
import { CoreTableFilterType } from '../types/core-table-filter-type.type';
// Interfaces
import { CoreTableColumnOptions } from '../interfaces/core-table-column-options';
import { CoreSelectOption } from '../../../shared/interfaces/core-select-option';

export class CoreTableColumn {
  public type: CoreTableColumnType;
  public field: string;
  public label: string;
  public filter: CoreTableFilterType;
  public filterField: string;
  public filterOptions: CoreSelectOption<string | number>[];
  public sort: boolean;
  public sortField: string;
  public reorderable: boolean;
  public resizable: boolean;
  public width: number | null;
  public minWidth: number | null;
  public fixed: boolean; // Default: false
  public padding: string; // Default: 0.5rem 0.75rem
  public visible: boolean;
  public index?: number;
  public align: CoreTableColumnAlign; // Default: left
  public sticky: boolean; // Default: false
  public showMenu: boolean; // Default: true
  public templateRef: TemplateRef<unknown> | null; // Default: null

  constructor(type: CoreTableColumnType, options: CoreTableColumnOptions) {
    this.type = type;
    this.field = options.field;
    this.label = options.label;
    this.filter = options.filter ?? false;
    this.filterField = options.filterField ?? options.field;
    this.filterOptions = options.filterOptions ?? [];
    this.sort = options.sort ?? true;
    this.sortField = options.sortField ?? options.field;
    this.reorderable = options.reorderable ?? true;
    this.resizable = options.resizable ?? true;
    this.width = options.width ?? null;
    this.minWidth = options.minWidth ?? null;
    this.fixed = options.fixed ?? false;
    this.padding = options.padding ?? '0.5rem 0.75rem';
    this.visible = options.visible ?? true;
    this.align = options.align ?? 'left';
    this.sticky = options.sticky ?? false;
    this.showMenu = options.showMenu ?? true;
    this.templateRef = options.templateRef ?? null;
  }
}
