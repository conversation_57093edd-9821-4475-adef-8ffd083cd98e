// Classes
import { CoreTableState } from '../classes/core-table-state';
import { CoreTableColumn } from '../classes/core-table-column';
// Types
import { CoreTableDataFn } from '../types/core-table-data-fn.type';
import { CoreTableSelectable } from '../types/core-table-selectable.type';
// Interfaces
import { CoreTableStateOptions } from './core-table-state-options';
import { CoreTableRow } from './core-table-row';
import { CoreTableColumnChangeItem } from './core-table-column-change-item';
import { CoreTableRowReorderEvent } from './core-table-row-reorder-event';
import { CoreTableCustomFooterOptions } from './core-table-footer-options';
import { CoreFilterGroup } from '../../../shared/interfaces/core-filter.interfaces';
import { CoreToolbarOptions } from '../../core-toolbar';
import { CoreTableContextMenuOptions } from './core-table-context-menu-options';

export interface CoreTableOptions<TRow extends CoreTableRow> {
  columns: CoreTableColumn[];
  pageable?: boolean; // Default: false
  pageSize?: number; // Default: 25
  pageSizes?: number[]; // Default: [25, 100, 1000]
  customFooter?: CoreTableCustomFooterOptions;
  state?: CoreTableStateOptions;
  selectable?: CoreTableSelectable; // Default: false
  responsiveMaxWidth?: number | null; // Default null
  showResponsiveToolbar?: boolean; // Default: false
  showSelectColumn?: boolean; // Default: false
  showRowReorderColumn?: boolean; // Default: true
  showSelectedCount?: boolean; // Default: false
  reorderable?: boolean; // Default: false
  rowReorderable?: boolean; // Default: false
  resizable?: boolean; // Default: false
  showDetails?: boolean | ((row: TRow) => boolean); // Default: false
  ngStyle?: Record<string, string>;
  toolbarOptions?: CoreToolbarOptions; // Default: null
  searchBarOptions?: CoreTableSearchBarOptions; // Default: null
  contextMenuOptions?: CoreTableContextMenuOptions[]; // Default: []
  data: (state: CoreTableState) => CoreTableDataFn;
  onSelectionChange?: (rows: TRow[]) => void | Promise<void>;
  onColumnsChange?: (columns: CoreTableColumnChangeItem[]) => void | Promise<void>;
  onRowReorder?: (event: CoreTableRowReorderEvent) => void | Promise<void>;
  onDataLoaded?: () => void | Promise<void>;
  onContextMenu?: (row: TRow | null) => CoreTableContextMenuOptions[] | Promise<CoreTableContextMenuOptions[]>;
  onContextMenuItemClick?: (item: { id: string | number; row: TRow | null }) => void | Promise<void>;
}

export interface CoreTableSearchBarOptions {
  searchButtonLabel?: string; // Default: ''
  value?: string; // Default: ''
  placeholder?: string; // Default: 'Search...'
  disabled?: boolean; // Default: false
  showBarCodeReader?: boolean; // Default: false
  showAdvancedFilter?: boolean; // Default: false
  advancedFilterValue?: CoreFilterGroup; // Default: { logic: 'AND', filters: [] };
  disableOnCallback?: boolean; // Default: false
  valueChangedDelay?: number; // Default: 400
  onSearch?: (value: string | null) => void | Promise<void>;
  onSearchValueChange?: (value: string | null) => void | Promise<void>;
  onSearchValueChanged?: (value: string | null) => void | Promise<void>;
  onApplyAdvancedFilters?: (filters: CoreFilterGroup | null) => void | Promise<void>;
}
