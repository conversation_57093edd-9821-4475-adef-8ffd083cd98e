import { TemplateRef } from '@angular/core';
import { CoreSelectOption } from '../../../shared/interfaces/core-select-option';
import { CoreTableFilterType } from '../types/core-table-filter-type.type';
import { CoreTableColumnAlign } from '../types/core-table-column-align';

export interface CoreTableColumnOptions {
  field: string;
  label: string;
  filter?: CoreTableFilterType;
  filterField?: string;
  filterOptions?: CoreSelectOption<string | number>[];
  sort?: boolean; // Default: true
  sortField?: string;
  reorderable?: boolean; // Default: true
  resizable?: boolean; // Default: true
  width?: number;
  minWidth?: number; // Default: null
  fixed?: boolean; // Default: false
  padding?: string; // Default: 0.5rem 0.75rem
  visible?: boolean; // Default: true
  align?: CoreTableColumnAlign; // Default: left
  sticky?: boolean; // Default: false
  showMenu?: boolean; // Default: true
  templateRef?: TemplateRef<unknown> | null; // Default: null
}
