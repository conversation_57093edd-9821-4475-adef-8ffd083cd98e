// Types
import { CoreTableColumnActionsType } from '../types/core-table-column-actions-type.type';
// Interfaces
import { CoreTableColumnActionsItem } from './core-table-column-actions-item';
import { CoreTableRow } from './core-table-row';

export interface CoreTableColumnActionsOptions<TRow extends CoreTableRow> {
  actions: CoreTableColumnActionsItem<TRow>[] | ((row: TRow) => CoreTableColumnActionsItem<TRow>[]);
  type?: CoreTableColumnActionsType; // Default: 'menu'
  sticky?: boolean; // Default: false
  width?: number; // Default: 64
}

export interface _CoreTableColumnActionsOptions<TRow extends CoreTableRow> {
  type: CoreTableColumnActionsType;
  sticky: boolean;
  actions: CoreTableColumnActionsItem<TRow>[] | ((row: TRow) => CoreTableColumnActionsItem<TRow>[]);
}
