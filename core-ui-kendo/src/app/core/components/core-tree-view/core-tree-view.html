<kendo-treeview
  kendoTreeViewExpandable
  [nodes]="_items()"
  [textField]="'label'"
  [children]="_fetchChildren"
  [hasChildren]="_hasChildren"
  [kendoTreeViewCheckable]="checkable() ? _checkableSettings : false"
  [isExpanded]="_isExpanded"
  (expand)="_handleExpand($event)"
  (collapse)="_handleCollapse($event)"
  [isChecked]="_isItemChecked"
  (checkedChange)="_handleChecking($event)"
>
  <ng-template kendoTreeViewNodeTemplate let-dataItem>
    <div class="tree-item">
      @if(dataItem.icon) {
      <core-icon [icon]="dataItem.icon" />
      }
      <span> {{ dataItem.label }} </span>
    </div>
  </ng-template>
</kendo-treeview>
