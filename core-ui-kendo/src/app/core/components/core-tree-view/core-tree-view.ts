// Angular
import { Component, computed, input, output, viewChild } from '@angular/core';
import { Observable, of } from 'rxjs';
// Kendo UI
import {
  CheckableSettings,
  CheckedState,
  KENDO_TREEVIEW,
  TreeItemLookup,
  TreeViewComponent,
} from '@progress/kendo-angular-treeview';
// Core UI
import { CoreIconModule } from '../core-icon';
// Interfaces
import { CoreTreeViewFlatItem, CoreTreeViewItem, _CoreTreeViewItem } from './core-tree-view.interfaces';

@Component({
  selector: 'core-tree-view',
  templateUrl: './core-tree-view.html',
  styleUrl: './core-tree-view.scss',
  imports: [KENDO_TREEVIEW, CoreIconModule],
})
export class CoreTreeView {
  // [ Public API ]

  public items = input.required<CoreTreeViewItem[]>();
  public checkable = input<boolean>(false);
  public expandChange = output<CoreTreeViewFlatItem[]>();
  public checkedChange = output<CoreTreeViewFlatItem[]>();

  public expandItems(itemIds: (string | number)[], emitEvent = true): void {
    this._expandedItemIds.push(...itemIds);
    if (emitEvent) {
      this._emitExpandedItems();
    }
  }

  public collapseItems(itemIds: (string | number)[], emitEvent = true): void {
    this._expandedItemIds = this._expandedItemIds.filter((i) => !itemIds.includes(i));
    if (emitEvent) {
      this._emitExpandedItems();
    }
  }

  public expandAll(emitEvent = true): void {
    const items = this._getItemsAsFlatList(this._items());
    this.expandItems(
      items.map((i) => i.id),
      emitEvent,
    );
  }

  public collapseAll(emitEvent = true): void {
    const items = this._getItemsAsFlatList(this._items());
    this.collapseItems(
      items.map((i) => i.id),
      emitEvent,
    );
  }

  public checkItems(ids: (string | number)[], emitEvent = true): void {
    this._checkItemsById(ids);
    if (emitEvent) {
      this._emitCheckedItems();
    }
  }

  public uncheckItems(ids: (string | number)[], emitEvent = true): void {
    this._uncheckItemsById(ids);
    if (emitEvent) {
      this._emitCheckedItems();
    }
  }

  public checkAll(emitEvent = true): void {
    const items = this._getItemsAsFlatList(this._items());
    this.checkItems(
      items.map((i) => i.id),
      emitEvent,
    );
  }

  public uncheckAll(emitEvent = true): void {
    const items = this._getItemsAsFlatList(this._items());
    this.uncheckItems(
      items.map((i) => i.id),
      emitEvent,
    );
  }

  // [ Internal ]

  public _items = computed(() => this.convertToInternalTreeItems(this.items()));
  public _checkableSettings: CheckableSettings = {
    checkChildren: true,
    uncheckCollapsedChildren: true,
  };
  public _treeView = viewChild(TreeViewComponent);
  private _checkedItemIds: (string | number)[] = [];
  private _expandedItemIds: (string | number)[] = [];

  // [ Kendo TreeView ]

  public _fetchChildren(node: object): Observable<_CoreTreeViewItem[]> {
    // Return the items collection of the parent node as children.
    if ('children' in node) {
      return of(node.children as _CoreTreeViewItem[]);
    } else {
      return of([]);
    }
  }

  public _hasChildren(node: object): boolean {
    // Check if the parent node has children.
    if ('children' in node) {
      return (node.children as _CoreTreeViewItem[]).length > 0;
    }
    return false;
  }

  public _isExpanded = (dataItem: object) => {
    return this._expandedItemIds.includes((dataItem as _CoreTreeViewItem).id);
  };

  public _handleExpand(e: { dataItem: _CoreTreeViewItem }): void {
    this.expandItems([e.dataItem.id]);
  }

  public _handleCollapse(e: { dataItem: _CoreTreeViewItem }): void {
    this.collapseItems([e.dataItem.id]);
  }

  public _isItemChecked = (dataItem: object): CheckedState => {
    const item = dataItem as _CoreTreeViewItem;
    if (this._checkedItemIds.some((id) => id === item.id) || this._hasAllChildrenChecked(item)) {
      return 'checked';
    } else if (this._hasChildChecked(item)) {
      return 'indeterminate';
    } else {
      return 'none';
    }
  };

  // [ Internal Methods ]

  private _hasAllChildrenChecked(item: _CoreTreeViewItem): boolean {
    if (!item.children) return false;
    const childrenIds = this._getAllChildrenIds(item.id);
    return childrenIds.every((id) => this._checkedItemIds.includes(id));
  }

  private _hasChildChecked(item: _CoreTreeViewItem): boolean {
    if (!item.children) return false;
    for (const child of item.children) {
      if (this._checkedItemIds.some((id) => id === child.id)) {
        return true;
      }
      if (this._hasChildChecked(child)) {
        return true;
      }
    }
    return false;
  }

  public _handleChecking(itemLookup: TreeItemLookup): void {
    const item = itemLookup.item.dataItem as _CoreTreeViewItem;
    const isChecked = this._isItemChecked(item) === 'checked';
    if (isChecked) {
      this._uncheckItemsById([item.id]);
    } else {
      this._checkItemsById([item.id]);
    }
    this._emitCheckedItems();
  }

  private _emitCheckedItems(): void {
    const checkedItems = this._getItemsById(this._checkedItemIds);
    this.checkedChange.emit(checkedItems);
  }

  private _emitExpandedItems(): void {
    const expandedItems = this._getItemsById(this._expandedItemIds);
    this.expandChange.emit(expandedItems);
  }

  private _checkItemsById(itemIds: (string | number)[]): void {
    // Edge case
    if (itemIds.length === 0) return;
    // Get the items and their children
    const items = this._getItemsById(itemIds);
    const checkedItemIds: (string | number)[] = [];
    for (const item of items) {
      checkedItemIds.push(item.id);
      if (item.hasChildren) {
        checkedItemIds.push(...this._getAllChildrenIds(item.id));
      }
    }
    this._checkedItemIds.push(...checkedItemIds);
  }

  private _uncheckItemsById(itemIds: (string | number)[]): void {
    // Edge case
    if (itemIds.length === 0) return;
    // Remove the item and its children
    const itemds = this._getItemsById(itemIds);
    const uncheckedItems: (string | number)[] = [];
    for (const item of itemds) {
      uncheckedItems.push(item.id);
      if (item.hasChildren) {
        uncheckedItems.push(...this._getAllChildrenIds(item.id));
      }
      if (item.parentId) {
        uncheckedItems.push(...this._getAllParentsByChildId(item.id));
      }
    }
    // Update checked state
    this._checkedItemIds = this._checkedItemIds.filter((i) => !uncheckedItems.includes(i));
  }

  private _getItemsById(ids: (string | number)[]): CoreTreeViewFlatItem[] {
    const flatList = this._getItemsAsFlatList(this._items());
    return flatList.filter((i) => ids.includes(i.id));
  }

  private _getItemsAsFlatList(items: _CoreTreeViewItem[]): CoreTreeViewFlatItem[] {
    const flatArray: CoreTreeViewFlatItem[] = [];
    for (const item of items) {
      flatArray.push({
        id: item.id,
        label: item.label,
        parentId: item.parentId ?? null,
        hasChildren: item.children ? item.children.length > 0 : false,
      });
      if (item.children) {
        flatArray.push(...this._getItemsAsFlatList(item.children));
      }
    }
    return flatArray;
  }

  private _getAllChildrenIds(parentId: string | number): (string | number)[] {
    const childrenIds: (string | number)[] = [];
    const allItems = this._getItemsAsFlatList(this._items());
    const children = allItems.filter((i) => i.parentId === parentId);
    for (const child of children) {
      childrenIds.push(child.id);
      childrenIds.push(...this._getAllChildrenIds(child.id));
    }
    return childrenIds;
  }

  private _getAllParentsByChildId(childId: string | number | null): (string | number)[] {
    const parentIds: (string | number)[] = [];
    const allItems = this._getItemsAsFlatList(this._items());
    const child = allItems.find((i) => i.id === childId)!;
    let parent = allItems.find((i) => i.id === child.parentId);
    while (parent !== undefined) {
      parentIds.push(parent.id);
      parent = allItems.find((i) => i.id === parent!.parentId);
    }
    return parentIds;
  }

  // [ Internal Helpers ]

  private convertToInternalTreeItems(
    items: CoreTreeViewItem[],
    parentId: string | number | null = null,
  ): _CoreTreeViewItem[] {
    const internalItems: _CoreTreeViewItem[] = [];
    for (const item of items) {
      if (item.children) {
        internalItems.push({
          ...item,
          parentId,
          children: this.convertToInternalTreeItems(item.children, item.id),
        });
      } else {
        internalItems.push({
          id: item.id,
          label: item.label,
          parentId,
        });
      }
    }
    return internalItems;
  }
}
