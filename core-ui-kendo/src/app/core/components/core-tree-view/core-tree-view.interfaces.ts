import { CoreIcon } from '../core-icon';

export interface CoreTreeViewItem {
  id: string | number;
  label: string;
  icon?: CoreIcon;
  children?: CoreTreeViewItem[];
}

export interface CoreTreeViewFlatItem {
  id: string | number;
  label: string;
  parentId: string | number | null;
  hasChildren: boolean;
}

export interface _CoreTreeViewItem {
  id: string | number;
  label: string;
  parentId: string | number | null;
  children?: _CoreTreeViewItem[];
}
