// Angular
import { Directive, OnDestroy, output, input, OnInit } from '@angular/core';
// RxJS
import { Subject, Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

@Directive({
  selector: '[coreValueChanged]',
  host: { '(valueChange)': 'onValueChange($event)' },
})
export class CoreValueChangedDirective implements OnInit, OnDestroy {
  // [ Inputs ]
  public valueChangeDelay = input<number>(400);

  // [ Outputs ]
  public valueChanged = output<number>();

  // [ Internal ]
  private stream: Subject<number> = new Subject<number>();
  private subscriptions$: Subscription = new Subscription();

  ngOnInit(): void {
    this.subscriptions$.add(
      this.stream
        .pipe(debounceTime(this.valueChangeDelay()))
        .subscribe((value: number) => this.valueChanged.emit(value)),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions$.unsubscribe();
  }

  public onValueChange(value: number): void {
    this.stream.next(value);
  }
}
