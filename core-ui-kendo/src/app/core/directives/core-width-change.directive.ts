/* eslint-disable @angular-eslint/no-output-on-prefix */
import { Directive, ElementRef, Ng<PERSON><PERSON>, On<PERSON><PERSON>roy, output } from '@angular/core';

@Directive({
  selector: '[coreWidthChange]',
})
export class CoreWidthChangeDirective implements OnDestroy {
  // Outputs
  public onWidthChange = output<number>({ alias: 'coreWidthChange' });

  private _resizeObserver: ResizeObserver;

  // eslint-disable-next-line @angular-eslint/prefer-inject
  constructor(host: ElementRef<HTMLElement>, zone: NgZone) {
    // Create a new ResizeObserver instance
    this._resizeObserver = new ResizeObserver((e: ResizeObserverEntry[]) => {
      // Run the callback in the Angular zone
      zone.run(() => this.onWidthChange.emit(e[0]?.contentRect?.width ?? 0));
    });
    // Observe the host element
    this._resizeObserver.observe(host.nativeElement);
  }

  public ngOnDestroy(): void {
    // Disconnect the ResizeObserver
    this._resizeObserver.disconnect();
  }
}
