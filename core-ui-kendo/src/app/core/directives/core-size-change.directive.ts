/* eslint-disable @angular-eslint/no-output-on-prefix */
import { Directive, ElementRef, inject, OnDestroy, OnInit, output } from '@angular/core';
import { debounceTime, Subject, Subscription } from 'rxjs';

@Directive({
  selector: '[coreSizeChange]',
})
export class CoreSizeChangeDirective implements OnInit, OnDestroy {
  public onSizeChange = output<DOMRectReadOnly | null>();

  public onSizeChanged = output<DOMRectReadOnly | null>();
  private sizeChangeSubject = new Subject<DOMRectReadOnly | null>();

  private resizeObserver?: ResizeObserver;
  private subscriptions$ = new Subscription();

  private _host = inject(ElementRef);

  public ngOnInit(): void {
    // Observe resize and emit immediately via onSizeChange
    this.resizeObserver = new ResizeObserver((entries: ResizeObserverEntry[]) => {
      const rect = entries[0]?.contentRect ?? null;
      this.onSizeChange.emit(rect);
      this.sizeChangeSubject.next(rect);
    });
    this.resizeObserver.observe(this._host.nativeElement as Element);

    // Debounce the size changes for onSizeChanged
    this.subscriptions$.add(
      this.sizeChangeSubject.pipe(debounceTime(200)).subscribe((value) => {
        this.onSizeChanged.emit(value);
      }),
    );
  }

  public ngOnDestroy(): void {
    this.resizeObserver?.disconnect();
    this.subscriptions$.unsubscribe();
  }
}
