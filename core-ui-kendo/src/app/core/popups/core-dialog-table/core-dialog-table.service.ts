// Angular
import { Injectable, inject, signal } from '@angular/core';
// Kendo UI
import { DialogCloseResult, DialogService } from '@progress/kendo-angular-dialog';
// Components
import { CoreDialogTableComponent } from './core-dialog-table.component';
// Interfaces
import { _CoreDialogTableOptions, CoreDialogTableOptions } from './core-dialog-table.interfaces';
import { CoreTableRow } from '../../components/core-table/interfaces/core-table-row';

@Injectable()
export class CoreDialogTableService {
  private _kendoDialogService = inject(DialogService);

  public async open<T extends CoreTableRow>(options: CoreDialogTableOptions<T>): Promise<boolean> {
    return new Promise<boolean>((resolve) => {
      // Create options
      const dialogOptions: _CoreDialogTableOptions<T> = {
        table: signal(options.table),
        actionLabel: signal(options.actionLabel ?? 'Submit'),
        cancelLabel: signal(options.cancelLabel ?? 'Cancel'),
        actionBusyLabel: signal(options.actionBusyLabel ?? 'Processing'),
        showAction: signal(options.showAction ?? true),
        onAction: options.onAction,
      };
      // Open dialog
      const dialogRef = this._kendoDialogService.open({
        title: options.title,
        content: CoreDialogTableComponent,
        width: '100vw',
        height: options.height ?? '400px',
        maxWidth: options.width ?? '400px',
        preventAction: () => {
          const dialog = dialogRef.content.instance as CoreDialogTableComponent<T>;
          return !!dialog.isBusy();
        },
      });
      // Set component data
      const dialog = dialogRef.content.instance as CoreDialogTableComponent<T>;
      dialog.options.set(dialogOptions);
      // Listen for close event
      dialogRef.result.subscribe((result) => {
        if (result instanceof DialogCloseResult) {
          resolve(false);
        } else {
          resolve(!!result);
        }
      });
    });
  }
}
