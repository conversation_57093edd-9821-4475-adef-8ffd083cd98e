import { Signal } from '@angular/core';
import { CoreTable } from '../../components/core-table';
import { CoreTableRow } from '../../components/core-table/interfaces/core-table-row';

export interface CoreDialogTableOptions<T extends CoreTableRow> {
  title: string;
  table: CoreTable<T>;
  height?: string; // Default: '400px'
  width?: string; // Default: '400px'
  actionLabel?: string;
  cancelLabel?: string;
  actionBusyLabel?: string; // Default: 'Processing'
  showAction?: boolean; // Default: true
  onAction?: () => Promise<boolean>;
}

export interface _CoreDialogTableOptions<T extends CoreTableRow> {
  table: Signal<CoreTable<T>>;
  actionLabel: Signal<string>;
  cancelLabel: Signal<string>;
  actionBusyLabel: Signal<string>;
  showAction: Signal<boolean>;
  onAction?: () => Promise<boolean>;
}
