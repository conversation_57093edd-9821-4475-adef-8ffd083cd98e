import { Signal } from '@angular/core';
import {
  CoreNetworkSwitchPortStatus,
  CoreNetworkConnectionType,
  CoreNetworkPatchPanelPortStatus,
  CoreNetworkDiagramNodeType,
  CoreNetworkDiagramPortType,
  CoreNetworkDiagramCableType,
  CoreNetworkDiagramCableLayoutDirection as CoreNetworkDiagramFiberCableLayoutDirection,
} from './core-network-diagram.enums';
import { CoreIcon } from '../../../core/components/core-icon';

// [ Public ]

export type CoreNetworkDiagramNode = CoreNetworkSwitch | CoreNetworkPatchPanel;
export type CoreNetworkDiagramPort = CoreNetworkSwitchPort | CoreNetworkPatchPanelPort;
export type CoreNetworkDiagramCable = CoreNetworkDiagramFiberCable | CoreNetworkDiagramEthernetCable;

export interface CoreNetworkSwitch {
  id: string;
  label: string;
  ports: CoreNetworkSwitchPort[];
  columns: number;
  nodeType: CoreNetworkDiagramNodeType.SWITCH;
}

export interface CoreNetworkSwitchPort {
  id: string;
  label: string;
  status: CoreNetworkSwitchPortStatus;
  connectionType: CoreNetworkConnectionType;
}

export interface CoreNetworkPatchPanel {
  id: string;
  label: string;
  ports: CoreNetworkPatchPanelPort[];
  columns: number;
  nodeType: CoreNetworkDiagramNodeType.PATCH_PANEL;
}

export interface CoreNetworkPatchPanelPort {
  id: string;
  label: string;
  status: CoreNetworkPatchPanelPortStatus;
  connectionType: CoreNetworkConnectionType;
}

export interface CoreNetworkDiagramContainer {
  id: string;
  label?: string;
  nodes?: CoreNetworkDiagramNode[];
  cables?: CoreNetworkDiagramCable[];
}

export interface CoreNetworkDiagramFiberCable {
  id: string;
  label: string;
  tubes: CoreNetworkDiagramFiberCableTube[]; // min 1, max 24, otherwise throw error
  cableType: CoreNetworkDiagramCableType.FIBER;
  cssClass?: string; // NODE: width and height are calculated internally
  layoutDirection?: CoreNetworkDiagramFiberCableLayoutDirection; // Defaults to Left To Right
}

export interface CoreNetworkDiagramFiberCableTube {
  id: string;
  label: string;
  strands: CoreNetworkDiagramFiberStrand[]; // min 1, max 24, otherwise throw error
  cssClass?: string; // NODE: width and height are calculated internally
}

export interface CoreNetworkDiagramFiberStrand {
  id: string;
  label: string;
  cssClass?: string; // NODE: width and height are calculated internally
}

export interface CoreNetworkDiagramEthernetCable {
  id: string;
  label: string;
  cableType: CoreNetworkDiagramCableType.ETHERNET;
  cssClass?: string; // NODE: width and height are calculated internally
}

export interface CoreNetworkDiagramConnection {
  id: string;
  outputId: string;
  inputId: string;
  label: string;
}

export interface CoreNetworkDiagramConnectionEvent {
  outputId: string;
  inputId: string;
}

export interface CoreNetworkDiagramState {
  nodesState?: CoreNetworkDiagramElement[];
  containersState?: CoreNetworkDiagramContainerState[];
}

export interface CoreNetworkDiagramContainerState {
  id: string;
  nodesState?: CoreNetworkDiagramElement[];
  cablesState?: CoreNetworkDiagramElement[];
}

export interface CoreNetworkDiagramElement {
  id: string;
  position: CoreNetworkDiagramElementPosition;
}

export interface CoreNetworkDiagramElementPosition {
  x: number;
  y: number;
}

export interface CoreNetworkDiagramOptions {
  connectionCssClass?: string;
  actions?: CoreNetworkDiagramActions;
}

export interface CoreNetworkDiagramActions {
  connectionActions?: CoreNetworkDiagramActionItem[];
  switchActions?: CoreNetworkDiagramActionItem[];
  switchPortActions?: CoreNetworkDiagramActionItem[];
  patchPanelActions?: CoreNetworkDiagramActionItem[];
  patchPanelPortActions?: CoreNetworkDiagramActionItem[];
  fiberCableActions?: CoreNetworkDiagramActionItem[];
  fiberTubeActions?: CoreNetworkDiagramActionItem[];
  fiberStrandActions?: CoreNetworkDiagramActionItem[];
  ethernetCableActions?: CoreNetworkDiagramActionItem[];
}

export interface CoreNetworkDiagramActionItem {
  id: string;
  label: string;
  icon?: CoreIcon;
}

// [ Internal Interfaces ]

export type _CoreNetworkDiagramNodePositionable =
  | _CoreNetworkDiagramSwitchPositionable
  | _CoreNetworkDiagramPatchPanelPositionable;

export type _CoreNetworkDiagramPortPositionable =
  | _CoreNetworkDiagramSwitchPortPositionable
  | _CoreNetworkDiagramPatchPanelPortPositionable;

export type _CoreNetworkDiagramCablePositionable =
  | _CoreNetworkDiagramFiberCablePositionable
  | _CoreNetworkDiagramEthernetCablePositionable;

export interface _CoreNetworkDiagramContainerInternal {
  id: string;
  label?: string;
  nodes?: _CoreNetworkDiagramNodePositionable[];
  cables?: _CoreNetworkDiagramCablePositionable[];
}

export interface _CoreNetworkDiagramFiberCablePositionable {
  id: string;
  label: string;
  position: CoreNetworkDiagramElementPosition;
  tubes: _CoreNetworkDiagramFiberCableTubePositionable[];
  cableType: CoreNetworkDiagramCableType.FIBER;
  cssClass?: string;
  style: Record<string, string>;
  direction: CoreNetworkDiagramFiberCableLayoutDirection;
}

export interface _CoreNetworkDiagramFiberCableTubePositionable {
  id: string;
  label: string;
  position: CoreNetworkDiagramElementPosition;
  strands: _CoreNetworkDiagramFiberStrandPositionable[];
  cssClass?: string;
  style: Record<string, string>;
}

export interface _CoreNetworkDiagramFiberStrandPositionable {
  id: string;
  label: string;
  position: CoreNetworkDiagramElementPosition;
  cssClass?: string;
  style: Record<string, string>;
}

export interface _CoreNetworkDiagramEthernetCablePositionable {
  id: string;
  label: string;
  position: CoreNetworkDiagramElementPosition;
  cableType: CoreNetworkDiagramCableType.ETHERNET;
  cssClass?: string;
  style: Record<string, string>;
}

export interface _CoreNetworkDiagramSwitchPositionable {
  id: string;
  label: string;
  position: CoreNetworkDiagramElementPosition;
  size: _CoreNetworkDiagramElementSize;
  ports: Signal<_CoreNetworkDiagramSwitchPortPositionable[]>;
  portsInUse: Signal<number>;
  nodeType: CoreNetworkDiagramNodeType.SWITCH;
}

export interface _CoreNetworkDiagramSwitchPortPositionable {
  id: string;
  label: string;
  status: CoreNetworkSwitchPortStatus;
  connectionType: CoreNetworkConnectionType;
  position: CoreNetworkDiagramElementPosition;
  portType: CoreNetworkDiagramPortType.SWITCH_PORT;
}

export interface _CoreNetworkDiagramPatchPanelPositionable {
  id: string;
  label: string;
  position: CoreNetworkDiagramElementPosition;
  size: _CoreNetworkDiagramElementSize;
  ports: Signal<_CoreNetworkDiagramPatchPanelPortPositionable[]>;
  portsInUse: Signal<number>;
  nodeType: CoreNetworkDiagramNodeType.PATCH_PANEL;
}

export interface _CoreNetworkDiagramPatchPanelPortPositionable {
  id: string;
  label: string;
  status: CoreNetworkPatchPanelPortStatus;
  connectionType: CoreNetworkConnectionType;
  position: CoreNetworkDiagramElementPosition;
  portType: CoreNetworkDiagramPortType.PATCH_PANEL_PORT;
}

export interface _CoreNetworkDiagramElementSize {
  width: number;
  height: number;
}
