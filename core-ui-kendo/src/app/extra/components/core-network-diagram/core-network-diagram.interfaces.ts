export interface CoreDiagramData {
  switches: CoreNetworkSwitch[];
  patchPanels: CoreNetworkPatchPanel[];
  connections: CoreNetworkConnection[];
}

export interface CoreNetworkSwitch {
  id: string;
  label: string;
  ports: CoreNetworkSwitchPort[];
  columns: number;
}

export interface CoreNetworkSwitchPort {
  id: string;
  label: string;
  status: 'offline' | 'online' | 'not-in-use';
  type: 'ethernet' | 'fiber';
}

export interface CoreNetworkPatchPanel {
  id: string;
  label: string;
  ports: CoreNetworkPatchPanelPort[];
  columns: number;
}

export interface CoreNetworkPatchPanelPort {
  id: string;
  label: string;
  status: 'one-connected' | 'two-connected' | 'not-in-use';
  type: 'ethernet' | 'fiber';
}

export interface CoreNetworkConnection {
  id: string;
  outputId: string;
  inputId: string;
  label: string;
}

export interface CoreNetworkDiagramState {
  switches: CoreNetworkDiagramElement[];
  patchPanels: CoreNetworkDiagramElement[];
}

export interface CoreNetworkDiagramElement {
  id: string;
  position: {
    x: number;
    y: number;
  };
}
