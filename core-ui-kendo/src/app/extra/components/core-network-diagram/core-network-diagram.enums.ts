export enum CoreNetworkSwitchPortStatus {
  OFFLINE = 'offline',
  ONLINE = 'online',
  NOT_IN_USE = 'not-in-use',
}

export enum CoreNetworkConnectionType {
  ETHERNET = 'ethernet',
  FIBER = 'fiber',
}

export enum CoreNetworkPatchPanelPortStatus {
  ONE_CONNECTED = 'one-connected',
  TWO_CONNECTED = 'two-connected',
  NOT_IN_USE = 'not-in-use',
}

export enum CoreNetworkDiagramNodeType {
  SWITCH = 'SWITCH',
  PATCH_PANEL = 'PATCH_PANEL',
}

export enum CoreNetworkDiagramPortType {
  SWITCH_PORT = 'SWITCH_PORT',
  PATCH_PANEL_PORT = 'PATCH_PANEL_PORT',
}

export enum CoreNetworkDiagramCableType {
  FIBER = 'FIBER',
  ETHERNET = 'ETHERNET',
}

export enum CoreNetworkDiagramCableLayoutDirection {
  LEFT_TO_RIGHT = 'LTR',
  RIGHT_TO_LEFT = 'RTL',
}
