:host {
  --core-net-dgrm-primary-color: var(--accent-primary-color);
  --core-net-dgrm-text-color: var(--text);
  --core-net-dgrm-text-invert-color: var(--selected-text);
  --core-net-dgrm-bg: var(--bg);
  --core-net-dgrm-component-bg: var(--component-bg);
  --core-net-dgrm-component-header-bg: var(--header-bg);
  --core-net-dgrm-border-color: var(--border-color);
  --core-net-dgrm-switch-port-status-offline: red;
  --core-net-dgrm-switch-port-status-online: green;
  --core-net-dgrm-patch-panel-port-status-one-connected: #0099ff;
  --core-net-dgrm-patch-panel-port-status-two-connected: green;
}

.container {
  border: 3px solid var(--core-net-dgrm-border-color);
  padding: 1rem;
  border-radius: 1rem;
}

.container-header {
  margin-top: -3.5rem;
  font-size: 1.5rem;
}

.connection-label {
  &:hover {
    color: var(--core-net-dgrm-primary-color);
  }
}

.selected-connection .connection-label {
  color: var(--core-net-dgrm-text-invert-color);
  background: var(--core-net-dgrm-primary-color);
  z-index: 1000;
}

.menu-icon:hover {
  cursor: pointer;
}

.ethernet-cable {
  display: flex;
  flex-direction: column;
  border-radius: 0.5rem;
  background: var(--core-net-dgrm-component-bg);
  border: 1px solid var(--core-net-dgrm-border-color);
  overflow: hidden;
}

.cable-header {
  display: flex;
  align-items: center;
  padding: 0.25rem;
  gap: 0.25rem;
  background: var(--core-net-dgrm-component-header-bg);

  &:hover {
    cursor: move;
  }
}

.ethernet-cable-port {
  border: 1px solid var(--core-net-dgrm-border-color);
  background: var(--core-net-dgrm-bg);
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--core-net-dgrm-text-color);
  position: relative !important;
  margin-top: auto;
  height: 40px;
}

.context-menu-toolbar {
  display: flex;
  flex-direction: row;
  gap: 0.25rem;
  padding: 0.25rem;
  border-radius: 0.25rem;
}

// Connection Overrides
::ng-deep f-flow {
  .f-connection {
    text {
      fill: var(--core-net-dgrm-text-color);
    }

    .f-connection-drag-handle {
      fill: none;
      stroke: none;
    }

    .f-connection-selection {
      stroke-width: 20;
    }

    .f-connection-path {
      stroke: var(--core-net-dgrm-text-color);
      stroke-width: 3;

      &:hover {
        stroke: var(--core-net-dgrm-primary-color);
      }
    }

    &.selected-connection {
      .f-connection-path {
        stroke: var(--core-net-dgrm-primary-color);
        z-index: 1000;
      }

      text {
        fill: var(--core-net-dgrm-primary-color);
      }
    }
  }
}
