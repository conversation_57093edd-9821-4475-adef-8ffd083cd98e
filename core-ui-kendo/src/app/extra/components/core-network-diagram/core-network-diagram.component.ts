// Angular
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  inject,
  input,
  On<PERSON><PERSON>roy,
  OnInit,
  output,
  signal,
  viewChild,
} from '@angular/core';
import { NgClass, NgStyle } from '@angular/common';
import { Subscription } from 'rxjs';
// Kendo UI
import { ContextMenuComponent, KENDO_MENUS } from '@progress/kendo-angular-menu';
import { moreVerticalIcon } from '@progress/kendo-svg-icons';
// Foblex Flow
import {
  FCanvasComponent,
  FFlowModule,
  FCreateConnectionEvent,
  FConnectionContent,
  FDragStartedEvent,
} from '@foblex/flow';
// Core UI
import { CoreButtonModule } from '../../../core/components/core-button';
import { CoreIconModule } from '../../../core/components/core-icon';
// Components
import { BaseNodeComponent } from './components/base-node/base-node.component';
import { FiberCableNodeComponent } from './components/fiber-cable-node/fiber-cable-node.component';
// Services
import { CoreNetworkDiagramService } from './core-network-diagram.service';
// Interfaces
import {
  CoreNetworkDiagramConnection,
  CoreNetworkDiagramConnectionEvent,
  CoreNetworkDiagramContainer,
  CoreNetworkDiagramContainerState,
  CoreNetworkDiagramElement,
  CoreNetworkDiagramNode,
  CoreNetworkDiagramOptions,
  CoreNetworkDiagramState,
  _CoreNetworkDiagramContainerInternal,
  _CoreNetworkDiagramNodePositionable,
} from './core-network-diagram.interfaces';
// Constants
import { CORE_NET_DIAGRAM_CABLE_HEADER_HEIGHT } from './core-network-diagram.constants';

@Component({
  selector: 'core-network-diagram',
  templateUrl: './core-network-diagram.component.html',
  styleUrl: './core-network-diagram.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [CoreNetworkDiagramService],
  imports: [
    FFlowModule,
    FConnectionContent,
    BaseNodeComponent,
    FiberCableNodeComponent,
    KENDO_MENUS,
    NgClass,
    NgStyle,
    CoreButtonModule,
    CoreIconModule,
  ],
})
export class CoreNetworkDiagramComponent implements OnInit, AfterViewInit, OnDestroy {
  // [ Public API ]

  // Inputs
  public nodes = input<CoreNetworkDiagramNode[]>([]);
  public containers = input<CoreNetworkDiagramContainer[]>([]);
  public connections = input<CoreNetworkDiagramConnection[]>([]);
  public state = input<CoreNetworkDiagramState>();
  public options = input<CoreNetworkDiagramOptions>();

  // Outputs
  public stateChange = output<CoreNetworkDiagramState>();
  public connectionCreate = output<CoreNetworkDiagramConnectionEvent>();
  public connectionFailed = output<{ error: string }>();

  // Actions Outputs
  public connectionAction = output<{ actionId: string; connectionId: string }>();
  public switchAction = output<{ switchId: string; actionId: string }>();
  public switchPortAction = output<{ switchId: string; portId: string; actionId: string }>();
  public patchPanelAction = output<{ patchPanelId: string; actionId: string }>();
  public patchPanelPortAction = output<{ patchPanelId: string; portId: string; actionId: string }>();
  public ethernetCableAction = output<{ cableId: string; actionId: string }>();
  public fiberCableAction = output<{ cableId: string; actionId: string }>();
  public fiberTubeAction = output<{ cableId: string; tubeId: string; actionId: string }>();
  public fiberStrandAction = output<{ cableId: string; tubeId: string; strandId: string; actionId: string }>();

  // Methods
  public setNodes(nodes: CoreNetworkDiagramNode[]): void {
    this._nodes.set(this._service.toInternalNodes(nodes, this.state()?.nodesState));
  }

  public setContainers(containers: CoreNetworkDiagramContainer[]): void {
    this._containers.set(this._service.toInternalContainers(containers, this.state()?.containersState));
  }

  public setConnections(connections: CoreNetworkDiagramConnection[]): void {
    this._connections.set(connections);
  }

  public refreshCanvas(): void {
    this._showCanvas.set(false);
    setTimeout(() => {
      this._showCanvas.set(true);
    });
  }

  // [ Internal ]

  // Dependencies

  private _service = inject(CoreNetworkDiagramService);

  public _nodes = signal<_CoreNetworkDiagramNodePositionable[]>([]);
  public _containers = signal<_CoreNetworkDiagramContainerInternal[]>([]);
  public _connections = signal<CoreNetworkDiagramConnection[]>([]);

  public _fCanvas = viewChild(FCanvasComponent);
  public _showCanvas = signal(false);
  public _isDraggingCanvas = signal(false);
  public _connectionMenu = viewChild(ContextMenuComponent);
  public _selectedConnectionId = signal<string | null>(null);
  public _cableHeaderHeight = CORE_NET_DIAGRAM_CABLE_HEADER_HEIGHT;
  public _verticalMoreIcon = moreVerticalIcon;
  public _ethernetCableMenu = viewChild<ContextMenuComponent>('ethernetCableMenu');
  public _selectedEthernetCableId = signal<string | null>(null);

  private _subscriptions$ = new Subscription();

  // Lifecycle Hooks

  public ngOnInit(): void {
    // Set data based on initial inputs
    this._nodes.set(this._service.toInternalNodes(this.nodes(), this.state()?.nodesState));
    this._containers.set(this._service.toInternalContainers(this.containers(), this.state()?.containersState));
    this._connections.set(this.connections());
    // Subscribe to selection reset event
    this._subscriptions$.add(
      this._service.resetAllSelections$.subscribe(() => {
        this.resetSelections();
      }),
    );
  }

  public ngAfterViewInit(): void {
    this.refreshCanvas();
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  // Events

  public onLoaded(): void {
    setTimeout(() => {
      this._fCanvas()?.fitToScreen({ x: 100, y: 100 });
    }, 100);
  }

  public onDragStarted(e: FDragStartedEvent): void {
    this._isDraggingCanvas.set(e.fEventType === 'canvas-move');
  }

  public onDragEnded(): void {
    // Edge case
    if (this._isDraggingCanvas()) {
      return;
    }
    // Logic
    const nodesState: CoreNetworkDiagramElement[] = this._nodes().map((n) => ({ id: n.id, position: n.position }));
    const containersState: CoreNetworkDiagramContainerState[] = this._containers().map((c) => ({
      id: c.id,
      nodesState: c.nodes?.map((n) => ({ id: n.id, position: n.position })),
      cablesState: c.cables?.map((c) => ({ id: c.id, position: c.position })),
    }));
    this.stateChange.emit({ nodesState, containersState });
  }

  public onCreateConnection(e: FCreateConnectionEvent): void {
    // Check if connection was dragged to an input
    if (!e.fInputId) {
      this.connectionFailed.emit({ error: 'Input port not specified' });
      return;
    }
    // Check it accidentally connected to the same port or strand
    if (e.fOutputId === e.fInputId) {
      this.connectionFailed.emit({ error: 'Cannot connect port/strand to itself' });
      return;
    }

    // Emit the new connection
    this.connectionCreate.emit({
      outputId: e.fOutputId,
      inputId: e.fInputId,
    });
  }

  public onSwitchAction(switchId: string, e: { actionId: string }): void {
    this.switchAction.emit({ ...e, switchId });
  }

  public onSwitchPortAction(switchId: string, e: { portId: string; actionId: string }): void {
    this.switchPortAction.emit({ ...e, switchId });
  }

  public onPatchPanelAction(patchPanelId: string, e: { actionId: string }): void {
    this.patchPanelAction.emit({ ...e, patchPanelId });
  }

  public onPatchPanelPortAction(patchPanelId: string, e: { portId: string; actionId: string }): void {
    this.patchPanelPortAction.emit({ ...e, patchPanelId });
  }

  public onSelectEthernetCable(cableId: string, cableRef: HTMLDivElement): void {
    this._selectedEthernetCableId.set(cableId);
    this._ethernetCableMenu()?.show(cableRef);
  }

  public onEthernetCableAction(actionId: string): void {
    this._ethernetCableMenu()?.hide();
    if (this._selectedEthernetCableId()) {
      this.ethernetCableAction.emit({ actionId, cableId: this._selectedEthernetCableId()! });
    }
  }

  public onFiberCableAction(cableId: string, e: { actionId: string }): void {
    this.fiberCableAction.emit({ ...e, cableId });
  }

  public onFiberTubeAction(cableId: string, e: { tubeId: string; actionId: string }): void {
    this.fiberTubeAction.emit({ ...e, cableId });
  }

  public onFiberStrandAction(cableId: string, e: { tubeId: string; strandId: string; actionId: string }): void {
    this.fiberStrandAction.emit({ ...e, cableId });
  }

  public unselectAll(): void {
    this._service.resetAllSelections$.next();
  }

  public onSelectConnection(connectionId: string, connectionElementRef: HTMLDivElement): void {
    // Clear any previous selections
    this._service.resetAllSelections$.next();
    // Set new selection
    this._selectedConnectionId.set(connectionId);
    this._connectionMenu()?.show(connectionElementRef);
  }

  public onConnectionAction(actionId: string): void {
    this._connectionMenu()?.hide();
    if (this._selectedConnectionId()) {
      this.connectionAction.emit({
        actionId,
        connectionId: this._selectedConnectionId()!,
      });
    }
  }

  // [ Private ]

  private resetSelections(): void {
    this._selectedConnectionId.set(null);
    this._selectedEthernetCableId.set(null);
  }
}
