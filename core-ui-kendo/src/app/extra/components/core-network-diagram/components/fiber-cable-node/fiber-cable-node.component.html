<div class="cable" [ngClass]="cable().cssClass" [ngStyle]="cable().style">
  <div #cableHeader class="cable-header" [ngStyle]="{ height: _headerHeight + 'px' }">
    @if (_fiberCableMenu()) {
      <core-icon
        class="menu-icon"
        [icon]="_verticalMoreIcon"
        (click)="_fiberCableMenu()?.show(cableHeader)"
      ></core-icon>
    }
    <span>{{ cable().label }}</span>
  </div>
  <!-- Fiber Tubes -->
  @for (tube of cable().tubes; track tube.id) {
    <div
      class="tube"
      [ngClass]="tube.cssClass"
      [ngStyle]="tube.style"
      [class.selected]="tube.id === _selectedTubeId()"
      fGroup
      [fGroupId]="tube.id"
      [fGroupParentId]="cable().id"
      [(fGroupPosition)]="tube.position"
    >
      @if (cable().direction === "RTL") {
        <div class="color-indicator"></div>
      }
      <div #tubeHeader class="tube-header">
        @if (_fiberTubeMenu()) {
          <core-icon
            class="menu-icon"
            [icon]="_verticalMoreIcon"
            (click)="onShowTubeMenu(tube.id, tubeHeader); $event.stopPropagation()"
          ></core-icon>
        }
        <span>{{ tube.label }}</span>
      </div>
      <!-- Tube Color Indicator -->
      @if (cable().direction === "LTR") {
        <div class="color-indicator"></div>
      }
      <!-- Fiber Strands -->
      @for (strand of tube.strands; track strand.id) {
        <button
          #strandRef
          class="strand"
          [ngStyle]="strand.style"
          [ngClass]="strand.cssClass"
          [class.selected]="strand.id === _selectedStrandId()"
          fNode
          [fNodeId]="strand.id"
          [fNodeParentId]="tube.id"
          fNodeOutput
          [fOutputId]="strand.id"
          [fOutputConnectableSide]="cable().direction === 'LTR' ? 'right' : 'left'"
          fNodeInput
          [fInputId]="strand.id"
          [fInputConnectableSide]="cable().direction === 'LTR' ? 'right' : 'left'"
          [(fNodePosition)]="strand.position"
          (click)="onShowStrandMenu(tube.id, strand.id, strandRef); $event.stopPropagation()"
        >
          <div
            class="color-indicator"
            [class.strand-color-indicator-left]="cable().direction === 'RTL'"
            [class.strand-color-indicator-right]="cable().direction === 'LTR'"
          ></div>
          {{ strand.label }}
        </button>
      }
    </div>
  }
</div>
<!-- Fiber Cable Actions -->
@if (fiberCableActions()) {
  <kendo-contextmenu #fiberCableMenu [filter]="'.cable-header'">
    <ng-template kendoContextMenuTemplate>
      <div class="context-menu-toolbar">
        @for (action of fiberCableActions(); track action.id) {
          @if (action.icon) {
            <core-button [icon]="action.icon" [label]="action.label" (click)="onFiberCableAction(action.id)" />
          } @else {
            <core-button [label]="action.label" (click)="onFiberCableAction(action.id)" />
          }
        }
      </div>
    </ng-template>
  </kendo-contextmenu>
}
<!-- Fiber Tube Actions -->
@if (fiberTubeActions()) {
  <kendo-contextmenu #fiberTubeMenu [filter]="'.tube-header'">
    <ng-template kendoContextMenuTemplate>
      <div class="context-menu-toolbar">
        @for (action of fiberTubeActions(); track action.id) {
          @if (action.icon) {
            <core-button [icon]="action.icon" [label]="action.label" (click)="onFiberTubeAction(action.id)" />
          } @else {
            <core-button [label]="action.label" (click)="onFiberTubeAction(action.id)" />
          }
        }
      </div>
    </ng-template>
  </kendo-contextmenu>
}
<!-- Fiber Strand Actions -->
@if (fiberStrandActions()) {
  <kendo-contextmenu #fiberStrandMenu [filter]="'.strand'">
    <ng-template kendoContextMenuTemplate>
      <div class="context-menu-toolbar">
        @for (action of fiberStrandActions(); track action.id) {
          @if (action.icon) {
            <core-button [icon]="action.icon" [label]="action.label" (click)="onFiberStrandAction(action.id)" />
          } @else {
            <core-button [label]="action.label" (click)="onFiberStrandAction(action.id)" />
          }
        }
      </div>
    </ng-template>
  </kendo-contextmenu>
}
