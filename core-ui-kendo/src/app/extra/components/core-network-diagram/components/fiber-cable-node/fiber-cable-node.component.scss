.cable {
  border-radius: 0.5rem;
  background: var(--core-net-dgrm-component-bg);
  border: 1px solid var(--core-net-dgrm-border-color);
  overflow: hidden;
}

.cable-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
  gap: 0.25rem;
  background: var(--core-net-dgrm-component-header-bg);
  color: var(--core-net-dgrm-text-color);

  &:hover {
    cursor: move;
  }
}

.tube {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  background: var(--core-net-dgrm-component-bg);
  border: 1px solid transparent;
  color: var(--core-net-dgrm-text-color);
}

.tube-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
  gap: 0.25rem;
}

.strand {
  border: 1px solid transparent;
  background: var(--core-net-dgrm-component-bg);
  color: var(--core-net-dgrm-text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.color-indicator {
  width: 0.5rem;
  height: 100%;
}

.strand-color-indicator-left {
  position: absolute;
  left: 0;
}

.strand-color-indicator-right {
  position: absolute;
  right: 0;
}

.context-menu-toolbar {
  display: flex;
  flex-direction: row;
  gap: 0.25rem;
  padding: 0.25rem;
  border-radius: 0.25rem;
}

.menu-icon:hover {
  cursor: pointer;
}

.selected {
  border: 1px solid var(--core-net-dgrm-primary-color);
}
