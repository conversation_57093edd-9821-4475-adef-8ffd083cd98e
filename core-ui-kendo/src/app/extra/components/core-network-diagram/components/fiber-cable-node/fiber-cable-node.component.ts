// Angular
import { Component, inject, input, On<PERSON><PERSON>roy, OnInit, output, signal, viewChild } from '@angular/core';
import { Subscription } from 'rxjs';
import { NgClass, NgStyle } from '@angular/common';
// Kendo UI
import { ContextMenuComponent, KENDO_MENUS } from '@progress/kendo-angular-menu';
import { moreVerticalIcon } from '@progress/kendo-svg-icons';
// Foblex Flow
import { FFlowModule } from '@foblex/flow';
// Core UI
import {
  _CoreNetworkDiagramFiberCablePositionable,
  CoreNetworkDiagramActionItem,
} from '../../core-network-diagram.interfaces';
import { CoreIconModule } from '../../../../../core/components/core-icon';
import { CoreButtonModule } from '../../../../../core/components/core-button';
// Services
import { CoreNetworkDiagramService } from '../../core-network-diagram.service';
// Constants
import { CORE_NET_DIAGRAM_CABLE_HEADER_HEIGHT } from '../../core-network-diagram.constants';

@Component({
  selector: 'core-fiber-cable-node',
  imports: [FFlowModule, NgStyle, NgClass, CoreIconModule, CoreButtonModule, KENDO_MENUS],
  templateUrl: './fiber-cable-node.component.html',
  styleUrl: './fiber-cable-node.component.scss',
})
export class FiberCableNodeComponent implements OnInit, OnDestroy {
  public cable = input.required<_CoreNetworkDiagramFiberCablePositionable>();
  public fiberCableActions = input<CoreNetworkDiagramActionItem[]>();
  public fiberTubeActions = input<CoreNetworkDiagramActionItem[]>();
  public fiberStrandActions = input<CoreNetworkDiagramActionItem[]>();

  public fiberCableAction = output<{ actionId: string }>();
  public fiberTubeAction = output<{ tubeId: string; actionId: string }>();
  public fiberStrandAction = output<{ tubeId: string; strandId: string; actionId: string }>();

  public _headerHeight = CORE_NET_DIAGRAM_CABLE_HEADER_HEIGHT;
  public _verticalMoreIcon = moreVerticalIcon;

  public _fiberCableMenu = viewChild<ContextMenuComponent>('fiberCableMenu');
  public _fiberTubeMenu = viewChild<ContextMenuComponent>('fiberTubeMenu');
  public _fiberStrandMenu = viewChild<ContextMenuComponent>('fiberStrandMenu');
  public _selectedTubeId = signal<string | null>(null);
  public _selectedStrandId = signal<string | null>(null);
  private _service = inject(CoreNetworkDiagramService);
  private _subscriptions$ = new Subscription();

  public ngOnInit(): void {
    this._subscriptions$.add(
      this._service.resetAllSelections$.subscribe(() => {
        this.resetAllSelections();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  public onFiberCableAction(actionId: string): void {
    // Clear any previous selections
    this._service.resetAllSelections$.next();
    // Set new selection
    this._fiberCableMenu()?.hide();
    this.fiberCableAction.emit({ actionId });
  }

  public onShowTubeMenu(tubeId: string, targetRef: HTMLDivElement): void {
    // Clear any previous selections
    this._service.resetAllSelections$.next();
    // Set new selection
    this._selectedTubeId.set(tubeId);
    this._fiberTubeMenu()?.show(targetRef);
  }

  public onFiberTubeAction(actionId: string): void {
    this._fiberTubeMenu()?.hide();
    if (this._selectedTubeId()) {
      this.fiberTubeAction.emit({ actionId, tubeId: this._selectedTubeId()! });
    }
  }

  public onShowStrandMenu(tubeId: string, strandId: string, targetRef: HTMLButtonElement): void {
    // Clear any previous selections
    this._service.resetAllSelections$.next();
    // Set new selection
    this._selectedTubeId.set(tubeId);
    this._selectedStrandId.set(strandId);
    this._fiberStrandMenu()?.show(targetRef);
  }

  public onFiberStrandAction(actionId: string): void {
    this._fiberStrandMenu()?.hide();
    if (this._selectedTubeId() && this._selectedStrandId()) {
      this.fiberStrandAction.emit({ actionId, tubeId: this._selectedTubeId()!, strandId: this._selectedStrandId()! });
    }
  }

  // [ Private ]

  private resetAllSelections(): void {
    this._selectedTubeId.set(null);
    this._selectedStrandId.set(null);
  }
}
