<div
  class="patch-panel"
  [ngStyle]="{
    width: patchPanel().size.width + 'px',
    height: patchPanel().size.height + 'px',
  }"
>
  <!-- Header -->
  <div
    #patchPanelHeader
    fDragHandle
    class="patch-panel-header"
    [ngStyle]="{
      height: _headerHeight + 'px',
      padding: '0 ' + (_padding + 'px'),
    }"
  >
    @if (_patchPanelMenu()) {
      <core-icon
        class="menu-icon"
        [icon]="_verticalMoreIcon"
        (click)="_patchPanelMenu()!.show(patchPanelHeader)"
      ></core-icon>
    }
    {{ patchPanel().label }}
    <div class="spacer"></div>
    Ports Used: {{ patchPanel().portsInUse() }} of {{ patchPanel().ports().length }}
  </div>
  <!-- Ports -->
  @for (port of patchPanel().ports(); track port.id) {
    <button
      class="port"
      #portRef
      fNode
      [ngStyle]="{
        width: _portSize.width + 'px',
        height: _portSize.height + 'px',
      }"
      [class.one-connected]="port.status === 'one-connected'"
      [class.two-connected]="port.status === 'two-connected'"
      [class.selected-port]="port.id === _selectedPortId()"
      [fNodeParentId]="patchPanel().id"
      [fNodeId]="port.id"
      fNodeOutput
      [fOutputId]="port.id"
      [fOutputConnectableSide]="'bottom'"
      fNodeInput
      [fInputId]="port.id"
      [fInputConnectableSide]="'top'"
      [(fNodePosition)]="port.position"
      (click)="onSelectPort(port.id, portRef); $event.stopPropagation()"
    >
      <span>{{ port.label }}</span>
      <span class="port-connection-type-indicator">{{ port.connectionType === "ethernet" ? "E" : "F" }}</span>
    </button>
  }
</div>
<!-- Patch Panel Actions -->
@if (patchPanelActions()) {
  <kendo-contextmenu #patchPanelMenu [filter]="'.patch-panel-header'">
    <ng-template kendoContextMenuTemplate>
      <div class="context-menu-toolbar">
        @for (action of patchPanelActions(); track action.id) {
          @if (action.icon) {
            <core-button [icon]="action.icon" [label]="action.label" (click)="onPatchPanelAction(action.id)" />
          } @else {
            <core-button [label]="action.label" (click)="onPatchPanelAction(action.id)" />
          }
        }
      </div>
    </ng-template>
  </kendo-contextmenu>
}

<!-- Patch Panel Port Actions -->
@if (patchPanelPortActions()) {
  <kendo-contextmenu #patchPanelPortMenu [filter]="'.selected-port'">
    <ng-template kendoContextMenuTemplate>
      <div class="context-menu-toolbar">
        @for (action of patchPanelPortActions(); track action.id) {
          @if (action.icon) {
            <core-button [icon]="action.icon" [label]="action.label" (click)="onPatchPanelPortAction(action.id)" />
          } @else {
            <core-button [label]="action.label" (click)="onPatchPanelPortAction(action.id)" />
          }
        }
      </div>
    </ng-template>
  </kendo-contextmenu>
}
