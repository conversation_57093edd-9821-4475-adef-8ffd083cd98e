// Angular
import { Component, inject, input, On<PERSON><PERSON>roy, OnInit, output, signal, viewChild } from '@angular/core';
import { NgStyle } from '@angular/common';
import { Subscription } from 'rxjs';
// Kendo UI
import { ContextMenuComponent, KENDO_MENUS } from '@progress/kendo-angular-menu';
import { moreVerticalIcon } from '@progress/kendo-svg-icons';
// Third party Libs
import { FFlowModule } from '@foblex/flow';
// Core UI
import { CoreButtonModule } from '../../../../../core/components/core-button';
import { CoreIconModule } from '../../../../../core/components/core-icon';
// Services
import { CoreNetworkDiagramService } from '../../core-network-diagram.service';
// Interfaces
import {
  _CoreNetworkDiagramElementSize,
  _CoreNetworkDiagramPatchPanelPositionable,
  CoreNetworkDiagramActionItem,
} from '../../core-network-diagram.interfaces';
// Constants
import {
  CORE_NET_DIAGRAM_NODE_HEADER_HEIGHT,
  CORE_NET_DIAGRAM_NODE_PADDING,
  CORE_NET_DIAGRAM_PORT_SIZE,
} from '../../core-network-diagram.constants';

@Component({
  selector: 'core-patch-panel-node',
  templateUrl: './patch-panel-node.component.html',
  styleUrl: './patch-panel-node.component.scss',
  imports: [FFlowModule, NgStyle, KENDO_MENUS, CoreButtonModule, CoreIconModule],
})
export class PatchPanelNodeComponent implements OnInit, OnDestroy {
  public patchPanel = input.required<_CoreNetworkDiagramPatchPanelPositionable>();
  public patchPanelActions = input<CoreNetworkDiagramActionItem[]>();
  public patchPanelPortActions = input<CoreNetworkDiagramActionItem[]>();
  public patchPanelAction = output<{ actionId: string }>();
  public patchPanelPortAction = output<{ actionId: string; portId: string }>();

  public _headerHeight = CORE_NET_DIAGRAM_NODE_HEADER_HEIGHT;
  public _padding = CORE_NET_DIAGRAM_NODE_PADDING;
  public _portSize: _CoreNetworkDiagramElementSize = CORE_NET_DIAGRAM_PORT_SIZE;
  public _verticalMoreIcon = moreVerticalIcon;
  public _patchPanelMenu = viewChild<ContextMenuComponent>('patchPanelMenu');
  public _patchPanelPortMenu = viewChild<ContextMenuComponent>('patchPanelPortMenu');
  public _selectedPortId = signal<string | null>(null);

  private _service = inject(CoreNetworkDiagramService);
  private _subscriptions$ = new Subscription();

  public ngOnInit(): void {
    this._subscriptions$.add(
      this._service.resetAllSelections$.subscribe(() => {
        this.resetAllSelections();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  public onPatchPanelAction(actionId: string): void {
    this._patchPanelMenu()?.hide();
    this.patchPanelAction.emit({ actionId });
  }

  public onPatchPanelPortAction(actionId: string): void {
    this._patchPanelPortMenu()?.hide();
    if (!this._selectedPortId()) {
      return;
    }
    this.patchPanelPortAction.emit({ actionId, portId: this._selectedPortId()! });
  }

  public onSelectPort(portId: string, portRef: HTMLButtonElement): void {
    // Clear any previous selections
    this._service.resetAllSelections$.next();
    // Set new selection
    this._selectedPortId.set(portId);
    this._patchPanelPortMenu()?.show(portRef);
  }

  // [ Private ]

  private resetAllSelections(): void {
    this._selectedPortId.set(null);
  }
}
