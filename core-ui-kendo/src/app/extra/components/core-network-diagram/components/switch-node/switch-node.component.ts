// Angular
import { Component, inject, input, On<PERSON><PERSON>roy, OnInit, output, signal, viewChild } from '@angular/core';
import { NgStyle } from '@angular/common';
import { Subscription } from 'rxjs';
// Kendo UI
import { moreVerticalIcon } from '@progress/kendo-svg-icons';
import { ContextMenuComponent, KENDO_MENUS } from '@progress/kendo-angular-menu';
// Core UI
import { CoreButtonModule } from '../../../../../core/components/core-button';
import { CoreIconModule } from '../../../../../core/components/core-icon';
// Third party Libs
import { FFlowModule } from '@foblex/flow';
// Services
import { CoreNetworkDiagramService } from '../../core-network-diagram.service';
// Interfaces
import {
  _CoreNetworkDiagramElementSize,
  _CoreNetworkDiagramSwitchPositionable,
  CoreNetworkDiagramActionItem,
} from '../../core-network-diagram.interfaces';
// Constants
import {
  CORE_NET_DIAGRAM_NODE_HEADER_HEIGHT,
  CORE_NET_DIAGRAM_NODE_PADDING,
  CORE_NET_DIAGRAM_PORT_SIZE,
} from '../../core-network-diagram.constants';

@Component({
  selector: 'core-switch-node',
  templateUrl: './switch-node.component.html',
  styleUrl: './switch-node.component.scss',
  imports: [FFlowModule, NgStyle, KENDO_MENUS, CoreButtonModule, CoreIconModule],
})
export class SwitchNodeComponent implements OnInit, OnDestroy {
  public switch = input.required<_CoreNetworkDiagramSwitchPositionable>();
  public switchActions = input<CoreNetworkDiagramActionItem[]>();
  public switchPortActions = input<CoreNetworkDiagramActionItem[]>();
  public switchAction = output<{ actionId: string }>();
  public switchPortAction = output<{ actionId: string; portId: string }>();

  public _headerHeight = CORE_NET_DIAGRAM_NODE_HEADER_HEIGHT;
  public _padding = CORE_NET_DIAGRAM_NODE_PADDING;
  public _portSize: _CoreNetworkDiagramElementSize = CORE_NET_DIAGRAM_PORT_SIZE;
  public _verticalMoreIcon = moreVerticalIcon;
  public _switchMenu = viewChild<ContextMenuComponent>('switchMenu');
  public _switchPortMenu = viewChild<ContextMenuComponent>('switchPortMenu');
  public _selectedPortId = signal<string | null>(null);

  private _service = inject(CoreNetworkDiagramService);
  private _subscriptions$ = new Subscription();

  public ngOnInit(): void {
    this._subscriptions$.add(
      this._service.resetAllSelections$.subscribe(() => {
        this.resetAllSelections();
      }),
    );
  }

  public ngOnDestroy(): void {
    this._subscriptions$.unsubscribe();
  }

  public onSwitchAction(actionId: string): void {
    this._switchMenu()?.hide();
    this.switchAction.emit({ actionId });
  }

  public onSwitchPortAction(actionId: string): void {
    this._switchPortMenu()?.hide();
    if (!this._selectedPortId()) {
      return;
    }
    this.switchPortAction.emit({ actionId, portId: this._selectedPortId()! });
  }

  public onSelectPort(portId: string, portRef: HTMLButtonElement): void {
    // Clear any previous selections
    this._service.resetAllSelections$.next();
    // Set new selection
    this._selectedPortId.set(portId);
    this._switchPortMenu()?.show(portRef);
  }

  // [ Private ]

  private resetAllSelections(): void {
    this._selectedPortId.set(null);
  }
}
