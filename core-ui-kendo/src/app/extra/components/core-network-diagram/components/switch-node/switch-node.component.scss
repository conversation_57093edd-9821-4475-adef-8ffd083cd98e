.switch {
  border: 1px solid var(--core-net-dgrm-border-color);
  border-radius: 1rem;
  background: var(--core-net-dgrm-component-bg);
  color: var(--core-net-dgrm-text-color);
  overflow: hidden;
}

.switch-header {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  gap: 0.25rem;
  background: var(--core-net-dgrm-component-header-bg);

  &:hover {
    cursor: move;
  }
}

.spacer {
  flex: 1 1 0;
}

.port {
  border: 1px solid var(--core-net-dgrm-border-color);
  background: var(--core-net-dgrm-bg);
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--core-net-dgrm-text-color);
}

.offline {
  background: var(--core-net-dgrm-switch-port-status-offline);
  color: var(--core-net-dgrm-text-invert-color);
}

.online {
  background: var(--core-net-dgrm-switch-port-status-online);
  color: var(--core-net-dgrm-text-invert-color);
}

.selected-port {
  border-color: var(--core-net-dgrm-primary-color);
}

.port-connection-type-indicator {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  font-size: 0.65rem;
}

.context-menu-toolbar {
  display: flex;
  flex-direction: row;
  gap: 0.25rem;
  padding: 0.25rem;
  border-radius: 0.25rem;
}

.menu-icon:hover {
  cursor: pointer;
}
