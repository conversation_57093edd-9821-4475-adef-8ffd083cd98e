<div
  class="switch"
  [ngStyle]="{
    width: switch().size.width + 'px',
    height: switch().size.height + 'px',
  }"
>
  <!-- Header -->
  <div
    fDragHandle
    #switchHeader
    class="switch-header"
    [ngStyle]="{
      height: _headerHeight + 'px',
      padding: '0 ' + (_padding + 'px'),
    }"
  >
    @if (_switchMenu()) {
      <core-icon class="menu-icon" [icon]="_verticalMoreIcon" (click)="_switchMenu()!.show(switchHeader)"></core-icon>
    }
    {{ switch().label }}
    <div class="spacer"></div>
    Ports Used: {{ switch().portsInUse() }} of {{ switch().ports().length }}
  </div>
  <!-- Ports -->
  @for (port of switch().ports(); track port.id) {
    <button
      class="port"
      #portRef
      fNode
      [ngStyle]="{
        width: _portSize.width + 'px',
        height: _portSize.height + 'px',
      }"
      [class.offline]="port.status === 'offline'"
      [class.online]="port.status === 'online'"
      [class.selected-port]="port.id === _selectedPortId()"
      [fNodeParentId]="switch().id"
      [fNodeId]="port.id"
      fNodeOutput
      [fOutputId]="port.id"
      [fOutputConnectableSide]="'bottom'"
      fNodeInput
      [fInputId]="port.id"
      [fInputConnectableSide]="'top'"
      [(fNodePosition)]="port.position"
      (click)="onSelectPort(port.id, portRef); $event.stopPropagation()"
    >
      <span>{{ port.label }}</span>
      <span class="port-connection-type-indicator">{{ port.connectionType === "ethernet" ? "E" : "F" }}</span>
    </button>
  }
</div>

<!-- Switch Actions -->
@if (switchActions()) {
  <kendo-contextmenu #switchMenu [filter]="'.switch-header'">
    <ng-template kendoContextMenuTemplate>
      <div class="context-menu-toolbar">
        @for (action of switchActions(); track action.id) {
          @if (action.icon) {
            <core-button [icon]="action.icon" [label]="action.label" (click)="onSwitchAction(action.id)" />
          } @else {
            <core-button [label]="action.label" (click)="onSwitchAction(action.id)" />
          }
        }
      </div>
    </ng-template>
  </kendo-contextmenu>
}

<!-- Switch Port Actions -->
@if (switchPortActions()) {
  <kendo-contextmenu #switchPortMenu [filter]="'.selected-port'">
    <ng-template kendoContextMenuTemplate>
      <div class="context-menu-toolbar">
        @for (action of switchPortActions(); track action.id) {
          @if (action.icon) {
            <core-button [icon]="action.icon" [label]="action.label" (click)="onSwitchPortAction(action.id)" />
          } @else {
            <core-button [label]="action.label" (click)="onSwitchPortAction(action.id)" />
          }
        }
      </div>
    </ng-template>
  </kendo-contextmenu>
}
