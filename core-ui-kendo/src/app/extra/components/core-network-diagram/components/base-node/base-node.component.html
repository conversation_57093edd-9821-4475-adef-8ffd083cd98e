@switch (node().nodeType) {
  @case ("SWITCH") {
    <core-switch-node
      [switch]="$asSwitch(node())"
      [switchActions]="switchActions()"
      [switchPortActions]="switchPortActions()"
      (switchAction)="switchAction.emit($event)"
      (switchPortAction)="switchPortAction.emit($event)"
    />
  }
  @case ("PATCH_PANEL") {
    <core-patch-panel-node
      [patchPanel]="$asPatchPanel(node())"
      [patchPanelActions]="patchPanelActions()"
      [patchPanelPortActions]="patchPanelPortActions()"
      (patchPanelAction)="patchPanelAction.emit($event)"
      (patchPanelPortAction)="patchPanelPortAction.emit($event)"
    />
  }
}
