// Angular
import { Component, input, output } from '@angular/core';
// Third party Libs
import { FFlowModule } from '@foblex/flow';
// Components
import { PatchPanelNodeComponent } from '../patch-panel-node/patch-panel-node.component';
import { SwitchNodeComponent } from '../switch-node/switch-node.component';
// Interfaces
import {
  _CoreNetworkDiagramNodePositionable,
  _CoreNetworkDiagramPatchPanelPositionable,
  _CoreNetworkDiagramSwitchPositionable,
  CoreNetworkDiagramActionItem,
} from '../../core-network-diagram.interfaces';

@Component({
  selector: 'core-base-node',
  templateUrl: './base-node.component.html',
  styleUrl: './base-node.component.scss',
  imports: [FFlowModule, SwitchNodeComponent, PatchPanelNodeComponent],
})
export class BaseNodeComponent {
  public node = input.required<_CoreNetworkDiagramNodePositionable>();
  public switchActions = input<CoreNetworkDiagramActionItem[]>();
  public switchPortActions = input<CoreNetworkDiagramActionItem[]>();
  public patchPanelActions = input<CoreNetworkDiagramActionItem[]>();
  public patchPanelPortActions = input<CoreNetworkDiagramActionItem[]>();

  public switchAction = output<{ actionId: string }>();
  public switchPortAction = output<{ actionId: string; portId: string }>();
  public patchPanelAction = output<{ actionId: string }>();
  public patchPanelPortAction = output<{ actionId: string; portId: string }>();

  public $asSwitch(node: _CoreNetworkDiagramNodePositionable): _CoreNetworkDiagramSwitchPositionable {
    return node as _CoreNetworkDiagramSwitchPositionable;
  }

  public $asPatchPanel(node: _CoreNetworkDiagramNodePositionable): _CoreNetworkDiagramPatchPanelPositionable {
    return node as _CoreNetworkDiagramPatchPanelPositionable;
  }
}
