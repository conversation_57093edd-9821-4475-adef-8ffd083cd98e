// Angular
import { computed, Injectable, signal } from '@angular/core';
import { Subject } from 'rxjs';
// Enums
import {
  CoreNetworkDiagramCableLayoutDirection,
  CoreNetworkDiagramCableType,
  CoreNetworkDiagramNodeType,
  CoreNetworkDiagramPortType,
  CoreNetworkPatchPanelPortStatus,
  CoreNetworkSwitchPortStatus,
} from './core-network-diagram.enums';
// Interfaces
import {
  _CoreNetworkDiagramCablePositionable,
  _CoreNetworkDiagramContainerInternal,
  _CoreNetworkDiagramElementSize,
  _CoreNetworkDiagramFiberCableTubePositionable,
  _CoreNetworkDiagramFiberStrandPositionable,
  _CoreNetworkDiagramNodePositionable,
  _CoreNetworkDiagramPatchPanelPortPositionable,
  _CoreNetworkDiagramPortPositionable,
  _CoreNetworkDiagramSwitchPortPositionable,
  CoreNetworkDiagramCable,
  CoreNetworkDiagramContainer,
  CoreNetworkDiagramContainerState,
  CoreNetworkDiagramElement,
  CoreNetworkDiagramElementPosition,
  CoreNetworkDiagramEthernetCable,
  CoreNetworkDiagramFiberCable,
  CoreNetworkDiagramFiberCableTube,
  CoreNetworkDiagramFiberStrand,
  CoreNetworkDiagramNode,
  CoreNetworkDiagramPort,
} from './core-network-diagram.interfaces';
// Constants
import {
  CORE_NET_DIAGRAM_PORT_SIZE,
  CORE_NET_DIAGRAM_NODE_HEADER_HEIGHT,
  CORE_NET_DIAGRAM_NODE_PADDING,
  CORE_NET_DIAGRAM_NODE_CHILDREN_GAP,
  CORE_NET_DIAGRAM_NODES_GAP,
  CORE_NET_DIAGRAM_FIBER_TUBE_WIDTH,
  CORE_NET_DIAGRAM_FIBER_STRAND_WIDTH,
  CORE_NET_DIAGRAM_FIBER_STRAND_HEIGHT,
  CORE_NET_DIAGRAM_CABLE_HEADER_HEIGHT,
  CORE_NET_DIAGRAM_ETHERNET_CABLE_HEIGHT,
  CORE_NET_DIAGRAM_ETHERNET_CABLE_WIDTH,
} from './core-network-diagram.constants';

@Injectable()
export class CoreNetworkDiagramService {
  public resetAllSelections$ = new Subject<void>();

  // Positioning variables
  public _availablePosition: CoreNetworkDiagramElementPosition = { x: 0, y: 0 };

  // Sizing variables
  public _portSize: _CoreNetworkDiagramElementSize = CORE_NET_DIAGRAM_PORT_SIZE;
  public _nodeHeaderHeight = CORE_NET_DIAGRAM_NODE_HEADER_HEIGHT;
  public _nodePadding = CORE_NET_DIAGRAM_NODE_PADDING;
  public _elementsGap = CORE_NET_DIAGRAM_NODE_CHILDREN_GAP;
  public _nodesGap = CORE_NET_DIAGRAM_NODES_GAP;

  // [ Public Interfaces ]

  public toInternalNodes(
    nodes: CoreNetworkDiagramNode[],
    nodesState?: CoreNetworkDiagramElement[],
  ): _CoreNetworkDiagramNodePositionable[] {
    return this.getNodesPositioned(nodes, nodesState);
  }

  public toInternalContainers(
    containers: CoreNetworkDiagramContainer[],
    containersState?: CoreNetworkDiagramContainerState[],
  ): _CoreNetworkDiagramContainerInternal[] {
    const internalContainers: _CoreNetworkDiagramContainerInternal[] = [];
    for (const container of containers) {
      const state = containersState?.find((s) => s.id === container.id);
      const internalContainer = this.getInternalContainer(container, state);
      internalContainers.push(internalContainer);
    }
    return internalContainers;
  }

  // [ Internal ]

  private getInternalContainer(
    container: CoreNetworkDiagramContainer,
    containerState?: CoreNetworkDiagramContainerState,
  ): _CoreNetworkDiagramContainerInternal {
    // Position the nodes
    const nodes = this.getNodesPositioned(container.nodes ?? [], containerState?.nodesState);
    const cables = this.getCablesPositioned(container.cables ?? [], containerState?.cablesState);
    return {
      id: container.id,
      label: container.label,
      nodes,
      cables,
    };
  }

  private getNodesPositioned(
    nodes: CoreNetworkDiagramNode[],
    nodesState?: CoreNetworkDiagramElement[],
  ): _CoreNetworkDiagramNodePositionable[] {
    const positionedNodes: _CoreNetworkDiagramNodePositionable[] = [];
    for (const node of nodes) {
      const position = nodesState ? nodesState.find((s) => s.id === node.id)?.position : undefined;
      const positionedNode: _CoreNetworkDiagramNodePositionable = this.getNodePositioned(node, position);
      positionedNodes.push(positionedNode);
    }
    return positionedNodes;
  }

  private getCablesPositioned(
    cables: CoreNetworkDiagramCable[],
    cablesState?: CoreNetworkDiagramElement[],
  ): _CoreNetworkDiagramCablePositionable[] {
    const positionedCables: _CoreNetworkDiagramCablePositionable[] = [];
    for (const cable of cables) {
      const position = cablesState ? cablesState.find((s) => s.id === cable.id)?.position : undefined;
      switch (cable.cableType) {
        case CoreNetworkDiagramCableType.ETHERNET:
          positionedCables.push(this.getEthernetCablePositioned(cable, position));
          break;
        case CoreNetworkDiagramCableType.FIBER:
          {
            if (
              !cable.layoutDirection ||
              cable.layoutDirection === CoreNetworkDiagramCableLayoutDirection.LEFT_TO_RIGHT
            ) {
              positionedCables.push(this.getFiberCablePositionedLeftToRight(cable, position));
            } else {
              positionedCables.push(this.getFiberCablePositionedRightToLeft(cable, position));
            }
          }
          break;
      }
    }
    return positionedCables;
  }

  private getNodePositioned(
    node: CoreNetworkDiagramNode,
    position?: CoreNetworkDiagramElementPosition,
  ): _CoreNetworkDiagramNodePositionable {
    const nodePosition = position ?? { ...this._availablePosition };
    const nodeWidth =
      this.getPortsRowWidth(this._portSize.width, node.columns, this._elementsGap) + this._nodePadding * 2;
    const nodeHeight =
      this._nodeHeaderHeight +
      this.getPortsTotalRowsHeight(node.ports.length, this._portSize.height, node.columns, this._elementsGap) +
      this._nodePadding * 2;
    // Update available position for adding next elements
    this._availablePosition.x += nodeWidth + this._nodesGap;
    const ports = this.getPortsPositioned(node.ports, nodePosition, node.columns, node.nodeType);
    return {
      id: node.id,
      label: node.label,
      position: nodePosition,
      size: {
        width: nodeWidth,
        height: nodeHeight,
      },
      ports:
        node.nodeType === CoreNetworkDiagramNodeType.SWITCH
          ? signal(ports as _CoreNetworkDiagramSwitchPortPositionable[])
          : signal(ports as _CoreNetworkDiagramPatchPanelPortPositionable[]),
      portsInUse: computed(() => {
        switch (node.nodeType) {
          case CoreNetworkDiagramNodeType.SWITCH:
            return (ports as _CoreNetworkDiagramSwitchPortPositionable[]).filter(
              (p) => p.status !== CoreNetworkSwitchPortStatus.NOT_IN_USE,
            ).length;
          case CoreNetworkDiagramNodeType.PATCH_PANEL:
            return (ports as _CoreNetworkDiagramPatchPanelPortPositionable[]).filter(
              (p) => p.status !== CoreNetworkPatchPanelPortStatus.NOT_IN_USE,
            ).length;
        }
      }),
      nodeType: node.nodeType,
    } as _CoreNetworkDiagramNodePositionable;
  }

  private getPortsPositioned(
    ports: CoreNetworkDiagramPort[],
    parentPosition: CoreNetworkDiagramElementPosition,
    parentColumns: number,
    parentType: CoreNetworkDiagramNodeType,
  ): _CoreNetworkDiagramPortPositionable[] {
    const availablePosition: CoreNetworkDiagramElementPosition = {
      x: parentPosition.x + this._nodePadding,
      y: parentPosition.y + this._nodeHeaderHeight + this._nodePadding,
    };
    const positionedPorts: _CoreNetworkDiagramPortPositionable[] = [];
    let currentPortColumn = 1;
    for (const port of ports) {
      positionedPorts.push({
        ...port,
        position: { ...availablePosition },
        portType:
          parentType === CoreNetworkDiagramNodeType.SWITCH
            ? CoreNetworkDiagramPortType.SWITCH_PORT
            : CoreNetworkDiagramPortType.PATCH_PANEL_PORT,
      } as _CoreNetworkDiagramPortPositionable);
      if (currentPortColumn === parentColumns) {
        availablePosition.x = parentPosition.x + this._nodePadding;
        availablePosition.y += this._portSize.height + this._elementsGap;
        currentPortColumn = 1;
      } else {
        availablePosition.x += this._portSize.width + this._elementsGap;
        currentPortColumn++;
      }
    }
    return positionedPorts;
  }

  private getEthernetCablePositioned(
    cable: CoreNetworkDiagramEthernetCable,
    position?: CoreNetworkDiagramElementPosition,
  ): _CoreNetworkDiagramCablePositionable {
    const cablePosition = position ?? { ...this._availablePosition };
    const cableWidth = this.getTotalCableWidth(cable);
    const cableHeight = this.getTotalCableHeight(cable);
    // Update available position for adding next elements
    this._availablePosition.x += cableWidth + this._nodesGap;
    return {
      id: cable.id,
      label: cable.label,
      position: cablePosition,
      cableType: CoreNetworkDiagramCableType.ETHERNET,
      cssClass: cable.cssClass,
      style: {
        width: cableWidth + 'px',
        height: cableHeight + 'px',
      },
    };
  }

  private getFiberCablePositionedLeftToRight(
    cable: CoreNetworkDiagramFiberCable,
    position?: CoreNetworkDiagramElementPosition,
  ): _CoreNetworkDiagramCablePositionable {
    const cablePosition = position ?? { ...this._availablePosition };
    const cableWidth = this.getTotalCableWidth(cable);
    const cableHeight = this.getTotalCableHeight(cable);
    // Update available position for adding next elements
    this._availablePosition.x += cableWidth + this._nodesGap;
    return {
      id: cable.id,
      label: cable.label,
      position: cablePosition,
      cableType: CoreNetworkDiagramCableType.FIBER,
      tubes: this.getFiberTubesPositionedLeftToRight(cable.tubes, cablePosition),
      cssClass: cable.cssClass,
      direction: CoreNetworkDiagramCableLayoutDirection.LEFT_TO_RIGHT,
      style: {
        width: cableWidth + 'px',
        height: cableHeight + 'px',
      },
    };
  }

  private getFiberTubesPositionedLeftToRight(
    tubes: CoreNetworkDiagramFiberCableTube[],
    cablePosition: CoreNetworkDiagramElementPosition,
  ): _CoreNetworkDiagramFiberCableTubePositionable[] {
    const availablePosition: CoreNetworkDiagramElementPosition = {
      x: cablePosition.x,
      y: cablePosition.y + CORE_NET_DIAGRAM_CABLE_HEADER_HEIGHT,
    };
    const positionedTubes: _CoreNetworkDiagramFiberCableTubePositionable[] = [];
    for (const tube of tubes) {
      const tubeHeight = tube.strands.length * CORE_NET_DIAGRAM_FIBER_STRAND_HEIGHT;
      const tubePosition = { ...availablePosition };
      positionedTubes.push({
        ...tube,
        position: tubePosition,
        strands: this.getFiberTubeStrandsPositionedLeftToRight(tube.strands, tubePosition),
        cssClass: tube.cssClass,
        style: {
          width: CORE_NET_DIAGRAM_FIBER_TUBE_WIDTH + 'px',
          height: tubeHeight + 'px',
        },
      });
      // Update available position for next tube
      availablePosition.y += tubeHeight;
    }
    return positionedTubes;
  }

  private getFiberTubeStrandsPositionedLeftToRight(
    strands: CoreNetworkDiagramFiberStrand[],
    tubePosition: CoreNetworkDiagramElementPosition,
  ): _CoreNetworkDiagramFiberStrandPositionable[] {
    const availablePosition: CoreNetworkDiagramElementPosition = {
      x: tubePosition.x + CORE_NET_DIAGRAM_FIBER_TUBE_WIDTH,
      y: tubePosition.y,
    };
    const positionedStrands: _CoreNetworkDiagramFiberStrandPositionable[] = [];
    for (const strand of strands) {
      const strandPosition = { ...availablePosition };
      positionedStrands.push({
        ...strand,
        position: strandPosition,
        cssClass: strand.cssClass,
        style: {
          width: CORE_NET_DIAGRAM_FIBER_STRAND_WIDTH + 'px',
          height: CORE_NET_DIAGRAM_FIBER_STRAND_HEIGHT + 'px',
        },
      });
      // Update available position for next tube
      availablePosition.y += CORE_NET_DIAGRAM_FIBER_STRAND_HEIGHT;
    }
    return positionedStrands;
  }

  private getFiberCablePositionedRightToLeft(
    cable: CoreNetworkDiagramFiberCable,
    position?: CoreNetworkDiagramElementPosition,
  ): _CoreNetworkDiagramCablePositionable {
    const cableWidth = this.getTotalCableWidth(cable);
    const cableHeight = this.getTotalCableHeight(cable);
    const cablePosition = position ?? { ...this._availablePosition };
    // Update available position for adding next elements
    this._availablePosition.x += cableWidth + this._nodesGap;
    return {
      id: cable.id,
      label: cable.label,
      position: cablePosition,
      cableType: CoreNetworkDiagramCableType.FIBER,
      tubes: this.getFiberTubesPositionedRightToLeft(cable.tubes, cablePosition),
      cssClass: cable.cssClass,
      direction: CoreNetworkDiagramCableLayoutDirection.RIGHT_TO_LEFT,
      style: {
        width: cableWidth + 'px',
        height: cableHeight + 'px',
      },
    };
  }

  private getFiberTubesPositionedRightToLeft(
    tubes: CoreNetworkDiagramFiberCableTube[],
    cablePosition: CoreNetworkDiagramElementPosition,
  ): _CoreNetworkDiagramFiberCableTubePositionable[] {
    const availablePosition: CoreNetworkDiagramElementPosition = {
      x: cablePosition.x + CORE_NET_DIAGRAM_FIBER_STRAND_WIDTH,
      y: cablePosition.y + CORE_NET_DIAGRAM_CABLE_HEADER_HEIGHT,
    };
    const positionedTubes: _CoreNetworkDiagramFiberCableTubePositionable[] = [];
    for (const tube of tubes) {
      const tubeHeight = tube.strands.length * CORE_NET_DIAGRAM_FIBER_STRAND_HEIGHT;
      const tubePosition = { ...availablePosition };
      positionedTubes.push({
        ...tube,
        position: tubePosition,
        strands: this.getFiberTubeStrandsPositionedRightToLeft(tube.strands, tubePosition),
        cssClass: tube.cssClass,
        style: {
          width: CORE_NET_DIAGRAM_FIBER_TUBE_WIDTH + 'px',
          height: tubeHeight + 'px',
        },
      });
      // Update available position for next tube
      availablePosition.y += tubeHeight;
    }
    return positionedTubes;
  }

  private getFiberTubeStrandsPositionedRightToLeft(
    strands: CoreNetworkDiagramFiberStrand[],
    tubePosition: CoreNetworkDiagramElementPosition,
  ): _CoreNetworkDiagramFiberStrandPositionable[] {
    const availablePosition: CoreNetworkDiagramElementPosition = {
      x: tubePosition.x - CORE_NET_DIAGRAM_FIBER_STRAND_WIDTH,
      y: tubePosition.y,
    };
    const positionedStrands: _CoreNetworkDiagramFiberStrandPositionable[] = [];
    for (const strand of strands) {
      const strandPosition = { ...availablePosition };
      positionedStrands.push({
        ...strand,
        position: strandPosition,
        cssClass: strand.cssClass,
        style: {
          width: CORE_NET_DIAGRAM_FIBER_STRAND_WIDTH + 'px',
          height: CORE_NET_DIAGRAM_FIBER_STRAND_HEIGHT + 'px',
        },
      });
      // Update available position for next tube
      availablePosition.y += CORE_NET_DIAGRAM_FIBER_STRAND_HEIGHT;
    }
    return positionedStrands;
  }

  // [ Helpers ]

  private getPortsRowWidth(portWidth: number, columns: number, gap: number): number {
    return portWidth * columns + (columns - 1) * gap;
  }

  private getPortsTotalRowsHeight(portsCount: number, portHeight: number, columns: number, gap: number): number {
    // Edge case
    if (columns >= portsCount) return portHeight;
    // Calculation
    // 8 ports, 2 cols
    const rowsCount = Math.ceil(portsCount / columns);
    const gaps = (rowsCount - 1) * gap;
    return rowsCount * portHeight + gaps;
  }

  private getTotalCableWidth(cable: CoreNetworkDiagramCable): number {
    switch (cable.cableType) {
      case CoreNetworkDiagramCableType.ETHERNET:
        return CORE_NET_DIAGRAM_ETHERNET_CABLE_WIDTH;
      case CoreNetworkDiagramCableType.FIBER:
        return CORE_NET_DIAGRAM_FIBER_TUBE_WIDTH + CORE_NET_DIAGRAM_FIBER_STRAND_WIDTH;
    }
  }

  private getTotalCableHeight(cable: CoreNetworkDiagramCable): number {
    switch (cable.cableType) {
      case CoreNetworkDiagramCableType.ETHERNET:
        return CORE_NET_DIAGRAM_ETHERNET_CABLE_HEIGHT;
      case CoreNetworkDiagramCableType.FIBER:
        return (
          CORE_NET_DIAGRAM_CABLE_HEADER_HEIGHT +
          cable.tubes.reduce(
            (acc, t) => (acc += t.strands.reduce((acc) => (acc += CORE_NET_DIAGRAM_FIBER_STRAND_HEIGHT), 0)),
            0,
          )
        );
    }
  }
}
