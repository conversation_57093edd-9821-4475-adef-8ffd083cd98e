@if (_showCanvas()) {
  <f-flow
    fDraggable
    (fLoaded)="onLoaded()"
    (fCreateConnection)="onCreateConnection($event)"
    (fDragStarted)="onDragStarted($event)"
    (fDragEnded)="onDragEnded()"
    (click)="unselectAll()"
  >
    <f-line-alignment [fAlignThreshold]="20"></f-line-alignment>
    <f-canvas fZoom>
      <!-- Nodes -->
      @for (node of _nodes(); track node.id) {
        <core-base-node
          fGroup
          [fGroupId]="node.id"
          [(fGroupPosition)]="node.position"
          [node]="node"
          [switchActions]="options()?.actions?.switchActions"
          [switchPortActions]="options()?.actions?.switchPortActions"
          [patchPanelActions]="options()?.actions?.patchPanelActions"
          [patchPanelPortActions]="options()?.actions?.patchPanelPortActions"
          (switchAction)="onSwitchAction(node.id, $event)"
          (switchPortAction)="onSwitchPortAction(node.id, $event)"
          (patchPanelAction)="onPatchPanelAction(node.id, $event)"
          (patchPanelPortAction)="onPatchPanelPortAction(node.id, $event)"
        />
      }

      <!-- Containers -->
      @for (container of _containers(); track container.id) {
        <div
          class="container"
          fGroup
          fDragHandle
          [fGroupId]="container.id"
          [fAutoSizeToFitChildren]="true"
          [fAutoExpandOnChildHit]="true"
          [fIncludePadding]="true"
        >
          <!-- Container Header -->
          @if (container.label) {
            <div class="container-header">{{ container.label }}</div>
          }
          <!-- Nodes -->
          @for (node of container.nodes; track node.id) {
            <core-base-node
              fNode
              [fNodeId]="node.id"
              [fNodeParentId]="container.id"
              [(fNodePosition)]="node.position"
              [node]="node"
              [switchActions]="options()?.actions?.switchActions"
              [switchPortActions]="options()?.actions?.switchPortActions"
              [patchPanelActions]="options()?.actions?.patchPanelActions"
              [patchPanelPortActions]="options()?.actions?.patchPanelPortActions"
              (switchAction)="onSwitchAction(node.id, $event)"
              (switchPortAction)="onSwitchPortAction(node.id, $event)"
              (patchPanelAction)="onPatchPanelAction(node.id, $event)"
              (patchPanelPortAction)="onPatchPanelPortAction(node.id, $event)"
            />
          }
          <!-- Cables -->
          @for (cable of container.cables; track cable.id) {
            @switch (cable.cableType) {
              @case ("FIBER") {
                <core-fiber-cable-node
                  fDragHandle
                  fGroup
                  [fGroupId]="cable.id"
                  [fGroupParentId]="container.id"
                  [(fGroupPosition)]="cable.position"
                  [cable]="cable"
                  [fiberCableActions]="options()?.actions?.fiberCableActions"
                  [fiberTubeActions]="options()?.actions?.fiberTubeActions"
                  [fiberStrandActions]="options()?.actions?.fiberStrandActions"
                  (fiberCableAction)="onFiberCableAction(cable.id, $event)"
                  (fiberTubeAction)="onFiberTubeAction(cable.id, $event)"
                  (fiberStrandAction)="onFiberStrandAction(cable.id, $event)"
                />
              }
              @case ("ETHERNET") {
                <div
                  class="ethernet-cable"
                  fNode
                  fDragHandle
                  #cableRef
                  [ngClass]="cable.cssClass"
                  [ngStyle]="cable.style"
                  [fNodeId]="cable.id"
                  [fNodeParentId]="container.id"
                  fNodeOutput
                  [fOutputId]="cable.id"
                  [fOutputConnectableSide]="'bottom'"
                  fNodeInput
                  [fInputId]="cable.id"
                  [fInputConnectableSide]="'bottom'"
                  [(fNodePosition)]="cable.position"
                >
                  <!-- Cable Header -->
                  <div #cableHeader class="cable-header" [ngStyle]="{ height: _cableHeaderHeight + 'px' }">
                    @if (_ethernetCableMenu()) {
                      <core-icon
                        class="menu-icon"
                        [icon]="_verticalMoreIcon"
                        (click)="onSelectEthernetCable(cable.id, cableHeader)"
                      ></core-icon>
                    }
                    <span>{{ cable.label }}</span>
                  </div>
                  <!-- Ethernet Port -->
                  <button class="ethernet-cable-port" fNode fNodeOutlet>Port</button>
                </div>
              }
            }
          }
        </div>
      }

      <!-- Connections -->
      @for (connection of _connections(); track connection.id; let index = $index) {
        <f-connection
          [fConnectionId]="connection.id"
          [fOutputId]="connection.outputId"
          [fInputId]="connection.inputId"
          [fReassignableStart]="true"
          [class.selected-connection]="_selectedConnectionId() === connection.id"
          (click)="onSelectConnection(connection.id, connectionElementRef); $event.stopPropagation()"
          fBehavior="fixed"
          fType="segment"
        >
          <div
            #connectionElementRef
            fConnectionContent
            [ngClass]="options()?.connectionCssClass"
            class="connection-label"
            [align]="'along'"
            [position]="0.5"
          >
            {{ connection.label }}
          </div>
        </f-connection>
      }

      <!-- Connection for create -->
      <f-connection-for-create></f-connection-for-create>
    </f-canvas>
  </f-flow>
}

<!-- Connections Context Menu -->
@if (_fCanvas() && options()?.actions?.connectionActions) {
  <kendo-contextmenu [target]="_fCanvas()!.hostElement" [filter]="'.selected-connection'">
    <ng-template kendoContextMenuTemplate>
      <div class="context-menu-toolbar">
        @for (action of options()!.actions!.connectionActions; track action.id) {
          @if (action.icon) {
            <core-button [icon]="action.icon" [label]="action.label" (click)="onConnectionAction(action.id)" />
          } @else {
            <core-button [label]="action.label" (click)="onConnectionAction(action.id)" />
          }
        }
      </div>
    </ng-template>
  </kendo-contextmenu>
}

<!-- Ethernet Cable Actions -->
@if (options()?.actions?.ethernetCableActions) {
  <kendo-contextmenu #ethernetCableMenu [filter]="'.cable-header'">
    <ng-template kendoContextMenuTemplate>
      <div class="context-menu-toolbar">
        @for (action of options()?.actions?.ethernetCableActions; track action.id) {
          @if (action.icon) {
            <core-button [icon]="action.icon" [label]="action.label" (click)="onEthernetCableAction(action.id)" />
          } @else {
            <core-button [label]="action.label" (click)="onEthernetCableAction(action.id)" />
          }
        }
      </div>
    </ng-template>
  </kendo-contextmenu>
}
