import { _CoreNetworkDiagramElementSize } from './core-network-diagram.interfaces';

export const CORE_NET_DIAGRAM_PORT_SIZE: _CoreNetworkDiagramElementSize = { width: 40, height: 40 }; // In pixels
export const CORE_NET_DIAGRAM_NODE_CHILDREN_GAP = 10;
export const CORE_NET_DIAGRAM_NODE_PADDING = 10;
export const CORE_NET_DIAGRAM_NODE_HEADER_HEIGHT = 30;
export const CORE_NET_DIAGRAM_NODES_GAP = 100;
export const CORE_NET_DIAGRAM_CABLE_HEADER_HEIGHT = 40;
export const CORE_NET_DIAGRAM_ETHERNET_CABLE_WIDTH = 220;
export const CORE_NET_DIAGRAM_ETHERNET_CABLE_HEIGHT = 75;
export const CORE_NET_DIAGRAM_FIBER_TUBE_WIDTH = 120;
export const CORE_NET_DIAGRAM_FIBER_STRAND_WIDTH = 120;
export const CORE_NET_DIAGRAM_FIBER_STRAND_HEIGHT = 25;
